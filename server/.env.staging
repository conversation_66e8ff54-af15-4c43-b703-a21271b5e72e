# 预发环境配置
NODE_ENV=staging

# 数据库配置
MONGODB_URI=mongodb://*************:27017/qr_management_staging

# JWT 配置
JWT_SECRET=staging-secret-key-2024-change-in-production
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3000

# CORS 配置
CORS_ORIGIN=https://staging.your-domain.com

# 日志级别
LOG_LEVEL=info

# API 文档
SWAGGER_ENABLED=true

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads/staging

# 缓存配置
REDIS_URL=redis://localhost:6379/1

# 邮件配置（如果需要）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=staging-password
