"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const product_service_1 = require("./product.service");
const create_product_dto_1 = require("./dto/create-product.dto");
const update_product_dto_1 = require("./dto/update-product.dto");
const query_product_dto_1 = require("./dto/query-product.dto");
let ProductController = class ProductController {
    constructor(productService) {
        this.productService = productService;
    }
    create(createProductDto) {
        return this.productService.create(createProductDto);
    }
    findAll(queryDto) {
        return this.productService.findAll(queryDto);
    }
    getStats() {
        return this.productService.getStats();
    }
    getBrands() {
        return this.productService.getBrands();
    }
    getCategories() {
        return this.productService.getCategories();
    }
    findOne(id) {
        return this.productService.findOne(id);
    }
    update(id, updateProductDto) {
        return this.productService.update(id, updateProductDto);
    }
    remove(id) {
        return this.productService.remove(id);
    }
    findByProductNumber(productNumber) {
        return this.productService.findByProductNumber(productNumber);
    }
};
exports.ProductController = ProductController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: "创建商品" }),
    (0, swagger_1.ApiResponse)({ status: 201, description: "创建成功" }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "请求参数错误" }),
    (0, swagger_1.ApiResponse)({ status: 409, description: "商品编号冲突" }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_product_dto_1.CreateProductDto]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: "获取商品列表" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "获取成功" }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_product_dto_1.QueryProductDto]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)("stats"),
    (0, swagger_1.ApiOperation)({ summary: "获取商品统计信息" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "获取成功" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)("brands"),
    (0, swagger_1.ApiOperation)({ summary: "获取所有品牌" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "获取成功" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "getBrands", null);
__decorate([
    (0, common_1.Get)("categories"),
    (0, swagger_1.ApiOperation)({ summary: "获取所有分类" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "获取成功" }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "getCategories", null);
__decorate([
    (0, common_1.Get)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "获取商品详情" }),
    (0, swagger_1.ApiParam)({ name: "id", description: "商品ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "获取成功" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "商品不存在" }),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "更新商品" }),
    (0, swagger_1.ApiParam)({ name: "id", description: "商品ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "更新成功" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "商品不存在" }),
    __param(0, (0, common_1.Param)("id")),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_product_dto_1.UpdateProductDto]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(":id"),
    (0, swagger_1.ApiOperation)({ summary: "删除商品" }),
    (0, swagger_1.ApiParam)({ name: "id", description: "商品ID" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "删除成功" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "商品不存在" }),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    __param(0, (0, common_1.Param)("id")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)("number/:productNumber"),
    (0, swagger_1.ApiOperation)({ summary: "根据商品编号获取商品信息" }),
    (0, swagger_1.ApiParam)({ name: "productNumber", description: "商品编号" }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "获取成功" }),
    (0, swagger_1.ApiResponse)({ status: 404, description: "商品不存在" }),
    __param(0, (0, common_1.Param)("productNumber")),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "findByProductNumber", null);
exports.ProductController = ProductController = __decorate([
    (0, swagger_1.ApiTags)("商品管理"),
    (0, common_1.Controller)("products"),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [product_service_1.ProductService])
], ProductController);
//# sourceMappingURL=product.controller.js.map