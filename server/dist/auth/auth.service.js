"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const jwt_1 = require("@nestjs/jwt");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcrypt");
const user_schema_1 = require("./schemas/user.schema");
let AuthService = class AuthService {
    constructor(userModel, jwtService) {
        this.userModel = userModel;
        this.jwtService = jwtService;
    }
    async onModuleInit() {
        await this.createDefaultAdmin();
    }
    async createDefaultAdmin() {
        const existingAdmin = await this.userModel.findOne({ username: 'admin' }).exec();
        if (!existingAdmin) {
            const hashedPassword = await bcrypt.hash('admin', 10);
            const admin = new this.userModel({
                username: 'admin',
                password: hashedPassword,
                role: 'admin',
            });
            await admin.save();
            console.log('✅ 默认管理员账户已创建: admin/admin');
        }
    }
    async validateUser(username, password) {
        const user = await this.userModel.findOne({ username, isActive: true }).exec();
        if (user && await bcrypt.compare(password, user.password)) {
            const { password, ...result } = user.toObject();
            return result;
        }
        return null;
    }
    async login(loginDto) {
        const user = await this.validateUser(loginDto.username, loginDto.password);
        if (!user) {
            throw new common_1.UnauthorizedException('用户名或密码错误');
        }
        await this.userModel.findByIdAndUpdate(user._id, { lastLoginAt: new Date() });
        const payload = { username: user.username, sub: user._id, role: user.role };
        return {
            access_token: this.jwtService.sign(payload),
            user: {
                id: user._id,
                username: user.username,
                role: user.role,
                lastLoginAt: user.lastLoginAt,
            },
        };
    }
    async changePassword(userId, changePasswordDto) {
        const user = await this.userModel.findById(userId).exec();
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.BadRequestException('当前密码错误');
        }
        const isSamePassword = await bcrypt.compare(changePasswordDto.newPassword, user.password);
        if (isSamePassword) {
            throw new common_1.BadRequestException('新密码不能与当前密码相同');
        }
        const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 10);
        await this.userModel.findByIdAndUpdate(userId, {
            password: hashedNewPassword,
            updatedAt: new Date(),
        });
        return { message: '密码更改成功' };
    }
    async getProfile(userId) {
        const user = await this.userModel.findById(userId).select('-password').exec();
        if (!user) {
            throw new common_1.UnauthorizedException('用户不存在');
        }
        return user;
    }
    async validateToken(payload) {
        const user = await this.userModel.findById(payload.sub).select('-password').exec();
        if (!user || !user.isActive) {
            throw new common_1.UnauthorizedException('用户不存在或已被禁用');
        }
        return user;
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        jwt_1.JwtService])
], AuthService);
//# sourceMappingURL=auth.service.js.map