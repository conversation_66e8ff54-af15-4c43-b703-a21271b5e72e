"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GenerateQRCodeDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class GenerateQRCodeDto {
    constructor() {
        this.type = 'standard';
    }
}
exports.GenerateQRCodeDto = GenerateQRCodeDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '商品编号（8位数字）',
        example: '12345678',
        pattern: '^\\d{8}$'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.Matches)(/^\d{8}$/, { message: '商品编号必须是8位数字' }),
    __metadata("design:type", String)
], GenerateQRCodeDto.prototype, "productNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '场景值（可选）',
        example: 'scan_from_admin',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenerateQRCodeDto.prototype, "scene", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: '二维码类型',
        enum: ['standard', 'official'],
        default: 'standard',
        required: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], GenerateQRCodeDto.prototype, "type", void 0);
//# sourceMappingURL=generate-qrcode.dto.js.map