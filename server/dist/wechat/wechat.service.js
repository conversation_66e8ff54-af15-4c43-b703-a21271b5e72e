"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var WechatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const cache_manager_1 = require("@nestjs/cache-manager");
const common_2 = require("@nestjs/common");
const rxjs_1 = require("rxjs");
let WechatService = WechatService_1 = class WechatService {
    constructor(httpService, cacheManager) {
        this.httpService = httpService;
        this.cacheManager = cacheManager;
        this.logger = new common_1.Logger(WechatService_1.name);
        this.tokenKey = "wechat_access_token";
    }
    async getAccessToken() {
        const cachedToken = await this.cacheManager.get(this.tokenKey);
        if (cachedToken) {
            this.logger.log("Using cached access token");
            return cachedToken;
        }
        const appId = process.env.WECHAT_APP_ID;
        const appSecret = process.env.WECHAT_APP_SECRET;
        const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.get(url));
            const token = response.data.access_token;
            const expiresIn = response.data.expires_in;
            await this.cacheManager.set(this.tokenKey, token, expiresIn - 60);
            this.logger.log("New access token obtained and cached");
            return token;
        }
        catch (error) {
            this.logger.error("Failed to get access token", error.stack);
            throw new Error("WECHAT_TOKEN_FAILURE");
        }
    }
    async generateWxacode(params) {
        const accessToken = await this.getAccessToken();
        const url = `https://api.weixin.qq.com/wxa/getwxacode?access_token=${accessToken}`;
        try {
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(url, params, {
                responseType: "arraybuffer",
            }));
            const result = response.data.toString();
            if (result.includes("errcode")) {
                const errorData = JSON.parse(result);
                this.logger.error(`Wechat API error: ${JSON.stringify(errorData)}`);
                throw new Error("WECHAT_API_ERROR");
            }
            return response.data;
        }
        catch (error) {
            this.logger.error("Failed to generate wxacode", error.stack);
            throw new Error("QRCODE_GENERATION_FAILED");
        }
    }
};
exports.WechatService = WechatService;
exports.WechatService = WechatService = WechatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, common_2.Inject)(cache_manager_1.CACHE_MANAGER)),
    __metadata("design:paramtypes", [axios_1.HttpService, Object])
], WechatService);
//# sourceMappingURL=wechat.service.js.map