import { HttpService } from "@nestjs/axios";
import { Cache } from "cache-manager";
export declare class WechatService {
    private readonly httpService;
    private cacheManager;
    private readonly logger;
    private readonly tokenKey;
    constructor(httpService: HttpService, cacheManager: Cache);
    getAccessToken(): Promise<string>;
    generateWxacode(params: {
        path: string;
        width?: number;
        autoColor?: boolean;
        isHyaline?: boolean;
    }): Promise<Buffer>;
}
