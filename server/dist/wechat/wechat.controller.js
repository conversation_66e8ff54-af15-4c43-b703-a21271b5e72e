"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WechatController = void 0;
const common_1 = require("@nestjs/common");
const wechat_service_1 = require("./wechat.service");
const swagger_1 = require("@nestjs/swagger");
let WechatController = class WechatController {
    constructor(wechatService) {
        this.wechatService = wechatService;
    }
    async getQrcode(path, width = 430, autoColor = false, isHyaline = false, res) {
        if (!path) {
            return res.status(400).json({ message: "路径参数不能为空" });
        }
        try {
            const qrcode = await this.wechatService.generateWxacode({
                path,
                width: Number(width),
                autoColor: String(autoColor) === "true",
                isHyaline: String(isHyaline) === "true",
            });
            res.send(qrcode);
        }
        catch (error) {
            if (error.message === "QRCODE_GENERATION_FAILED") {
                return res.status(500).json({ message: "二维码生成失败" });
            }
            res.status(500).json({ message: "服务器内部错误" });
        }
    }
};
exports.WechatController = WechatController;
__decorate([
    (0, common_1.Get)("qrcode"),
    (0, common_1.Header)("Content-Type", "image/png"),
    (0, swagger_1.ApiOperation)({
        summary: "生成小程序二维码",
        description: "返回PNG格式的二维码图片",
    }),
    (0, swagger_1.ApiQuery)({
        name: "path",
        required: true,
        description: "小程序页面路径，例如：pages/index/index",
    }),
    (0, swagger_1.ApiQuery)({
        name: "width",
        required: false,
        description: "二维码宽度，默认430px",
        example: 430,
    }),
    (0, swagger_1.ApiQuery)({
        name: "autoColor",
        required: false,
        description: "是否自动配置线条颜色，默认false",
        example: false,
    }),
    (0, swagger_1.ApiQuery)({
        name: "isHyaline",
        required: false,
        description: "是否透明底色，默认false",
        example: false,
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: "PNG格式图片" }),
    (0, swagger_1.ApiResponse)({ status: 400, description: "参数错误" }),
    (0, swagger_1.ApiResponse)({ status: 500, description: "服务器内部错误" }),
    __param(0, (0, common_1.Query)("path")),
    __param(1, (0, common_1.Query)("width")),
    __param(2, (0, common_1.Query)("autoColor")),
    __param(3, (0, common_1.Query)("isHyaline")),
    __param(4, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object, Object, Object]),
    __metadata("design:returntype", Promise)
], WechatController.prototype, "getQrcode", null);
exports.WechatController = WechatController = __decorate([
    (0, swagger_1.ApiTags)("微信二维码"),
    (0, common_1.Controller)("wechat"),
    __metadata("design:paramtypes", [wechat_service_1.WechatService])
], WechatController);
//# sourceMappingURL=wechat.controller.js.map