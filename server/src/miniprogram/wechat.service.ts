import { Injectable, Logger } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { CACHE_MANAGER } from "@nestjs/cache-manager";
import { Cache } from "cache-manager";
import { Inject } from "@nestjs/common";
import { firstValueFrom } from "rxjs";
import { AxiosResponse } from "axios";

@Injectable()
export class WechatService {
  private readonly logger = new Logger(WechatService.name);
  private readonly tokenKey = "wechat_access_token";

  constructor(
    private readonly httpService: HttpService,
    @Inject(CACHE_MANAGER) private cacheManager: Cache
  ) {}

  async getAccessToken(): Promise<string> {
    const cachedToken = await this.cacheManager.get(this.tokenKey);
    if (cachedToken) {
      this.logger.log("Using cached access token");
      return cachedToken as string;
    }

    const appId = process.env.WECHAT_APP_ID;
    const appSecret = process.env.WECHAT_APP_SECRET;
    const url = `https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=${appId}&secret=${appSecret}`;

    try {
      const response = await firstValueFrom(
        this.httpService.get<{ access_token: string; expires_in: number }>(url)
      );

      const token = response.data.access_token;
      const expiresIn = response.data.expires_in;

      // 缓存token，有效期设置为7200秒
      await this.cacheManager.set(this.tokenKey, token, expiresIn - 60);
      this.logger.log("New access token obtained and cached");

      return token;
    } catch (error) {
      this.logger.error("Failed to get access token", error.stack);
      throw new Error("WECHAT_TOKEN_FAILURE");
    }
  }

  async generateWxacode(params: {
    path: string;
    width?: number;
    autoColor?: boolean;
    isHyaline?: boolean;
  }): Promise<Buffer> {
    const accessToken = await this.getAccessToken();
    const url = `https://api.weixin.qq.com/wxa/getwxacode?access_token=${accessToken}`;

    try {
      const response = await firstValueFrom(
        this.httpService.post(url, params, {
          responseType: "arraybuffer",
        })
      );

      // 检查响应是否是错误
      const result = response.data.toString();
      if (result.includes("errcode")) {
        const errorData = JSON.parse(result);
        this.logger.error(`Wechat API error: ${JSON.stringify(errorData)}`);
        throw new Error("WECHAT_API_ERROR");
      }

      return response.data;
    } catch (error) {
      this.logger.error("Failed to generate wxacode", error.stack);
      throw new Error("QRCODE_GENERATION_FAILED");
    }
  }
}
