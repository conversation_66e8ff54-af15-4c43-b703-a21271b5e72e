import { Controller, Get, Query, <PERSON><PERSON>, Head<PERSON> } from "@nestjs/common";
import { WechatService } from "./wechat.service";
import { Response } from "express";
import { ApiOperation, ApiQuery, ApiResponse, ApiTags } from "@nestjs/swagger";

@ApiTags("微信二维码")
@Controller("wechat")
export class WechatController {
  constructor(private readonly wechatService: WechatService) {}

  @Get("qrcode")
  @Header("Content-Type", "image/png")
  @ApiOperation({
    summary: "生成小程序二维码",
    description: "返回PNG格式的二维码图片",
  })
  @ApiQuery({
    name: "path",
    required: true,
    description: "小程序页面路径，例如：pages/index/index",
  })
  @ApiQuery({
    name: "width",
    required: false,
    description: "二维码宽度，默认430px",
    example: 430,
  })
  @ApiQuery({
    name: "autoColor",
    required: false,
    description: "是否自动配置线条颜色，默认false",
    example: false,
  })
  @ApiQuery({
    name: "isHyaline",
    required: false,
    description: "是否透明底色，默认false",
    example: false,
  })
  @ApiResponse({ status: 200, description: "PNG格式图片" })
  @ApiResponse({ status: 400, description: "参数错误" })
  @ApiResponse({ status: 500, description: "服务器内部错误" })
  async getQrcode(
    @Query("path") path: string,
    @Query("width") width = 430,
    @Query("autoColor") autoColor = false,
    @Query("isHyaline") isHyaline = false,
    @Res() res: Response
  ) {
    if (!path) {
      return res.status(400).json({ message: "路径参数不能为空" });
    }

    try {
      const qrcode = await this.wechatService.generateWxacode({
        path,
        width: Number(width),
        autoColor: String(autoColor) === "true",
        isHyaline: String(isHyaline) === "true",
      });

      res.send(qrcode);
    } catch (error) {
      if (error.message === "QRCODE_GENERATION_FAILED") {
        return res.status(500).json({ message: "二维码生成失败" });
      }
      res.status(500).json({ message: "服务器内部错误" });
    }
  }
}
