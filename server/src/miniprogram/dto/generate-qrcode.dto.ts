import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Matches } from 'class-validator';

export class GenerateQRCodeDto {
  @ApiProperty({ 
    description: '商品编号（8位数字）', 
    example: '12345678',
    pattern: '^\\d{8}$'
  })
  @IsString()
  @Matches(/^\d{8}$/, { message: '商品编号必须是8位数字' })
  productNumber: string;

  @ApiProperty({ 
    description: '场景值（可选）', 
    example: 'scan_from_admin',
    required: false 
  })
  @IsOptional()
  @IsString()
  scene?: string;

  @ApiProperty({ 
    description: '二维码类型', 
    enum: ['standard', 'official'],
    default: 'standard',
    required: false 
  })
  @IsOptional()
  @IsString()
  type?: 'standard' | 'official' = 'standard';
}
