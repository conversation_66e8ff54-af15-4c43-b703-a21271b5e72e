import {
  Controller,
  Post,
  Get,
  Body,
  Query,
  Param,
  UseGuards,
  HttpCode,
  HttpStatus,
} from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from "@nestjs/swagger";
import { JwtAuthGuard } from "../auth/guards/jwt-auth.guard";
import { VerificationService } from "./verification.service";
import {
  VerifyProductDto,
  QueryVerificationDto,
} from "./dto/verify-product.dto";

@ApiTags("商品验证")
@Controller("verification")
export class VerificationController {
  constructor(private readonly verificationService: VerificationService) {}

  @Post("verify")
  @ApiOperation({ summary: "验证商品二维码" })
  @ApiResponse({ status: 200, description: "验证成功" })
  @ApiResponse({ status: 400, description: "请求参数错误或商品状态异常" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  @ApiResponse({ status: 409, description: "商品已被验证" })
  @HttpCode(HttpStatus.OK)
  async verifyProduct(@Body() verifyDto: VerifyProductDto) {
    return this.verificationService.verifyProduct(verifyDto);
  }

  @Get("records")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "获取验证记录列表" })
  @ApiResponse({ status: 200, description: "获取成功" })
  async getVerifications(@Query() queryDto: QueryVerificationDto) {
    return this.verificationService.getVerifications(queryDto);
  }

  @Get("stats")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "获取验证统计信息" })
  @ApiResponse({ status: 200, description: "获取成功" })
  async getVerificationStats() {
    return this.verificationService.getVerificationStats();
  }

  @Get("product/:productNumber")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "根据商品编号获取验证记录" })
  @ApiParam({ name: "productNumber", description: "商品编号" })
  @ApiResponse({ status: 200, description: "获取成功" })
  @ApiResponse({ status: 404, description: "未找到验证记录" })
  async getVerificationByProductNumber(
    @Param("productNumber") productNumber: string
  ) {
    return this.verificationService.getVerificationByProductNumber(
      productNumber
    );
  }

  @Post("batch-verify")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "批量验证商品" })
  @ApiResponse({ status: 200, description: "批量验证完成" })
  @ApiResponse({ status: 400, description: "请求参数错误" })
  @HttpCode(HttpStatus.OK)
  async batchVerifyProducts(
    @Body() body: { verifications: VerifyProductDto[] }
  ) {
    const { verifications } = body;

    if (
      !verifications ||
      !Array.isArray(verifications) ||
      verifications.length === 0
    ) {
      throw new Error("验证数据不能为空");
    }

    if (verifications.length > 100) {
      throw new Error("批量验证数量不能超过100个");
    }

    return this.verificationService.batchVerifyProducts(verifications);
  }

  // 小程序端验证接口（无需认证）
  @Post("wechat/verify")
  @ApiOperation({ summary: "小程序端验证商品二维码" })
  @ApiResponse({ status: 200, description: "验证成功" })
  @ApiResponse({ status: 400, description: "请求参数错误或商品状态异常" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  @ApiResponse({ status: 409, description: "商品已被验证" })
  @HttpCode(HttpStatus.OK)
  async verifyProductFromWechat(@Body() verifyDto: VerifyProductDto) {
    // 小程序端验证，自动设置验证方式为二维码
    return this.verificationService.verifyProduct({
      ...verifyDto,
      verificationType: "qrcode",
    });
  }

  @Get("wechat/check/:productNumber")
  @ApiOperation({ summary: "小程序端检查商品验证状态" })
  @ApiParam({ name: "productNumber", description: "商品编号" })
  @ApiResponse({ status: 200, description: "检查成功" })
  @ApiResponse({ status: 404, description: "商品不存在" })
  async checkProductVerificationStatus(
    @Param("productNumber") productNumber: string
  ) {
    try {
      const verification =
        await this.verificationService.getVerificationByProductNumber(
          productNumber
        );
      return {
        success: true,
        data: {
          isVerified: true,
          verificationTime: verification.verificationTime,
          verifiedBy: verification.phoneNumber,
          productInfo: verification.productId,
        },
      };
    } catch (error) {
      return {
        success: true,
        data: {
          isVerified: false,
          verificationTime: null,
          verifiedBy: null,
          productInfo: null,
        },
      };
    }
  }
}
