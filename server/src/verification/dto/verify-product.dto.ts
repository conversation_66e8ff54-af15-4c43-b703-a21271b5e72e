import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, Matches, IsObject, IsNumber, IsEnum } from 'class-validator';

export class VerifyProductDto {
  @ApiProperty({ 
    description: '商品编号（8位数字）', 
    example: '12345678',
    pattern: '^\\d{8}$'
  })
  @IsString()
  @Matches(/^\d{8}$/, { message: '商品编号必须是8位数字' })
  productNumber: string;

  @ApiProperty({ 
    description: '验证人手机号', 
    example: '13800138000',
    pattern: '^1[3-9]\\d{9}$'
  })
  @IsString()
  @Matches(/^1[3-9]\d{9}$/, { message: '手机号格式不正确' })
  phoneNumber: string;

  @ApiProperty({ 
    description: '验证人姓名（可选）', 
    example: '张三',
    required: false 
  })
  @IsOptional()
  @IsString()
  userName?: string;

  @ApiProperty({ 
    description: '微信OpenID（可选）', 
    example: 'o1234567890abcdef',
    required: false 
  })
  @IsOptional()
  @IsString()
  wechatOpenId?: string;

  @ApiProperty({ 
    description: '微信UnionID（可选）', 
    example: 'u1234567890abcdef',
    required: false 
  })
  @IsOptional()
  @IsString()
  wechatUnionId?: string;

  @ApiProperty({ 
    description: '验证地点（可选）', 
    example: {
      latitude: 39.9042,
      longitude: 116.4074,
      address: '北京市朝阳区'
    },
    required: false 
  })
  @IsOptional()
  @IsObject()
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };

  @ApiProperty({ 
    description: '设备信息（可选）', 
    example: {
      userAgent: 'Mozilla/5.0...',
      ip: '***********',
      platform: 'iOS'
    },
    required: false 
  })
  @IsOptional()
  @IsObject()
  deviceInfo?: {
    userAgent: string;
    ip: string;
    platform?: string;
  };

  @ApiProperty({ 
    description: '验证方式', 
    enum: ['qrcode', 'manual', 'batch'],
    default: 'qrcode',
    required: false 
  })
  @IsOptional()
  @IsEnum(['qrcode', 'manual', 'batch'])
  verificationType?: string = 'qrcode';

  @ApiProperty({ 
    description: '备注（可选）', 
    example: '用户主动验证',
    required: false 
  })
  @IsOptional()
  @IsString()
  remarks?: string;
}

export class QueryVerificationDto {
  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @IsNumber()
  page?: number = 1;

  @ApiProperty({ description: '每页数量', example: 10, required: false })
  @IsOptional()
  @IsNumber()
  limit?: number = 10;

  @ApiProperty({ description: '商品编号搜索', required: false })
  @IsOptional()
  @IsString()
  productNumber?: string;

  @ApiProperty({ description: '手机号搜索', required: false })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiProperty({ description: '验证结果筛选', enum: ['success', 'failed', 'duplicate'], required: false })
  @IsOptional()
  @IsEnum(['success', 'failed', 'duplicate'])
  verificationResult?: string;

  @ApiProperty({ description: '验证方式筛选', enum: ['qrcode', 'manual', 'batch'], required: false })
  @IsOptional()
  @IsEnum(['qrcode', 'manual', 'batch'])
  verificationType?: string;

  @ApiProperty({ description: '排序字段', enum: ['verificationTime', 'createdAt'], default: 'verificationTime', required: false })
  @IsOptional()
  @IsEnum(['verificationTime', 'createdAt'])
  sortBy?: string = 'verificationTime';

  @ApiProperty({ description: '排序方向', enum: ['asc', 'desc'], default: 'desc', required: false })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: string = 'desc';
}
