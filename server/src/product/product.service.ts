import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product, ProductDocument } from './schemas/product.schema';
import { CreateProductDto } from './dto/create-product.dto';
import { UpdateProductDto } from './dto/update-product.dto';
import { QueryProductDto } from './dto/query-product.dto';

@Injectable()
export class ProductService {
  constructor(
    @InjectModel(Product.name) private productModel: Model<ProductDocument>,
  ) {}

  // 生成8位随机商品编号
  private generateProductNumber(): string {
    const timestamp = Date.now().toString().slice(-4); // 取时间戳后4位
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0'); // 4位随机数
    return timestamp + random;
  }

  // 创建商品
  async create(createProductDto: CreateProductDto): Promise<Product> {
    let productNumber: string;
    let attempts = 0;
    const maxAttempts = 10;

    // 确保商品编号唯一
    do {
      productNumber = this.generateProductNumber();
      const existing = await this.productModel.findOne({ productNumber }).exec();
      if (!existing) break;
      attempts++;
    } while (attempts < maxAttempts);

    if (attempts >= maxAttempts) {
      throw new ConflictException('无法生成唯一的商品编号，请重试');
    }

    const product = new this.productModel({
      ...createProductDto,
      productNumber,
      packagingDate: new Date(createProductDto.packagingDate),
    });

    return product.save();
  }

  // 获取商品列表（分页）
  async findAll(queryDto: QueryProductDto) {
    const { page, limit, search, status, brand, category, sortBy, sortOrder } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { productNumber: { $regex: search, $options: 'i' } },
        { brand: { $regex: search, $options: 'i' } },
        { category: { $regex: search, $options: 'i' } },
        { batchNumber: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      query.status = status;
    }

    if (brand) {
      query.brand = { $regex: brand, $options: 'i' };
    }

    if (category) {
      query.category = { $regex: category, $options: 'i' };
    }

    // 构建排序条件
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [data, total] = await Promise.all([
      this.productModel.find(query).sort(sort).skip(skip).limit(limit).exec(),
      this.productModel.countDocuments(query).exec(),
    ]);

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // 根据ID获取商品详情
  async findOne(id: string): Promise<Product> {
    const product = await this.productModel.findById(id).exec();
    if (!product) {
      throw new NotFoundException('商品不存在');
    }
    return product;
  }

  // 根据商品编号获取商品
  async findByProductNumber(productNumber: string): Promise<Product> {
    const product = await this.productModel.findOne({ productNumber }).exec();
    if (!product) {
      throw new NotFoundException('商品不存在');
    }
    return product;
  }

  // 更新商品
  async update(id: string, updateProductDto: UpdateProductDto): Promise<Product> {
    const updateData: any = { ...updateProductDto };
    
    if (updateProductDto.packagingDate) {
      updateData.packagingDate = new Date(updateProductDto.packagingDate);
    }

    const product = await this.productModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).exec();

    if (!product) {
      throw new NotFoundException('商品不存在');
    }

    return product;
  }

  // 删除商品
  async remove(id: string): Promise<void> {
    const result = await this.productModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('商品不存在');
    }
  }

  // 获取统计信息
  async getStats() {
    const [total, active, inactive, discontinued, brands, categories] = await Promise.all([
      this.productModel.countDocuments().exec(),
      this.productModel.countDocuments({ status: 'active' }).exec(),
      this.productModel.countDocuments({ status: 'inactive' }).exec(),
      this.productModel.countDocuments({ status: 'discontinued' }).exec(),
      this.productModel.distinct('brand').exec(),
      this.productModel.distinct('category').exec(),
    ]);

    return {
      total,
      active,
      inactive,
      discontinued,
      totalBrands: brands.filter(Boolean).length,
      totalCategories: categories.filter(Boolean).length,
    };
  }

  // 获取所有品牌
  async getBrands(): Promise<string[]> {
    const brands = await this.productModel.distinct('brand').exec();
    return brands.filter(Boolean).sort();
  }

  // 获取所有分类
  async getCategories(): Promise<string[]> {
    const categories = await this.productModel.distinct('category').exec();
    return categories.filter(Boolean).sort();
  }
}
