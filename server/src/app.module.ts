import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { QrCodeModule } from './qr-code/qr-code.module';
import { ProductModule } from './product/product.module';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    
    // MongoDB 连接
    MongooseModule.forRoot(process.env.MONGODB_URI || 'mongodb://localhost:27017/qr_management'),
    
    // 业务模块
    QrCodeModule,
  ],
})
export class AppModule {}
