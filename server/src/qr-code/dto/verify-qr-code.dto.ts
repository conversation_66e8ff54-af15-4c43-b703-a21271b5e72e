import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class VerifyQrCodeDto {
  @ApiProperty({ description: '二维码标识', example: 'qr_123456789' })
  @IsString()
  code: string;

  @ApiProperty({ description: '微信用户ID', required: false, example: 'wx_user_123' })
  @IsOptional()
  @IsString()
  wechatUserId?: string;

  @ApiProperty({ 
    description: '微信用户信息', 
    required: false,
    example: {
      nickname: '张三',
      avatar: 'https://example.com/avatar.jpg',
      openid: 'openid_123'
    }
  })
  @IsOptional()
  wechatUserInfo?: {
    nickname?: string;
    avatar?: string;
    openid?: string;
  };
}
