import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, IsEnum } from 'class-validator';

export class CreateQrCodeDto {
  @ApiProperty({ description: '二维码名称', example: '活动签到码' })
  @IsString()
  name: string;

  @ApiProperty({ description: '二维码内容', example: 'https://example.com/event/123' })
  @IsString()
  content: string;

  @ApiProperty({ description: '二维码描述', required: false, example: '用于活动签到的二维码' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ 
    description: '二维码状态', 
    enum: ['active', 'inactive', 'expired'],
    default: 'active',
    required: false 
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'expired'])
  status?: string;

  @ApiProperty({ description: '过期时间', required: false, example: '2024-12-31T23:59:59.000Z' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}
