import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, Min, IsEnum } from 'class-validator';
import { Transform } from 'class-transformer';

export class QueryQrCodeDto {
  @ApiProperty({ description: '页码', default: 1, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ description: '每页数量', default: 10, required: false })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsNumber()
  @Min(1)
  limit?: number = 10;

  @ApiProperty({ description: '搜索关键词', required: false })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiProperty({ 
    description: '状态筛选', 
    enum: ['active', 'inactive', 'expired'],
    required: false 
  })
  @IsOptional()
  @IsEnum(['active', 'inactive', 'expired'])
  status?: string;

  @ApiProperty({ 
    description: '排序字段', 
    enum: ['createdAt', 'updatedAt', 'scanCount', 'lastScannedAt'],
    default: 'createdAt',
    required: false 
  })
  @IsOptional()
  @IsEnum(['createdAt', 'updatedAt', 'scanCount', 'lastScannedAt'])
  sortBy?: string = 'createdAt';

  @ApiProperty({ 
    description: '排序方向', 
    enum: ['asc', 'desc'],
    default: 'desc',
    required: false 
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: string = 'desc';
}
