import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type QrCodeDocument = QrCode & Document;

@Schema({ timestamps: true })
export class QrCode {
  @Prop({ required: true, unique: true })
  code: string; // 二维码唯一标识

  @Prop({ required: true })
  content: string; // 二维码内容

  @Prop({ required: true })
  name: string; // 二维码名称

  @Prop()
  description?: string; // 二维码描述

  @Prop({ required: true })
  qrImageUrl: string; // 二维码图片URL

  @Prop({ default: 'active', enum: ['active', 'inactive', 'expired'] })
  status: string; // 二维码状态

  @Prop()
  expiresAt?: Date; // 过期时间

  @Prop({ default: 0 })
  scanCount: number; // 扫描次数

  @Prop()
  lastScannedAt?: Date; // 最后扫描时间

  @Prop()
  wechatUserId?: string; // 绑定的微信用户ID

  @Prop()
  wechatUserInfo?: {
    nickname?: string;
    avatar?: string;
    openid?: string;
  }; // 微信用户信息

  @Prop({ default: Date.now })
  createdAt: Date; // 创建时间

  @Prop({ default: Date.now })
  updatedAt: Date; // 更新时间
}

export const QrCodeSchema = SchemaFactory.createForClass(QrCode);
