import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { QrCodeService } from './qr-code.service';
import { CreateQrCodeDto } from './dto/create-qr-code.dto';
import { UpdateQrCodeDto } from './dto/update-qr-code.dto';
import { VerifyQrCodeDto } from './dto/verify-qr-code.dto';
import { QueryQrCodeDto } from './dto/query-qr-code.dto';

@ApiTags('二维码管理')
@Controller('qr-codes')
export class QrCodeController {
  constructor(private readonly qrCodeService: QrCodeService) {}

  @Post()
  @ApiOperation({ summary: '创建二维码' })
  @ApiResponse({ status: 201, description: '创建成功' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  create(@Body() createQrCodeDto: CreateQrCodeDto) {
    return this.qrCodeService.create(createQrCodeDto);
  }

  @Get()
  @ApiOperation({ summary: '获取二维码列表' })
  @ApiResponse({ status: 200, description: '获取成功' })
  findAll(@Query() queryDto: QueryQrCodeDto) {
    return this.qrCodeService.findAll(queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取统计信息' })
  @ApiResponse({ status: 200, description: '获取成功' })
  getStats() {
    return this.qrCodeService.getStats();
  }

  @Get(':id')
  @ApiOperation({ summary: '获取二维码详情' })
  @ApiParam({ name: 'id', description: '二维码ID' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '二维码不存在' })
  findOne(@Param('id') id: string) {
    return this.qrCodeService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新二维码' })
  @ApiParam({ name: 'id', description: '二维码ID' })
  @ApiResponse({ status: 200, description: '更新成功' })
  @ApiResponse({ status: 404, description: '二维码不存在' })
  update(@Param('id') id: string, @Body() updateQrCodeDto: UpdateQrCodeDto) {
    return this.qrCodeService.update(id, updateQrCodeDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除二维码' })
  @ApiParam({ name: 'id', description: '二维码ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '二维码不存在' })
  @HttpCode(HttpStatus.OK)
  remove(@Param('id') id: string) {
    return this.qrCodeService.remove(id);
  }

  @Post('verify')
  @ApiOperation({ summary: '验证二维码' })
  @ApiResponse({ status: 200, description: '验证完成' })
  @ApiResponse({ status: 404, description: '二维码不存在' })
  @HttpCode(HttpStatus.OK)
  verify(@Body() verifyDto: VerifyQrCodeDto) {
    return this.qrCodeService.verify(verifyDto);
  }

  @Get('code/:code')
  @ApiOperation({ summary: '根据code获取二维码信息' })
  @ApiParam({ name: 'code', description: '二维码标识' })
  @ApiResponse({ status: 200, description: '获取成功' })
  @ApiResponse({ status: 404, description: '二维码不存在' })
  findByCode(@Param('code') code: string) {
    return this.qrCodeService.findByCode(code);
  }
}
