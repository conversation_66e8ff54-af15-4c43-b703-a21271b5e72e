import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import * as QRCode from 'qrcode';
import { QrCode, QrCodeDocument } from './schemas/qr-code.schema';
import { CreateQrCodeDto } from './dto/create-qr-code.dto';
import { UpdateQrCodeDto } from './dto/update-qr-code.dto';
import { VerifyQrCodeDto } from './dto/verify-qr-code.dto';
import { QueryQrCodeDto } from './dto/query-qr-code.dto';

@Injectable()
export class QrCodeService {
  constructor(
    @InjectModel(QrCode.name) private qrCodeModel: Model<QrCodeDocument>,
  ) {}

  // 生成唯一的二维码标识
  private generateQrCode(): string {
    return `qr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 生成二维码图片
  private async generateQrImage(content: string): Promise<string> {
    try {
      const qrImageData = await QRCode.toDataURL(content, {
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      return qrImageData;
    } catch (error) {
      throw new BadRequestException('生成二维码图片失败');
    }
  }

  // 创建二维码
  async create(createQrCodeDto: CreateQrCodeDto): Promise<QrCode> {
    const code = this.generateQrCode();
    const qrImageUrl = await this.generateQrImage(createQrCodeDto.content);

    const qrCode = new this.qrCodeModel({
      ...createQrCodeDto,
      code,
      qrImageUrl,
      expiresAt: createQrCodeDto.expiresAt ? new Date(createQrCodeDto.expiresAt) : undefined,
    });

    return qrCode.save();
  }

  // 获取二维码列表（分页）
  async findAll(queryDto: QueryQrCodeDto) {
    const { page, limit, search, status, sortBy, sortOrder } = queryDto;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { code: { $regex: search, $options: 'i' } },
      ];
    }

    if (status) {
      query.status = status;
    }

    // 构建排序条件
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [data, total] = await Promise.all([
      this.qrCodeModel.find(query).sort(sort).skip(skip).limit(limit).exec(),
      this.qrCodeModel.countDocuments(query).exec(),
    ]);

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  // 根据ID获取二维码详情
  async findOne(id: string): Promise<QrCode> {
    const qrCode = await this.qrCodeModel.findById(id).exec();
    if (!qrCode) {
      throw new NotFoundException('二维码不存在');
    }
    return qrCode;
  }

  // 根据code获取二维码
  async findByCode(code: string): Promise<QrCode> {
    const qrCode = await this.qrCodeModel.findOne({ code }).exec();
    if (!qrCode) {
      throw new NotFoundException('二维码不存在');
    }
    return qrCode;
  }

  // 更新二维码
  async update(id: string, updateQrCodeDto: UpdateQrCodeDto): Promise<QrCode> {
    const updateData: any = { ...updateQrCodeDto };
    
    if (updateQrCodeDto.expiresAt) {
      updateData.expiresAt = new Date(updateQrCodeDto.expiresAt);
    }

    // 如果更新了内容，需要重新生成二维码图片
    if (updateQrCodeDto.content) {
      updateData.qrImageUrl = await this.generateQrImage(updateQrCodeDto.content);
    }

    const qrCode = await this.qrCodeModel.findByIdAndUpdate(
      id,
      updateData,
      { new: true }
    ).exec();

    if (!qrCode) {
      throw new NotFoundException('二维码不存在');
    }

    return qrCode;
  }

  // 删除二维码
  async remove(id: string): Promise<void> {
    const result = await this.qrCodeModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException('二维码不存在');
    }
  }

  // 验证二维码并记录扫描
  async verify(verifyDto: VerifyQrCodeDto): Promise<{ success: boolean; message: string; data?: any }> {
    const qrCode = await this.findByCode(verifyDto.code);

    // 检查二维码状态
    if (qrCode.status !== 'active') {
      return { success: false, message: '二维码已失效' };
    }

    // 检查是否过期
    if (qrCode.expiresAt && new Date() > qrCode.expiresAt) {
      // 自动更新状态为过期
      await this.qrCodeModel.findByIdAndUpdate(qrCode._id, { status: 'expired' });
      return { success: false, message: '二维码已过期' };
    }

    // 更新扫描信息
    const updateData: any = {
      $inc: { scanCount: 1 },
      lastScannedAt: new Date(),
    };

    // 如果提供了微信用户信息，则绑定
    if (verifyDto.wechatUserId) {
      updateData.wechatUserId = verifyDto.wechatUserId;
    }
    if (verifyDto.wechatUserInfo) {
      updateData.wechatUserInfo = verifyDto.wechatUserInfo;
    }

    await this.qrCodeModel.findByIdAndUpdate(qrCode._id, updateData);

    return {
      success: true,
      message: '验证成功',
      data: {
        name: qrCode.name,
        content: qrCode.content,
        description: qrCode.description,
      }
    };
  }

  // 获取统计信息
  async getStats() {
    const [total, active, inactive, expired, totalScans] = await Promise.all([
      this.qrCodeModel.countDocuments().exec(),
      this.qrCodeModel.countDocuments({ status: 'active' }).exec(),
      this.qrCodeModel.countDocuments({ status: 'inactive' }).exec(),
      this.qrCodeModel.countDocuments({ status: 'expired' }).exec(),
      this.qrCodeModel.aggregate([
        { $group: { _id: null, totalScans: { $sum: '$scanCount' } } }
      ]).exec(),
    ]);

    return {
      total,
      active,
      inactive,
      expired,
      totalScans: totalScans[0]?.totalScans || 0,
    };
  }
}
