import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { WechatService } from "./wechat.service";
import { WechatController } from "./wechat.controller";
import { ProductModule } from "../product/product.module";
import { HttpModule } from "@nestjs/axios";
import { CacheModule } from "@nestjs/cache-manager";

@Module({
  imports: [
    ProductModule,
    HttpModule,
    CacheModule.register({
      ttl: 600, // 默认缓存时间（秒）
      max: 100,
    }),
  ],
  controllers: [WechatController],
  providers: [WechatService],
  exports: [WechatService],
})
export class WechatModule {}
