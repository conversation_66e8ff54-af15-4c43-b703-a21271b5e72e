# 生产环境配置
NODE_ENV=production

# 数据库配置
MONGODB_URI=mongodb://*************:27017/qr_management_prod

# JWT 配置
JWT_SECRET=CHANGE-THIS-TO-A-SECURE-SECRET-KEY-IN-PRODUCTION
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3000

# CORS 配置
CORS_ORIGIN=https://your-domain.com

# 日志级别
LOG_LEVEL=error

# API 文档（生产环境建议关闭）
SWAGGER_ENABLED=false

# 文件上传配置
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads/prod

# 缓存配置
REDIS_URL=redis://localhost:6379/0

# 邮件配置
SMTP_HOST=smtp.your-domain.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-secure-password

# 监控配置
SENTRY_DSN=https://<EMAIL>/project-id

# 安全配置
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# SSL 配置
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem
