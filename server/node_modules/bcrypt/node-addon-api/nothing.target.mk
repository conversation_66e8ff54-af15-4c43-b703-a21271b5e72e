# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := nothing
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=nothing' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-O0 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-mmacosx-version-min=11.0 \
	-arch \
	x86_64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-std=gnu++17 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Debug :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Debug :=

INCS_Debug := \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/v8/include

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=nothing' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_DARWIN_USE_64_BIT_INODE=1' \
	'-D_LARGEFILE_SOURCE' \
	'-D_FILE_OFFSET_BITS=64' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS'

# Flags passed to all source files.
CFLAGS_Release := \
	-O3 \
	-gdwarf-2 \
	-fno-strict-aliasing \
	-mmacosx-version-min=11.0 \
	-arch \
	x86_64 \
	-Wall \
	-Wendif-labels \
	-W \
	-Wno-unused-parameter

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-std=gnu++17 \
	-stdlib=libc++ \
	-fno-rtti \
	-fno-exceptions

# Flags passed to only ObjC files.
CFLAGS_OBJC_Release :=

# Flags passed to only ObjC++ files.
CFLAGS_OBJCC_Release :=

INCS_Release := \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/include/node \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/src \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/openssl/config \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/openssl/openssl/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/uv/include \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/zlib \
	-I/Users/<USER>/Library/Caches/node-gyp/22.11.0/deps/v8/include

OBJS := \
	$(obj).target/$(TARGET)/../node-addon-api/nothing.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))
$(OBJS): GYP_OBJCFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE)) $(CFLAGS_OBJC_$(BUILDTYPE))
$(OBJS): GYP_OBJCXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE)) $(CFLAGS_OBJCC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-mmacosx-version-min=11.0 \
	-arch \
	x86_64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Debug :=

LDFLAGS_Release := \
	-mmacosx-version-min=11.0 \
	-arch \
	x86_64 \
	-L$(builddir) \
	-stdlib=libc++

LIBTOOLFLAGS_Release :=

LIBS :=

$(builddir)/nothing.a: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(builddir)/nothing.a: LIBS := $(LIBS)
$(builddir)/nothing.a: GYP_LIBTOOLFLAGS := $(LIBTOOLFLAGS_$(BUILDTYPE))
$(builddir)/nothing.a: TOOLSET := $(TOOLSET)
$(builddir)/nothing.a: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,alink)

all_deps += $(builddir)/nothing.a
# Add target alias
.PHONY: nothing
nothing: $(builddir)/nothing.a

# Add target alias to "all" target.
.PHONY: all
all: nothing

# Add target alias
.PHONY: nothing
nothing: $(builddir)/nothing.a

# Short alias for building this static library.
.PHONY: nothing.a
nothing.a: $(builddir)/nothing.a

# Add static library to "all" target.
.PHONY: all
all: $(builddir)/nothing.a

