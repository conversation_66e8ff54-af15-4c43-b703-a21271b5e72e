cmd_/Users/<USER>/github/7/server/node_modules/bcrypt/lib/binding/napi-v3/bcrypt_lib.node := ln -f "Release/bcrypt_lib.node" "/Users/<USER>/github/7/server/node_modules/bcrypt/lib/binding/napi-v3/bcrypt_lib.node" 2>/dev/null || (rm -rf "/Users/<USER>/github/7/server/node_modules/bcrypt/lib/binding/napi-v3/bcrypt_lib.node" && cp -af "Release/bcrypt_lib.node" "/Users/<USER>/github/7/server/node_modules/bcrypt/lib/binding/napi-v3/bcrypt_lib.node")
