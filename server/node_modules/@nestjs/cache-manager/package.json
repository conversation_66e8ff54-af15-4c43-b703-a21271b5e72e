{"name": "@nestjs/cache-manager", "version": "3.0.1", "description": "Nest - modern, fast, powerful node.js web framework (@cache-manager)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/cache-manager#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"{lib,test}/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it", "prepare": "husky"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-angular": "19.8.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.23.0", "@keyv/redis": "4.3.2", "@nestjs/common": "11.0.12", "@nestjs/core": "11.0.12", "@nestjs/platform-express": "11.0.12", "@nestjs/testing": "11.0.12", "@types/jest": "29.5.14", "@types/node": "22.13.13", "@types/supertest": "6.0.3", "cache-manager": "6.4.1", "cacheable": "1.8.9", "eslint": "9.23.0", "eslint-config-prettier": "10.1.1", "eslint-plugin-prettier": "5.2.4", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "keyv": "5.3.2", "lint-staged": "15.5.0", "prettier": "3.5.3", "reflect-metadata": "0.2.2", "release-it": "18.1.2", "rimraf": "6.0.1", "rxjs": "7.8.2", "supertest": "7.1.0", "ts-jest": "29.3.0", "typescript": "5.8.2", "typescript-eslint": "8.28.0"}, "peerDependencies": {"@nestjs/common": "^9.0.0 || ^10.0.0 || ^11.0.0", "@nestjs/core": "^9.0.0 || ^10.0.0 || ^11.0.0", "cache-manager": ">=6", "keyv": ">=5", "rxjs": "^7.8.1"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/cache-manager"}}