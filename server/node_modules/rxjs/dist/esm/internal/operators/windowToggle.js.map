{"version": 3, "file": "windowToggle.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/windowToggle.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAE/C,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AACpD,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,IAAI,EAAE,MAAM,cAAc,CAAC;AACpC,OAAO,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AA+C9C,MAAM,UAAU,YAAY,CAC1B,QAA4B,EAC5B,eAAuD;IAEvD,OAAO,OAAO,CAAC,CAAC,MAAM,EAAE,UAAU,EAAE,EAAE;QACpC,MAAM,OAAO,GAAiB,EAAE,CAAC;QAEjC,MAAM,WAAW,GAAG,CAAC,GAAQ,EAAE,EAAE;YAC/B,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;aAC7B;YACD,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC;QAEF,SAAS,CAAC,QAAQ,CAAC,CAAC,SAAS,CAC3B,wBAAwB,CACtB,UAAU,EACV,CAAC,SAAS,EAAE,EAAE;YACZ,MAAM,MAAM,GAAG,IAAI,OAAO,EAAK,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACrB,MAAM,mBAAmB,GAAG,IAAI,YAAY,EAAE,CAAC;YAC/C,MAAM,WAAW,GAAG,GAAG,EAAE;gBACvB,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBAC3B,MAAM,CAAC,QAAQ,EAAE,CAAC;gBAClB,mBAAmB,CAAC,WAAW,EAAE,CAAC;YACpC,CAAC,CAAC;YAEF,IAAI,eAAgC,CAAC;YACrC,IAAI;gBACF,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,CAAC;aACzD;YAAC,OAAO,GAAG,EAAE;gBACZ,WAAW,CAAC,GAAG,CAAC,CAAC;gBACjB,OAAO;aACR;YAED,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAEvC,mBAAmB,CAAC,GAAG,CAAC,eAAe,CAAC,SAAS,CAAC,wBAAwB,CAAC,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QAC3H,CAAC,EACD,IAAI,CACL,CACF,CAAC;QAGF,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,CAAC,KAAQ,EAAE,EAAE;YAGX,MAAM,WAAW,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YACpC,KAAK,MAAM,MAAM,IAAI,WAAW,EAAE;gBAChC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACpB;QACH,CAAC,EACD,GAAG,EAAE;YAEH,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,QAAQ,EAAE,CAAC;aAC7B;YACD,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,EACD,WAAW,EACX,GAAG,EAAE;YAMH,OAAO,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE;gBACzB,OAAO,CAAC,KAAK,EAAG,CAAC,WAAW,EAAE,CAAC;aAChC;QACH,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}