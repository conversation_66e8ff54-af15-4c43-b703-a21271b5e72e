# 环境配置指南

本项目支持多环境配置，包括开发环境、预发环境和生产环境。

## 环境概览

| 环境 | 用途 | 数据库 | 调试 | API文档 |
|------|------|--------|------|---------|
| development | 本地开发 | qr_management_dev | 开启 | 开启 |
| staging | 预发测试 | qr_management_staging | 部分开启 | 开启 |
| production | 生产部署 | qr_management_prod | 关闭 | 关闭 |

## 快速启动

### 方式一：交互式启动
```bash
./start.sh
# 然后根据提示选择环境
```

### 方式二：直接启动特定环境
```bash
# 开发环境
./start-dev.sh

# 预发环境
./start-staging.sh

# 生产环境
./start-prod.sh
```

## 环境配置文件

### 后端配置文件
- `server/.env.development` - 开发环境配置
- `server/.env.staging` - 预发环境配置
- `server/.env.production` - 生产环境配置

### 前端配置文件
- `admin/.env.development` - 开发环境配置
- `admin/.env.staging` - 预发环境配置
- `admin/.env.production` - 生产环境配置

## 启动命令

### 后端服务
```bash
# 开发环境
cd server && npm run start:dev

# 预发环境
cd server && npm run start:staging

# 生产环境
cd server && npm run start:prod
```

### 前端服务
```bash
# 开发环境
cd admin && npm start

# 预发环境
cd admin && npm run start:staging

# 构建生产版本
cd admin && npm run build
```

## 构建命令

### 后端构建
```bash
# 预发环境构建
cd server && npm run build:staging

# 生产环境构建
cd server && npm run build:prod
```

### 前端构建
```bash
# 开发环境构建
cd admin && npm run build:dev

# 预发环境构建
cd admin && npm run build:staging

# 生产环境构建
cd admin && npm run build
```

## PM2 进程管理

### 启动服务
```bash
# 开发环境
pm2 start ecosystem.config.js --only qr-backend-dev

# 预发环境
pm2 start ecosystem.config.js --only qr-backend-staging

# 生产环境
pm2 start ecosystem.config.js --only qr-backend-prod
```

### 管理命令
```bash
# 查看状态
pm2 status

# 查看日志
pm2 logs qr-backend-prod

# 重启服务
pm2 restart qr-backend-prod

# 停止服务
pm2 stop qr-backend-prod

# 删除服务
pm2 delete qr-backend-prod
```

## 环境变量说明

### 后端环境变量

| 变量名 | 说明 | 开发环境 | 预发环境 | 生产环境 |
|--------|------|----------|----------|----------|
| NODE_ENV | 运行环境 | development | staging | production |
| PORT | 服务端口 | 3000 | 3000 | 3000 |
| MONGODB_URI | 数据库连接 | 本地开发库 | 预发测试库 | 生产数据库 |
| JWT_SECRET | JWT密钥 | 开发密钥 | 预发密钥 | 生产密钥 |
| LOG_LEVEL | 日志级别 | debug | info | error |
| SWAGGER_ENABLED | API文档 | true | true | false |

### 前端环境变量

| 变量名 | 说明 | 开发环境 | 预发环境 | 生产环境 |
|--------|------|----------|----------|----------|
| REACT_APP_ENV | 应用环境 | development | staging | production |
| REACT_APP_API_URL | API地址 | localhost:3000 | 预发API地址 | 生产API地址 |
| REACT_APP_DEBUG | 调试模式 | true | true | false |
| GENERATE_SOURCEMAP | 源码映射 | true | true | false |

## 部署流程

### 预发环境部署
1. 合并代码到 `develop` 分支
2. 执行预发构建：`./start-staging.sh`
3. 部署到预发服务器
4. 执行预发测试

### 生产环境部署
1. 合并代码到 `main` 分支
2. 执行生产构建：`./start-prod.sh`
3. 备份生产数据
4. 部署到生产服务器
5. 执行健康检查

## 安全注意事项

### 开发环境
- 使用默认配置即可
- 可以开启调试和详细日志

### 预发环境
- 使用独立的数据库
- 配置接近生产环境的设置
- 开启必要的监控

### 生产环境
- **必须**修改默认的 JWT_SECRET
- **必须**使用 HTTPS
- **必须**配置防火墙
- **必须**设置日志轮转
- **必须**配置监控告警
- **必须**定期备份数据

## 故障排查

### 常见问题
1. **端口被占用**：修改 PORT 环境变量
2. **数据库连接失败**：检查 MONGODB_URI 配置
3. **跨域问题**：检查 CORS_ORIGIN 配置
4. **构建失败**：检查 Node.js 版本和依赖

### 日志查看
```bash
# PM2 日志
pm2 logs qr-backend-prod

# 应用日志
tail -f logs/prod/combined.log

# 错误日志
tail -f logs/prod/error.log
```

## 监控和维护

### 健康检查
- 后端：`GET /health`
- 数据库连接状态
- 内存使用情况
- CPU 使用情况

### 性能监控
- 响应时间
- 错误率
- 并发用户数
- 数据库查询性能

### 定期维护
- 日志清理
- 数据库优化
- 安全更新
- 性能调优
