# 微信小程序端示例代码

## 小程序页面结构

```
pages/
├── product/
│   ├── detail.js
│   ├── detail.json
│   ├── detail.wxml
│   └── detail.wxss
```

## 商品详情页面代码

### detail.js

```javascript
// pages/product/detail.js
Page({
  data: {
    productInfo: null,
    loading: true,
    error: null,
  },

  onLoad: function (options) {
    console.log("页面参数:", options);

    // 从二维码扫描获取商品编号
    const productNumber = options.productNumber || options.scene;

    if (productNumber) {
      this.getProductInfo(productNumber);
    } else {
      this.setData({
        loading: false,
        error: "缺少商品编号参数",
      });
    }
  },

  // 获取商品信息
  getProductInfo: function (productNumber) {
    const that = this;

    wx.request({
      url: "http://your-api-domain.com/wechat/product/" + productNumber,
      method: "GET",
      header: {
        "content-type": "application/json",
      },
      success: function (res) {
        console.log("API响应:", res.data);

        if (res.data.success) {
          that.setData({
            productInfo: res.data.data,
            loading: false,
          });
        } else {
          that.setData({
            loading: false,
            error: res.data.message || "商品不存在",
          });
        }
      },
      fail: function (err) {
        console.error("请求失败:", err);
        that.setData({
          loading: false,
          error: "网络请求失败，请重试",
        });
      },
    });
  },

  // 重新加载
  onRetry: function () {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const options = currentPage.options;

    this.setData({
      loading: true,
      error: null,
    });

    const productNumber = options.productNumber || options.scene;
    if (productNumber) {
      this.getProductInfo(productNumber);
    }
  },

  // 分享功能
  onShareAppMessage: function () {
    const productInfo = this.data.productInfo;

    return {
      title: productInfo ? `${productInfo.name} - 商品详情` : "商品详情",
      path: `/pages/product/detail?productNumber=${productInfo?.productNumber}`,
      imageUrl: "", // 可以设置分享图片
    };
  },
});
```

### detail.wxml

```xml
<!--pages/product/detail.wxml-->
<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading">
    <view class="loading-icon"></view>
    <text>正在加载商品信息...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error">
    <view class="error-icon">❌</view>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重试</button>
  </view>

  <!-- 商品信息 -->
  <view wx:else class="product-info">
    <view class="product-header">
      <text class="product-name">{{productInfo.name}}</text>
      <view class="product-number">
        <text class="label">商品编号：</text>
        <text class="value">{{productInfo.productNumber}}</text>
      </view>
    </view>

    <view class="product-details">
      <view class="detail-item">
        <text class="label">酒精度：</text>
        <text class="value">{{productInfo.alcoholContent}}%</text>
      </view>

      <view class="detail-item">
        <text class="label">包装日期：</text>
        <text class="value">{{productInfo.packagingDate}}</text>
      </view>

      <view class="detail-item" wx:if="{{productInfo.brand}}">
        <text class="label">品牌：</text>
        <text class="value">{{productInfo.brand}}</text>
      </view>

      <view class="detail-item" wx:if="{{productInfo.category}}">
        <text class="label">分类：</text>
        <text class="value">{{productInfo.category}}</text>
      </view>

      <view class="detail-item" wx:if="{{productInfo.volume}}">
        <text class="label">容量：</text>
        <text class="value">{{productInfo.volume}}ml</text>
      </view>

      <view class="detail-item" wx:if="{{productInfo.batchNumber}}">
        <text class="label">批次号：</text>
        <text class="value">{{productInfo.batchNumber}}</text>
      </view>

      <view class="detail-item" wx:if="{{productInfo.productionLocation}}">
        <text class="label">生产地：</text>
        <text class="value">{{productInfo.productionLocation}}</text>
      </view>
    </view>

    <view class="product-description" wx:if="{{productInfo.description}}">
      <text class="label">商品描述：</text>
      <text class="value">{{productInfo.description}}</text>
    </view>

    <view class="product-status">
      <text class="label">状态：</text>
      <text class="value status-{{productInfo.status}}">
        {{productInfo.status === 'active' ? '正常' : productInfo.status === 'inactive' ? '停用' : '停产'}}
      </text>
    </view>
  </view>
</view>
```

### detail.wxss

```css
/* pages/product/detail.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
  color: #666;
}

.loading-icon {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400rpx;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #ff4d4f;
  margin-bottom: 30rpx;
  text-align: center;
}

.retry-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
}

.product-info {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.product-header {
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 20rpx;
  margin-bottom: 30rpx;
}

.product-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.product-number {
  display: flex;
  align-items: center;
}

.detail-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label {
  color: #666;
  font-size: 28rpx;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  color: #333;
  font-size: 28rpx;
  flex: 1;
}

.product-description {
  background-color: #f9f9f9;
  padding: 20rpx;
  border-radius: 8rpx;
  margin-top: 30rpx;
}

.product-description .label {
  display: block;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.product-description .value {
  line-height: 1.6;
}

.product-status {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.status-active {
  color: #52c41a;
}

.status-inactive {
  color: #faad14;
}

.status-discontinued {
  color: #ff4d4f;
}
```

### detail.json

```json
{
  "navigationBarTitleText": "商品详情",
  "enablePullDownRefresh": true,
  "backgroundColor": "#f5f5f5"
}
```

## 使用说明

1. **扫码跳转**：用户扫描生成的二维码后，会自动跳转到小程序的商品详情页面
2. **参数传递**：商品编号通过 URL 参数 `productNumber` 传递
3. **API 调用**：页面会自动调用后端 API 获取商品详情
4. **错误处理**：包含加载状态、错误状态和重试功能
5. **分享功能**：支持分享商品详情页面

## 注意事项

1. 需要将 API 域名替换为实际的后端服务地址
2. 需要在小程序后台配置服务器域名白名单
3. 建议添加用户授权和登录功能
4. 可以根据实际需求调整页面样式和功能
