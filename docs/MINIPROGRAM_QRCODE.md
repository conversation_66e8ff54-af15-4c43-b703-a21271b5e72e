# 微信小程序二维码功能说明

## 功能概述

本系统支持为每个商品生成微信小程序二维码，用户扫描二维码后可以在小程序中查看商品详细信息。

## 主要功能

### 1. 二维码生成

- 点击商品列表中的"二维码"按钮
- 自动生成包含商品编号的小程序二维码
- 支持预览和下载二维码图片

### 2. 商品信息传递

- 二维码包含商品编号参数
- 小程序端可通过商品编号获取完整商品信息
- 支持场景值追踪

### 3. 小程序端展示

- 自动解析二维码参数
- 调用 API 获取商品详情
- 友好的用户界面展示

## 使用流程

### 管理员操作

1. 登录管理平台
2. 进入商品管理页面
3. 点击任意商品的"二维码"按钮
4. 在弹窗中查看生成的二维码
5. 可以下载二维码用于打印或分享

### 用户操作

1. 使用微信扫描二维码
2. 自动跳转到小程序商品详情页
3. 查看商品的详细信息

## 技术实现

### 后端 API

#### 生成二维码

```
POST /wechat/qrcode/generate
```

**请求参数:**

- `productNumber`: 商品编号（必填）
- `scene`: 场景值（可选）
- `type`: 二维码类型（可选）

#### 获取商品信息

```
GET /wechat/product/:productNumber
```

**返回数据:**

- 商品基本信息
- 包装日期、酒精度等详细信息
- 品牌、分类、容量等扩展信息

### 前端功能

#### 二维码弹窗组件

- 自动生成二维码
- 实时预览
- 下载功能
- 商品信息展示

#### 商品列表集成

- 每行商品都有二维码按钮
- 点击即可生成对应商品的二维码
- 无需额外配置

## 配置说明

### 环境变量

```bash
# 微信小程序配置
wechat_APP_ID=your-wechat-appid
wechat_APP_SECRET=your-wechat-secret
```

### 小程序页面路径

默认跳转到：`pages/product/detail`

可在 `wechatService` 中修改：

```typescript
private readonly pagePath = 'pages/product/detail';
```

## 小程序端开发

### 页面结构

```
pages/
├── product/
│   ├── detail.js      # 商品详情页面逻辑
│   ├── detail.json    # 页面配置
│   ├── detail.wxml    # 页面模板
│   └── detail.wxss    # 页面样式
```

### 关键代码

参考 `docs/wechat-example.md` 中的完整示例代码。

## 扩展功能

### 1. 官方小程序码

如需使用微信官方小程序码 API，需要：

1. 配置有效的 AppID 和 AppSecret
2. 实现 access_token 获取逻辑
3. 调用微信官方 API

### 2. 批量生成

支持批量生成多个商品的二维码：

```
POST /wechat/qrcode/batch-generate
```

### 3. 场景值追踪

可以通过场景值追踪二维码的使用情况：

- 扫描来源
- 扫描时间
- 用户信息

## 注意事项

1. **域名配置**: 需要在小程序后台配置服务器域名白名单
2. **HTTPS**: 生产环境必须使用 HTTPS
3. **参数长度**: 微信小程序场景值最大长度为 32 位
4. **二维码大小**: 建议二维码尺寸不小于 200x200 像素
5. **缓存策略**: 可以考虑对生成的二维码进行缓存

## 故障排查

### 常见问题

1. **二维码生成失败**

   - 检查商品编号格式（必须是 8 位数字）
   - 确认商品在数据库中存在

2. **小程序无法获取商品信息**

   - 检查 API 域名配置
   - 确认网络连接正常
   - 验证商品编号参数传递

3. **二维码无法下载**
   - 检查浏览器权限设置
   - 确认二维码已成功生成

### 调试方法

1. 查看浏览器控制台错误信息
2. 检查网络请求状态
3. 验证 API 响应数据格式
4. 使用小程序开发者工具调试

## 更新日志

- v1.0.0: 基础二维码生成功能
- v1.1.0: 添加批量生成功能
- v1.2.0: 优化用户界面和错误处理
