# API 文档

## 基础信息

- 基础 URL: `http://localhost:3000`
- 内容类型: `application/json`

## 二维码管理 API

### 1. 创建二维码

**POST** `/qr-codes`

**请求体:**

```json
{
  "name": "活动签到码",
  "content": "https://example.com/event/123",
  "description": "用于活动签到的二维码",
  "status": "active",
  "expiresAt": "2024-12-31T23:59:59.000Z"
}
```

**响应:**

```json
{
  "_id": "64f1234567890abcdef12345",
  "code": "qr_1234567890_abcdef123",
  "name": "活动签到码",
  "content": "https://example.com/event/123",
  "description": "用于活动签到的二维码",
  "qrImageUrl": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "status": "active",
  "expiresAt": "2024-12-31T23:59:59.000Z",
  "scanCount": 0,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### 2. 获取二维码列表

**GET** `/qr-codes`

**查询参数:**

- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `search`: 搜索关键词
- `status`: 状态筛选 (active/inactive/expired)
- `sortBy`: 排序字段 (createdAt/updatedAt/scanCount/lastScannedAt)
- `sortOrder`: 排序方向 (asc/desc)

**响应:**

```json
{
  "data": [...],
  "total": 100,
  "page": 1,
  "limit": 10,
  "totalPages": 10
}
```

### 3. 获取二维码详情

**GET** `/qr-codes/:id`

### 4. 更新二维码

**PATCH** `/qr-codes/:id`

### 5. 删除二维码

**DELETE** `/qr-codes/:id`

### 6. 验证二维码

**POST** `/qr-codes/verify`

**请求体:**

```json
{
  "code": "qr_1234567890_abcdef123",
  "wechatUserId": "wx_user_123",
  "wechatUserInfo": {
    "nickname": "张三",
    "avatar": "https://example.com/avatar.jpg",
    "openid": "openid_123"
  }
}
```

**响应:**

```json
{
  "success": true,
  "message": "验证成功",
  "data": {
    "name": "活动签到码",
    "content": "https://example.com/event/123",
    "description": "用于活动签到的二维码"
  }
}
```

### 7. 根据 code 获取二维码

**GET** `/qr-codes/code/:code`

### 8. 获取统计信息

**GET** `/qr-codes/stats`

**响应:**

```json
{
  "total": 100,
  "active": 80,
  "inactive": 15,
  "expired": 5,
  "totalScans": 1500
}
```

## 微信小程序 API

### 1. 生成商品小程序二维码

**POST** `/wechat/qrcode/generate`

**请求体:**

```json
{
  "productNumber": "12345678",
  "scene": "admin_generate",
  "type": "standard"
}
```

**响应:**

```json
{
  "success": true,
  "data": {
    "productNumber": "12345678",
    "productName": "茅台酒",
    "qrCodeImage": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "scene": "admin_generate",
    "generatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. 获取商品信息（小程序端）

**GET** `/wechat/product/:productNumber`

**响应:**

```json
{
  "success": true,
  "data": {
    "productNumber": "12345678",
    "name": "茅台酒",
    "alcoholContent": 53,
    "packagingDate": "2024-01-15T00:00:00.000Z",
    "brand": "茅台",
    "category": "白酒",
    "volume": 500,
    "description": "优质白酒",
    "status": "active",
    "batchNumber": "B20240115001",
    "productionLocation": "贵州茅台镇"
  }
}
```

### 3. 批量生成二维码

**POST** `/wechat/qrcode/batch-generate`

**请求体:**

```json
{
  "productNumbers": ["12345678", "87654321"]
}
```

## 状态码说明

- `200`: 成功
- `201`: 创建成功
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 错误响应格式

```json
{
  "statusCode": 400,
  "message": "请求参数错误",
  "error": "Bad Request"
}
```
