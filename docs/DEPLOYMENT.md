# 部署指南

## 环境要求

- Node.js >= 16.0.0
- MongoDB >= 4.4
- npm >= 8.0.0

## 本地开发环境搭建

### 1. 克隆项目

```bash
git clone <repository-url>
cd qr-management-platform
```

### 2. 安装依赖

```bash
# 安装后端依赖
cd server
npm install

# 安装前端依赖
cd ../admin
npm install
```

### 3. 配置环境变量

```bash
cd ../server
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

### 4. 启动 MongoDB

```bash
# macOS (使用 Homebrew)
brew services start mongodb-community

# Ubuntu
sudo systemctl start mongod

# Windows
net start MongoDB
```

### 5. 启动服务

```bash
# 启动后端服务 (端口 3000)
cd server
npm run start:dev

# 启动前端服务 (端口 3001)
cd admin
npm start
```

### 6. 访问应用

- 前端管理平台: http://localhost:3001
- 后端 API: http://localhost:3000
- API 文档: http://localhost:3000/api

## 生产环境部署

### 1. 构建前端

```bash
cd admin
npm run build
```

### 2. 构建后端

```bash
cd server
npm run build
```

### 3. 配置生产环境变量

```bash
# 在服务器上创建 .env 文件
NODE_ENV=production
PORT=3000
MONGODB_URI=mongodb://*************:27017/qr_management_prod
JWT_SECRET=your-production-secret-key
CORS_ORIGIN=https://your-domain.com
```

### 4. 启动生产服务

```bash
# 使用 PM2 管理进程
npm install -g pm2

# 启动后端服务
cd server
pm2 start dist/main.js --name "qr-backend"

# 配置 Nginx 代理前端静态文件
# 将 admin/build 目录部署到 Nginx
```

### 5. Nginx 配置示例

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        root /path/to/admin/build;
        try_files $uri $uri/ /index.html;
    }

    # 后端 API 代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 直接代理后端路由
    location /qr-codes {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Docker 部署

### 1. 创建 Dockerfile (后端)

```dockerfile
# server/Dockerfile
FROM node:16-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["node", "dist/main"]
```

### 2. 创建 Dockerfile (前端)

```dockerfile
# admin/Dockerfile
FROM node:16-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
```

### 3. Docker Compose

```yaml
version: "3.8"

services:
  mongodb:
    image: mongo:4.4
    container_name: qr-mongodb
    restart: unless-stopped
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db

  backend:
    build: ./server
    container_name: qr-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - MONGODB_URI=mongodb://mongodb:27017/qr_management
      - JWT_SECRET=your-secret-key
    depends_on:
      - mongodb

  frontend:
    build: ./admin
    container_name: qr-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - backend

volumes:
  mongodb_data:
```

## 监控和日志

### 使用 PM2 监控

```bash
# 查看进程状态
pm2 status

# 查看日志
pm2 logs qr-backend

# 重启服务
pm2 restart qr-backend

# 监控面板
pm2 monit
```

### 日志配置

建议配置日志轮转和持久化存储，确保生产环境的稳定性。
