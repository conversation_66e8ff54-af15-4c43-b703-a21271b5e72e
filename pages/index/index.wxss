/* 比例常数：750rpx / 3444px = 0.21777003484320556 */

.page {
  width: 750rpx;
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(178,0,8,1) 0%, rgba(255,145,0,1) 100%);
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.wrap1 {
  width: 100%;
  height: 134rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}

.container2 {
  width: 750rpx;
  height: 108rpx;
  box-sizing: border-box;
  background: rgba(255,255,255,1);
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: center;
  padding-right: 12rpx;
  padding-left: 12rpx;
}

.content2 {
  width: 726rpx;
  height: 103rpx;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.main {
  width: 174rpx;
  height: 64rpx;
  overflow: hidden;
  box-sizing: border-box;
  margin-top: 19rpx;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
}

.section {
  margin-left: 22rpx;
  margin-bottom: 15rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18rpx;
  height: 34rpx;
}

.image27 {
  width: 18rpx;
  height: 34rpx;
}

.text13 {
  margin-left: 120rpx;
  font-family: PingFang SC;
  font-size: 34rpx;
  white-space: nowrap;
  text-align: center;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
  flex-shrink: 0;
}

.main1 {
  margin-top: 19rpx;
  margin-left: 120rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 174rpx;
  height: 64rpx;
}

.section1 {
  width: 174rpx;
  height: 64rpx;
  border-radius: 37rpx;
  border-color: rgba(233,233,233,1);
  border-style: solid;
  border-width: 1rpx;
  box-sizing: border-box;
  background: rgba(255,255,255,1);
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.image30 {
  width: 38rpx;
  height: 14rpx;
  margin-top: 25rpx;
  margin-left: 27rpx;
}

.image29 {
  width: 1rpx;
  height: 40rpx;
  margin-top: 12rpx;
  margin-left: 21rpx;
}

.image28 {
  width: 36rpx;
  height: 36rpx;
  margin-top: 14rpx;
  margin-left: 26rpx;
}

.image31 {
  width: 26rpx;
  height: 26rpx;
}

.wrap {
  width: 100%;
  height: 692rpx;
  margin-top: 26rpx;
  position: relative;
}

.image {
  width: 34rpx;
  height: 34rpx;
  position: absolute;
  top: 31rpx;
  left: 358rpx;
}

.image1 {
  width: 29rpx;
  height: 29rpx;
  position: absolute;
  top: 31rpx;
  left: 581rpx;
}

.image2 {
  width: 86rpx;
  height: 260rpx;
  position: absolute;
  top: 392rpx;
  left: 0rpx;
}

.image3 {
  width: 14rpx;
  height: 14rpx;
  position: absolute;
  top: 29rpx;
  left: 244rpx;
}

.container {
  height: 395rpx;
  border-radius: 18rpx;
  border-color: rgba(255,255,255,1);
  border-style: solid;
  border-width: 3rpx;
  box-sizing: border-box;
  position: absolute;
  top: 44rpx;
  left: 37rpx;
  right: 37rpx;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  flex-direction: row;
}

.content {
  position: relative;
  flex: 1;
}

.text {
  position: absolute;
  top: 6rpx;
  left: 51rpx;
  font-family: PingFang SC;
  font-size: 40rpx;
  white-space: nowrap;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text1 {
  position: absolute;
  top: 83rpx;
  left: 51rpx;
  font-family: PingFang SC;
  font-size: 30rpx;
  white-space: nowrap;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text2 {
  position: absolute;
  top: 150rpx;
  left: 51rpx;
  font-family: PingFang SC;
  font-size: 30rpx;
  white-space: nowrap;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text3 {
  position: absolute;
  top: 217rpx;
  left: 51rpx;
  font-family: PingFang SC;
  font-size: 30rpx;
  white-space: nowrap;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text4 {
  position: absolute;
  top: 284rpx;
  left: 51rpx;
  font-family: PingFang SC;
  font-size: 30rpx;
  white-space: nowrap;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.image4 {
  width: 26rpx;
  height: 26rpx;
  position: absolute;
  top: 63rpx;
  left: 233rpx;
}

.image5 {
  width: 30rpx;
  height: 30rpx;
  position: absolute;
  top: 6rpx;
  left: 95rpx;
}

.image6 {
  width: 24rpx;
  height: 24rpx;
  position: absolute;
  top: 0rpx;
  left: 178rpx;
}

.image7 {
  width: 33rpx;
  height: 33rpx;
  position: absolute;
  top: 52rpx;
  left: 206rpx;
}

.image8 {
  width: 59rpx;
  height: 59rpx;
  position: absolute;
  top: 139rpx;
  left: 0rpx;
}

.content1 {
  position: relative;
  margin-right: 15rpx;
  flex: 1;
}

.text5 {
  position: absolute;
  top: 152rpx;
  left: 141rpx;
  font-family: PingFang SC;
  font-size: 30rpx;
  white-space: nowrap;
  text-align: right;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text6 {
  position: absolute;
  top: 84rpx;
  left: 0rpx;
  font-family: Inter;
  font-size: 28rpx;
  white-space: nowrap;
  text-align: right;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text7 {
  position: absolute;
  top: 219rpx;
  left: 228rpx;
  font-family: Inter;
  font-size: 28rpx;
  white-space: nowrap;
  text-align: right;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.text8 {
  position: absolute;
  top: 284rpx;
  left: 161rpx;
  font-family: Inter;
  font-size: 28rpx;
  white-space: nowrap;
  text-align: right;
  color: rgba(255,255,255,1);
  line-height: 102rpx;
  font-weight: 400;
}

.image9 {
  position: absolute;
  top: 51rpx;
  left: 0rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  width: 18rpx;
  height: 31rpx;
  background-image: url('public/images/images/b449e27b-01f9-499f-af10-bebd29d31b6b.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.image10 {
  width: 12rpx;
  height: 21rpx;
  margin-top: 3rpx;
}

.image11 {
  position: absolute;
  top: 51rpx;
  left: 18rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  width: 18rpx;
  height: 31rpx;
  background-image: url('public/images/images/a1de5e21-2ec1-4f1a-81e6-c46fecddb701.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.image12 {
  width: 12rpx;
  height: 21rpx;
  margin-top: 3rpx;
}

.image13 {
  width: 36rpx;
  height: 14rpx;
  position: absolute;
  top: 40rpx;
  left: 0rpx;
}

.image14 {
  width: 24rpx;
  height: 21rpx;
  position: absolute;
  top: 44rpx;
  left: 6rpx;
}

.text9 {
  position: absolute;
  top: 11rpx;
  left: 56rpx;
  font-family: Inter;
  font-size: 28rpx;
  white-space: nowrap;
  text-align: right;
  color: rgba(23,147,231,1);
  line-height: 102rpx;
  font-weight: 400;
}

.image15 {
  width: 26rpx;
  height: 26rpx;
  position: absolute;
  top: 0rpx;
  left: 3rpx;
}

.image16 {
  width: 26rpx;
  height: 26rpx;
  position: absolute;
  top: 0rpx;
  left: 237rpx;
}

.container1 {
  height: 189rpx;
  border-radius: 18rpx;
  border-color: rgba(213,19,15,1);
  border-style: solid;
  border-width: 3rpx;
  box-sizing: border-box;
  box-shadow: 0rpx 2rpx 14rpx 0rpx rgba(117,0,0,0.88);
  background: rgba(254,221,188,1);
  position: absolute;
  top: 475rpx;
  left: 24rpx;
  right: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  padding: 20rpx 0;
}

.text10 {
  font-family: PingFang SC;
  font-size: 39rpx;
  white-space: nowrap;
  text-align: center;
  color: rgba(169,10,0,1);
  line-height: 65rpx;
  letter-spacing: 3rpx;
  font-weight: 400;
}

.text11 {
  font-family: PingFang SC;
  font-size: 23rpx;
  white-space: nowrap;
  text-align: center;
  color: rgba(0,0,0,1);
  line-height: normal;
  font-weight: 400;
}

.image17 {
  width: 312rpx;
  height: 106rpx;
  position: absolute;
  top: 457rpx;
  left: 219rpx;
}

.bg1 {
  height: 80rpx;
  width: 250rpx;
  position: absolute;
  top: 465rpx;
  left: 250rpx;
}

.bg-img1  {
  height: 80rpx;
  width: 250rpx;  
}

.text12 {
  font-family: PingFang SC;
  font-size: 39rpx;
  white-space: nowrap;
  text-align: center;
  color: rgba(169,10,0,1);
  line-height: 102rpx;
  letter-spacing: 10rpx;
  font-weight: 400;
  position: absolute;
  height: 80rpx;
  width: 250rpx;
}

.image18 {
  width: 38rpx;
  height: 38rpx;
  position: absolute;
  top: 1rpx;
  left: 37rpx;
}

.image19 {
  width: 19rpx;
  height: 23rpx;
  position: absolute;
  top: 28rpx;
  left: 0rpx;
}

.image20 {
  width: 26rpx;
  height: 26rpx;
  position: absolute;
  top: 17rpx;
  left: 80rpx;
}

.image21 {
  width: 26rpx;
  height: 26rpx;
  position: absolute;
  top: 19rpx;
  left: 603rpx;
}

.image22 {
  width: 24rpx;
  height: 24rpx;
  position: absolute;
  top: 39rpx;
  left: 457rpx;
}

.image23 {
  width: 24rpx;
  height: 24rpx;
  position: absolute;
  top: 16rpx;
  left: 552rpx;
}

.image24 {
  width: 17rpx;
  height: 17rpx;
  position: absolute;
  top: 0rpx;
  left: 136rpx;
}

.image25 {
  width: 17rpx;
  height: 17rpx;
  position: absolute;
  top: 0rpx;
  left: 371rpx;
}

.image26 {
  width: 13rpx;
  height: 13rpx;
  position: absolute;
  top: 30rpx;
  left: 421rpx;
}

.wrap2 {
  width: 100%;
  margin-top: 75rpx;
  position: relative;
}

.container3 {
  height: 222rpx;
  border-radius: 18rpx;
  border-color: rgba(213,19,15,1);
  border-style: solid;
  border-width: 3rpx;
  box-sizing: border-box;
  box-shadow: 3rpx 5rpx 16rpx 0rpx rgba(117,0,0,0.88);
  background: rgba(254,221,188,1);
  position: absolute;
  top: 0rpx;
  left: 25rpx;
  right: 25rpx;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}

.content3 {
  width: 488rpx;
  height: 186rpx;
  margin-top: 19rpx;
  margin-left: 81rpx;
  position: relative;
}

.main2 {
  width: 488rpx;
  height: 88rpx;
  box-sizing: border-box;
  background: rgba(213,19,17,1);
  position: absolute;
  top: 83rpx;
  left: 0rpx;
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  border-radius: 18rpx 0 0 18rpx;
}

.image32 {
  width: 26rpx;
  height: 26rpx;
  margin-top: 38rpx;
  margin-left: 94rpx;
}

.image33 {
  width: 26rpx;
  height: 26rpx;
  margin-top: 38rpx;
  margin-left: 330rpx;
}

.text14 {
  position: absolute;
  top: 83rpx;
  left: 128rpx;
  font-family: PingFang SC;
  font-size: 28rpx;
  white-space: nowrap;
  text-align: center;
  color: rgba(254,223,192,1);
  line-height: 102rpx;
  letter-spacing: 8rpx;
  font-weight: 400;
}

.text15 {
  position: absolute;
  top: 0rpx;
  left: 179rpx;
  font-family: PingFang SC;
  font-size: 39rpx;
  white-space: nowrap;
  text-align: center;
  color: rgba(169,10,0,1);
  line-height: 102rpx;
  letter-spacing: 8rpx;
  font-weight: 400;
}

.content4 {
  width: 45rpx;
  height: 138rpx;
  margin-top: 54rpx;
  margin-left: 5rpx;
  position: relative;
}

.main3 {
  width: 45rpx;
  box-sizing: border-box;
  background: rgba(212,20,17,1);
  min-height: 105rpx;
  position: absolute;
  top: 33rpx;
  left: 0rpx;
}

.image34 {
  width: 17rpx;
  height: 34rpx;
  position: absolute;
  top: 4rpx;
  left: 15rpx;
}

.main4 {
  width: 20rpx;
  box-sizing: border-box;
  background: rgba(212,20,17,1);
  min-height: 4rpx;
  position: absolute;
  top: 32rpx;
  left: 13rpx;
}

.main5 {
  width: 20rpx;
  box-sizing: border-box;
  background: rgba(212,20,17,1);
  min-height: 4rpx;
  position: absolute;
  top: 3rpx;
  left: 13rpx;
}

.image35 {
  width: 25rpx;
  height: 5rpx;
  position: absolute;
  top: 0rpx;
  left: 10rpx;
}

.image36 {
  width: 750rpx;
  height: 335rpx;
  position: absolute;
  top: 411rpx;
  left: 0rpx;
}

.image37 {
  width: 746rpx;
  height: 561rpx;
  position: absolute;
  top: 185rpx;
  left: 0rpx;
}

