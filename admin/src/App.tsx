import React from "react";
import { Routes, Route } from "react-router-dom";
import { Layout } from "antd";
import { AuthProvider } from "./contexts/AuthContext";
import ProtectedRoute from "./components/ProtectedRoute";
import Sidebar from "./components/Sidebar";
import Header from "./components/Header";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import ProductList from "./pages/ProductList";
import ProductDetail from "./pages/ProductDetail";
import CreateProduct from "./pages/CreateProduct";
import ChangePassword from "./pages/ChangePassword";

const { Content } = Layout;

function App() {
  return (
    <AuthProvider>
      <Routes>
        {/* 登录页面 */}
        <Route path="/login" element={<Login />} />

        {/* 受保护的路由 */}
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <Layout>
                <Sidebar />
                <Layout>
                  <Header />
                  <Content
                    style={{
                      margin: "24px 16px",
                      padding: 24,
                      background: "#fff",
                    }}
                  >
                    <Routes>
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/products" element={<ProductList />} />
                      <Route
                        path="/products/create"
                        element={<CreateProduct />}
                      />
                      <Route path="/products/:id" element={<ProductDetail />} />
                      <Route
                        path="/change-password"
                        element={<ChangePassword />}
                      />
                    </Routes>
                  </Content>
                </Layout>
              </Layout>
            </ProtectedRoute>
          }
        />
      </Routes>
    </AuthProvider>
  );
}

export default App;
