import React from "react";
import { Routes, Route } from "react-router-dom";
import { Layout } from "antd";
import Sidebar from "./components/Sidebar";
import Header from "./components/Header";
import Dashboard from "./pages/Dashboard";
import QrCodeList from "./pages/QrCodeList";
import QrCodeDetail from "./pages/QrCodeDetail";
import CreateQrCode from "./pages/CreateQrCode";
import ProductList from "./pages/ProductList";
import ProductDetail from "./pages/ProductDetail";
import CreateProduct from "./pages/CreateProduct";

const { Content } = Layout;

function App() {
  return (
    <Layout>
      <Sidebar />
      <Layout>
        <Header />
        <Content
          style={{ margin: "24px 16px", padding: 24, background: "#fff" }}
        >
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/qr-codes" element={<QrCodeList />} />
            <Route path="/qr-codes/create" element={<CreateQrCode />} />
            <Route path="/qr-codes/:id" element={<QrCodeDetail />} />
            <Route path="/products" element={<ProductList />} />
            <Route path="/products/create" element={<CreateProduct />} />
            <Route path="/products/:id" element={<ProductDetail />} />
          </Routes>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
