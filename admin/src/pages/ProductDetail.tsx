import React, { useEffect, useState } from "react";
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  message,
  Spin,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Divider,
  Row,
  Col,
} from "antd";
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import {
  ArrowLeftOutlined,
  EditOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { productApi, Product } from "../services/api";

const { TextArea } = Input;
const { Option } = Select;

const ProductDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const isEdit = searchParams.get("edit") === "true";

  const [data, setData] = useState<Product | null>(null);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(isEdit);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();

  useEffect(() => {
    if (id) {
      fetchData();
    }
  }, [id]);

  useEffect(() => {
    if (data && editMode) {
      form.setFieldsValue({
        name: data.name,
        alcoholContent: data.alcoholContent,
        packagingDate: dayjs(data.packagingDate),
        description: data.description,
        status: data.status,
        brand: data.brand,
        category: data.category,
        volume: data.volume,
        batchNumber: data.batchNumber,
        productionLocation: data.productionLocation,
      });
    }
  }, [data, editMode, form]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await productApi.getById(id!);
      setData(response);
    } catch (error) {
      message.error("获取数据失败");
      navigate("/products");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values: any) => {
    try {
      setSaving(true);
      const updateData = {
        name: values.name,
        alcoholContent: values.alcoholContent,
        packagingDate: values.packagingDate.toISOString(),
        description: values.description,
        status: values.status,
        brand: values.brand,
        category: values.category,
        volume: values.volume,
        batchNumber: values.batchNumber,
        productionLocation: values.productionLocation,
      };

      await productApi.update(id!, updateData);
      message.success("更新成功");
      setEditMode(false);
      fetchData();
    } catch (error) {
      message.error("更新失败");
    } finally {
      setSaving(false);
    }
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: "green", text: "活跃" },
      inactive: { color: "orange", text: "停用" },
      discontinued: { color: "red", text: "停产" },
    };
    const config = statusMap[status as keyof typeof statusMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading) {
    return (
      <div style={{ textAlign: "center", padding: "50px" }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data) {
    return <div>数据不存在</div>;
  }

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate("/products")}
            >
              返回列表
            </Button>
            <Divider type="vertical" />
            <h1 style={{ margin: 0 }}>商品详情</h1>
            {!editMode && (
              <>
                <Divider type="vertical" />
                <Button
                  type="primary"
                  icon={<EditOutlined />}
                  onClick={() => setEditMode(true)}
                >
                  编辑
                </Button>
              </>
            )}
          </Space>
        </div>

        {editMode ? (
          <Form form={form} layout="vertical" onFinish={handleSave}>
            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="商品名称"
                  name="name"
                  rules={[
                    { required: true, message: "请输入商品名称" },
                    { max: 100, message: "名称不能超过100个字符" },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item
                  label="酒精度 (%)"
                  name="alcoholContent"
                  rules={[
                    { required: true, message: "请输入酒精度" },
                    {
                      type: "number",
                      min: 0,
                      max: 100,
                      message: "酒精度应在0-100之间",
                    },
                  ]}
                >
                  <InputNumber
                    style={{ width: "100%" }}
                    min={0}
                    max={100}
                    precision={1}
                    addonAfter="%"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item
                  label="包装日期"
                  name="packagingDate"
                  rules={[{ required: true, message: "请选择包装日期" }]}
                >
                  <DatePicker style={{ width: "100%" }} />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item label="状态" name="status">
                  <Select>
                    <Option value="active">活跃</Option>
                    <Option value="inactive">停用</Option>
                    <Option value="discontinued">停产</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item label="品牌" name="brand">
                  <Input />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item label="分类" name="category">
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col xs={24} md={12}>
                <Form.Item label="容量 (ml)" name="volume">
                  <InputNumber
                    style={{ width: "100%" }}
                    min={1}
                    addonAfter="ml"
                  />
                </Form.Item>
              </Col>

              <Col xs={24} md={12}>
                <Form.Item label="批次号" name="batchNumber">
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label="生产地" name="productionLocation">
              <Input />
            </Form.Item>

            <Form.Item label="商品描述" name="description">
              <TextArea rows={4} />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  icon={<SaveOutlined />}
                >
                  保存
                </Button>
                <Button onClick={() => setEditMode(false)}>取消</Button>
              </Space>
            </Form.Item>
          </Form>
        ) : (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="商品名称" span={2}>
              {data.name}
            </Descriptions.Item>
            <Descriptions.Item label="商品编号">
              <span style={{ fontFamily: "monospace", fontWeight: "bold" }}>
                {data.productNumber}
              </span>
            </Descriptions.Item>
            <Descriptions.Item label="酒精度">
              {data.alcoholContent}%
            </Descriptions.Item>
            <Descriptions.Item label="包装日期">
              {dayjs(data.packagingDate).format("YYYY-MM-DD")}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {getStatusTag(data.status)}
            </Descriptions.Item>
            <Descriptions.Item label="品牌">
              {data.brand || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="分类">
              {data.category || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="容量">
              {data.volume ? `${data.volume}ml` : "-"}
            </Descriptions.Item>
            <Descriptions.Item label="批次号">
              {data.batchNumber || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="生产地">
              {data.productionLocation || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(data.createdAt).format("YYYY-MM-DD HH:mm:ss")}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {dayjs(data.updatedAt).format("YYYY-MM-DD HH:mm:ss")}
            </Descriptions.Item>
            <Descriptions.Item label="商品描述" span={2}>
              {data.description || "-"}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Card>
    </div>
  );
};

export default ProductDetail;
