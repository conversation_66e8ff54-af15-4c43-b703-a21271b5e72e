import React, { useEffect, useState } from "react";
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Popconfirm,
  Card,
  Tooltip,
} from "antd";
import { useNavigate } from "react-router-dom";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
  QrcodeOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { productApi, Product, ProductQueryParams } from "../services/api";

const { Search } = Input;
const { Option } = Select;

const ProductList: React.FC = () => {
  const navigate = useNavigate();
  const [data, setData] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState<ProductQueryParams>({});
  const [brands, setBrands] = useState<string[]>([]);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    fetchData();
    fetchBrands();
    fetchCategories();
  }, [pagination.current, pagination.pageSize, filters]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };
      const response = await productApi.getList(params);
      setData(response.data);
      setPagination((prev) => ({
        ...prev,
        total: response.total,
      }));
    } catch (error) {
      message.error("获取数据失败");
    } finally {
      setLoading(false);
    }
  };

  const fetchBrands = async () => {
    try {
      const brandList = await productApi.getBrands();
      setBrands(brandList);
    } catch (error) {
      console.error("获取品牌列表失败");
    }
  };

  const fetchCategories = async () => {
    try {
      const categoryList = await productApi.getCategories();
      setCategories(categoryList);
    } catch (error) {
      console.error("获取分类列表失败");
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await productApi.delete(id);
      message.success("删除成功");
      fetchData();
    } catch (error) {
      message.error("删除失败");
    }
  };

  const handleSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, search: value }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleStatusFilter = (value: string) => {
    setFilters((prev) => ({ ...prev, status: value || undefined }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleBrandFilter = (value: string) => {
    setFilters((prev) => ({ ...prev, brand: value || undefined }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const handleCategoryFilter = (value: string) => {
    setFilters((prev) => ({ ...prev, category: value || undefined }));
    setPagination((prev) => ({ ...prev, current: 1 }));
  };

  const getStatusTag = (status: string) => {
    const statusMap = {
      active: { color: "green", text: "活跃" },
      inactive: { color: "orange", text: "停用" },
      discontinued: { color: "red", text: "停产" },
    };
    const config = statusMap[status as keyof typeof statusMap];
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const columns = [
    {
      title: "商品编号",
      dataIndex: "productNumber",
      key: "productNumber",
      width: 120,
      fixed: "left" as const,
      render: (productNumber: string) => (
        <span style={{ fontFamily: "monospace", fontWeight: "bold" }}>
          {productNumber}
        </span>
      ),
    },
    {
      title: "商品名称",
      dataIndex: "name",
      key: "name",
      width: 150,
      ellipsis: {
        showTitle: false,
      },
      render: (name: string) => (
        <Tooltip placement="topLeft" title={name}>
          {name}
        </Tooltip>
      ),
    },
    {
      title: "酒精度",
      dataIndex: "alcoholContent",
      key: "alcoholContent",
      width: 100,
      render: (alcoholContent: number) => `${alcoholContent}%`,
      sorter: true,
    },
    {
      title: "包装日期",
      dataIndex: "packagingDate",
      key: "packagingDate",
      width: 120,
      render: (date: string) => dayjs(date).format("YYYY-MM-DD"),
      sorter: true,
    },
    {
      title: "品牌",
      dataIndex: "brand",
      key: "brand",
      width: 100,
      render: (brand: string) => brand || "-",
    },
    {
      title: "分类",
      dataIndex: "category",
      key: "category",
      width: 100,
      render: (category: string) => category || "-",
    },
    {
      title: "容量",
      dataIndex: "volume",
      key: "volume",
      width: 80,
      render: (volume: number) => (volume ? `${volume}ml` : "-"),
    },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      width: 80,
      render: (status: string) => getStatusTag(status),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 120,
      render: (date: string) => dayjs(date).format("MM-DD HH:mm"),
      sorter: true,
    },
    {
      title: "操作",
      key: "action",
      width: 180,
      fixed: "right" as const,
      render: (_: any, record: Product) => (
        <Space size="small" wrap>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/products/${record._id}`)}
          >
            查看
          </Button>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => navigate(`/products/${record._id}?edit=true`)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<QrcodeOutlined />}
            onClick={() => {
              const productNumber = record.productNumber;
              navigator.clipboard.writeText(productNumber).then(
                () => {
                  message.success("复制成功");
                },
                () => {
                  message.error("复制失败");
                }
              );
            }}
          >
            二维码
          </Button>
          <Popconfirm
            title="确定要删除这个商品吗？"
            onConfirm={() => handleDelete(record._id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div
          style={{
            marginBottom: 16,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <h1 style={{ margin: 0 }}>商品管理</h1>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => navigate("/products/create")}
          >
            创建商品
          </Button>
        </div>

        <div
          style={{
            marginBottom: 16,
            display: "flex",
            gap: 16,
            flexWrap: "wrap",
          }}
        >
          <Search
            placeholder="搜索商品名称、编号、品牌等"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            enterButton={<SearchOutlined />}
          />
          <Select
            placeholder="筛选状态"
            allowClear
            style={{ width: 120 }}
            onChange={handleStatusFilter}
          >
            <Option value="active">活跃</Option>
            <Option value="inactive">停用</Option>
            <Option value="discontinued">停产</Option>
          </Select>
          <Select
            placeholder="筛选品牌"
            allowClear
            style={{ width: 120 }}
            onChange={handleBrandFilter}
          >
            {brands.map((brand) => (
              <Option key={brand} value={brand}>
                {brand}
              </Option>
            ))}
          </Select>
          <Select
            placeholder="筛选分类"
            allowClear
            style={{ width: 120 }}
            onChange={handleCategoryFilter}
          >
            {categories.map((category) => (
              <Option key={category} value={category}>
                {category}
              </Option>
            ))}
          </Select>
          <Button icon={<ReloadOutlined />} onClick={fetchData}>
            刷新
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={data}
          rowKey="_id"
          loading={loading}
          scroll={{ x: "max-content" }}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination((prev) => ({
                ...prev,
                current: page,
                pageSize: pageSize || 10,
              }));
            },
          }}
        />
      </Card>
    </div>
  );
};

export default ProductList;
