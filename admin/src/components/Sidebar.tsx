import React from "react";
import { Layout, Menu } from "antd";
import { useNavigate, useLocation } from "react-router-dom";
import {
  DashboardOutlined,
  QrcodeOutlined,
  PlusOutlined,
  ShoppingOutlined,
  AppstoreAddOutlined,
} from "@ant-design/icons";

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems = [
    {
      key: "/",
      icon: <DashboardOutlined />,
      label: "仪表盘",
    },
    {
      key: "/qr-codes",
      icon: <QrcodeOutlined />,
      label: "二维码管理",
    },
    {
      key: "/qr-codes/create",
      icon: <PlusOutlined />,
      label: "创建二维码",
    },
    {
      key: "/products",
      icon: <ShoppingOutlined />,
      label: "商品管理",
    },
    {
      key: "/products/create",
      icon: <AppstoreAddOutlined />,
      label: "创建商品",
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  return (
    <Sider width={200} theme="dark">
      <div
        style={{
          height: 64,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "white",
          fontSize: "18px",
          fontWeight: "bold",
        }}
      >
        二维码管理平台
      </div>
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        theme="dark"
      />
    </Sider>
  );
};

export default Sidebar;
