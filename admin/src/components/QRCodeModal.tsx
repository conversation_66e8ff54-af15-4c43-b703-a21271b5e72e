import React, { useState } from "react";
import {
  Modal,
  Button,
  Space,
  Typography,
  Descriptions,
  message,
  Spin,
  Alert,
  Image,
} from "antd";
import {
  DownloadOutlined,
  QrcodeOutlined,
  CopyOutlined,
  ReloadOutlined,
} from "@ant-design/icons";
import { wechatApi, Product, QRCodeResponse } from "../services/api";

const { Title, Text } = Typography;

interface QRCodeModalProps {
  visible: boolean;
  onClose: () => void;
  product: Product | null;
}

const QRCodeModal: React.FC<QRCodeModalProps> = ({
  visible,
  onClose,
  product,
}) => {
  const [loading, setLoading] = useState(false);
  const [qrCodeData, setQrCodeData] = useState<QRCodeResponse["data"] | null>(
    null
  );

  // 生成二维码
  const generateQRCode = async () => {
    if (!product) return;

    try {
      setLoading(true);
      const response = await wechatApi.generateQRCode({
        productNumber: product.productNumber,
        scene: "admin_generate",
        type: "standard",
      });

      if (response.success) {
        setQrCodeData(response.data);
        message.success("二维码生成成功");
      } else {
        message.error("二维码生成失败");
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || "生成失败，请重试";
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 下载二维码
  const downloadQRCode = () => {
    if (!qrCodeData) return;

    try {
      const link = document.createElement("a");
      link.href = qrCodeData.qrCodeImage;
      link.download = `${product?.name}_${qrCodeData.productNumber}_wechat_qrcode.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      message.success("二维码下载成功");
    } catch (error) {
      message.error("下载失败");
    }
  };

  // 复制商品编号
  const copyProductNumber = () => {
    if (!product) return;

    navigator.clipboard
      .writeText(product.productNumber)
      .then(() => {
        message.success("商品编号已复制到剪贴板");
      })
      .catch(() => {
        message.error("复制失败");
      });
  };

  // 重新生成二维码
  const regenerateQRCode = () => {
    setQrCodeData(null);
    generateQRCode();
  };

  // 当弹窗打开时自动生成二维码
  React.useEffect(() => {
    if (visible && product && !qrCodeData) {
      generateQRCode();
    }
  }, [visible, product]);

  // 当弹窗关闭时清理数据
  React.useEffect(() => {
    if (!visible) {
      setQrCodeData(null);
    }
  }, [visible]);

  return (
    <Modal
      title={
        <Space>
          <QrcodeOutlined />
          <span>微信小程序二维码</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button
          key="regenerate"
          icon={<ReloadOutlined />}
          onClick={regenerateQRCode}
          disabled={loading}
        >
          重新生成
        </Button>,
        <Button
          key="download"
          type="primary"
          icon={<DownloadOutlined />}
          onClick={downloadQRCode}
          disabled={!qrCodeData || loading}
        >
          下载二维码
        </Button>,
      ]}
    >
      {product && (
        <div>
          {/* 商品信息 */}
          <Descriptions
            title="商品信息"
            size="small"
            column={2}
            style={{ marginBottom: 24 }}
          >
            <Descriptions.Item label="商品名称" span={2}>
              {product.name}
            </Descriptions.Item>
            <Descriptions.Item label="商品编号">
              <Space>
                <Text code>{product.productNumber}</Text>
                <Button
                  type="link"
                  size="small"
                  icon={<CopyOutlined />}
                  onClick={copyProductNumber}
                >
                  复制
                </Button>
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="酒精度">
              {product.alcoholContent}%
            </Descriptions.Item>
            <Descriptions.Item label="品牌">
              {product.brand || "-"}
            </Descriptions.Item>
            <Descriptions.Item label="容量">
              {product.volume ? `${product.volume}ml` : "-"}
            </Descriptions.Item>
          </Descriptions>

          {/* 二维码展示区域 */}
          <div style={{ textAlign: "center", marginBottom: 24 }}>
            <Title level={4}>小程序二维码</Title>

            {loading && (
              <div style={{ padding: "60px 0" }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">正在生成二维码...</Text>
                </div>
              </div>
            )}

            {!loading && qrCodeData && (
              <div>
                <div
                  style={{
                    display: "inline-block",
                    padding: 16,
                    border: "1px solid #d9d9d9",
                    borderRadius: 8,
                    backgroundColor: "#fafafa",
                  }}
                >
                  <Image
                    src={qrCodeData.qrCodeImage}
                    alt="小程序二维码"
                    width={200}
                    height={200}
                    style={{ display: "block" }}
                  />
                </div>
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">
                    生成时间:{" "}
                    {new Date(qrCodeData.generatedAt).toLocaleString()}
                  </Text>
                </div>
              </div>
            )}

            {!loading && !qrCodeData && (
              <div style={{ padding: "60px 0" }}>
                <Text type="secondary">二维码生成失败，请重试</Text>
              </div>
            )}
          </div>

          {/* 使用说明 */}
          <Alert
            message="使用说明"
            description={
              <div>
                <p>1. 用户扫描此二维码将跳转到微信小程序</p>
                <p>2. 小程序会自动获取商品编号并显示商品详情</p>
                <p>3. 二维码包含商品编号: {product.productNumber}</p>
                <p>4. 可以下载二维码用于打印或分享</p>
              </div>
            }
            type="info"
            showIcon
          />
        </div>
      )}
    </Modal>
  );
};

export default QRCodeModal;
