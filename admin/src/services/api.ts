import axios from "axios";

// 环境配置
const getApiBaseUrl = () => {
  const env = process.env.REACT_APP_ENV || "development";

  switch (env) {
    case "production":
      return process.env.REACT_APP_API_URL || "https://api.your-domain.com";
    case "staging":
      return (
        process.env.REACT_APP_API_URL || "https://api-staging.your-domain.com"
      );
    case "development":
    default:
      return process.env.REACT_APP_API_URL || "http://localhost:3000";
  }
};

const API_BASE_URL = getApiBaseUrl();

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 根据环境决定是否打印详细错误信息
    if (process.env.REACT_APP_DEBUG === "true") {
      console.error("API Error:", error);
    }

    // 401 未授权，清除 token 并跳转到登录页
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }

    // 生产环境下可以集成错误监控服务
    if (
      process.env.REACT_APP_ENV === "production" &&
      process.env.REACT_APP_SENTRY_DSN
    ) {
      // 这里可以集成 Sentry 等错误监控服务
      // Sentry.captureException(error);
    }

    return Promise.reject(error);
  }
);

// 认证相关接口
export interface LoginData {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  user: {
    id: string;
    username: string;
    role: string;
    lastLoginAt?: string;
  };
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface UserProfile {
  id: string;
  username: string;
  role: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 商品相关接口
export interface Product {
  _id: string;
  name: string;
  productNumber: string;
  alcoholContent: number;
  packagingDate: string;
  description?: string;
  status: "active" | "inactive" | "discontinued";
  brand?: string;
  category?: string;
  volume?: number;
  batchNumber?: string;
  productionLocation?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductData {
  name: string;
  alcoholContent: number;
  packagingDate: string;
  description?: string;
  status?: string;
  brand?: string;
  category?: string;
  volume?: number;
  batchNumber?: string;
  productionLocation?: string;
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  brand?: string;
  category?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface ProductListResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProductStatsResponse {
  total: number;
  active: number;
  inactive: number;
  discontinued: number;
  totalBrands: number;
  totalCategories: number;
}

// 小程序二维码相关接口
export interface GenerateQRCodeData {
  productNumber: string;
  scene?: string;
  type?: "standard" | "official";
}

export interface QRCodeResponse {
  success: boolean;
  data: {
    productNumber: string;
    productName: string;
    qrCodeImage: string;
    scene: string;
    generatedAt: string;
  };
}

// 验证相关接口
export interface VerifyProductData {
  productNumber: string;
  phoneNumber: string;
  userName?: string;
  wechatOpenId?: string;
  wechatUnionId?: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  deviceInfo?: {
    userAgent: string;
    ip: string;
    platform?: string;
  };
  verificationType?: "qrcode" | "manual" | "batch";
  remarks?: string;
}

export interface VerificationRecord {
  _id: string;
  productNumber: string;
  productId: any;
  phoneNumber: string;
  userName?: string;
  wechatOpenId?: string;
  wechatUnionId?: string;
  verificationTime: string;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  deviceInfo?: {
    userAgent: string;
    ip: string;
    platform?: string;
  };
  verificationType: string;
  verificationResult: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
}

export interface VerificationQueryParams {
  page?: number;
  limit?: number;
  productNumber?: string;
  phoneNumber?: string;
  verificationResult?: "success" | "failed" | "duplicate";
  verificationType?: "qrcode" | "manual" | "batch";
  sortBy?: "verificationTime" | "createdAt";
  sortOrder?: "asc" | "desc";
}

export interface VerificationStats {
  total: number;
  success: number;
  failed: number;
  duplicate: number;
  today: number;
  thisMonth: number;
  successRate: string;
}

// 认证 API
export const authApi = {
  // 用户登录
  login: (data: LoginData): Promise<LoginResponse> => {
    return api.post("/auth/login", data);
  },

  // 获取用户信息
  getProfile: (): Promise<UserProfile> => {
    return api.get("/auth/profile");
  },

  // 更改密码
  changePassword: (data: ChangePasswordData): Promise<{ message: string }> => {
    return api.post("/auth/change-password", data);
  },
};

// 商品 API
export const productApi = {
  // 获取商品列表
  getList: (params?: ProductQueryParams): Promise<ProductListResponse> => {
    return api.get("/products", { params });
  },

  // 获取商品详情
  getById: (id: string): Promise<Product> => {
    return api.get(`/products/${id}`);
  },

  // 创建商品
  create: (data: CreateProductData): Promise<Product> => {
    return api.post("/products", data);
  },

  // 更新商品
  update: (id: string, data: Partial<CreateProductData>): Promise<Product> => {
    return api.patch(`/products/${id}`, data);
  },

  // 删除商品
  delete: (id: string): Promise<void> => {
    return api.delete(`/products/${id}`);
  },

  // 获取统计信息
  getStats: (): Promise<ProductStatsResponse> => {
    return api.get("/products/stats");
  },

  // 获取所有品牌
  getBrands: (): Promise<string[]> => {
    return api.get("/products/brands");
  },

  // 获取所有分类
  getCategories: (): Promise<string[]> => {
    return api.get("/products/categories");
  },

  // 根据商品编号获取商品
  getByProductNumber: (productNumber: string): Promise<Product> => {
    return api.get(`/products/number/${productNumber}`);
  },
};

// 小程序二维码 API
export const wechatApi = {
  // 生成商品小程序二维码
  generateQRCode: async (data: GenerateQRCodeData): Promise<QRCodeResponse> => {
    // 构建小程序页面路径，包含商品编号参数
    const path = `pages/product/detail?productNumber=${data.productNumber}`;
    const params = new URLSearchParams({
      path: path,
      width: "430",
      autoColor: "false",
      isHyaline: "false",
    });

    try {
      // 获取二维码图片
      const response = await fetch(`${API_BASE_URL}/wechat/qrcode?${params}`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
      });

      if (!response.ok) {
        throw new Error("二维码生成失败");
      }

      // 将图片转换为 base64
      const blob = await response.blob();
      const reader = new FileReader();

      return new Promise((resolve, reject) => {
        reader.onload = () => {
          resolve({
            success: true,
            data: {
              productNumber: data.productNumber,
              productName: "", // 这里可以通过商品API获取名称
              qrCodeImage: reader.result as string,
              scene: data.scene || "admin_generate",
              generatedAt: new Date().toISOString(),
            },
          });
        };
        reader.onerror = () => reject(new Error("图片转换失败"));
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      throw error;
    }
  },

  // 批量生成二维码
  batchGenerateQRCode: (productNumbers: string[]): Promise<any> => {
    return api.post("/wechat/qrcode/batch-generate", { productNumbers });
  },

  // 通过商品编号获取商品信息（小程序端）
  getProductByNumber: (productNumber: string): Promise<any> => {
    return api.get(`/wechat/product/${productNumber}`);
  },

  // 获取商品详情（包含验证状态）
  getProductDetail: (productNumber: string): Promise<any> => {
    return api.get(`/wechat/product/${productNumber}`);
  },
};

// 验证 API
export const verificationApi = {
  // 验证商品二维码
  verifyProduct: (data: VerifyProductData): Promise<any> => {
    return api.post("/verification/verify", data);
  },

  // 获取验证记录列表
  getVerifications: (params: VerificationQueryParams): Promise<any> => {
    return api.get("/verification/records", { params });
  },

  // 获取验证统计信息
  getStats: (): Promise<VerificationStats> => {
    return api.get("/verification/stats");
  },

  // 根据商品编号获取验证记录
  getVerificationByProductNumber: (productNumber: string): Promise<any> => {
    return api.get(`/verification/product/${productNumber}`);
  },

  // 批量验证商品
  batchVerifyProducts: (verifications: VerifyProductData[]): Promise<any> => {
    return api.post("/verification/batch-verify", { verifications });
  },

  // 小程序端验证商品
  verifyProductFromWechat: (data: VerifyProductData): Promise<any> => {
    return api.post("/verification/wechat/verify", data);
  },

  // 小程序端检查商品验证状态
  checkProductVerificationStatus: (productNumber: string): Promise<any> => {
    return api.get(`/verification/wechat/check/${productNumber}`);
  },
};

export default api;
