import axios from "axios";

// 环境配置
const getApiBaseUrl = () => {
  const env = process.env.REACT_APP_ENV || "development";

  switch (env) {
    case "production":
      return process.env.REACT_APP_API_URL || "https://api.your-domain.com";
    case "staging":
      return (
        process.env.REACT_APP_API_URL || "https://api-staging.your-domain.com"
      );
    case "development":
    default:
      return process.env.REACT_APP_API_URL || "http://localhost:3000";
  }
};

const API_BASE_URL = getApiBaseUrl();

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证 token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 根据环境决定是否打印详细错误信息
    if (process.env.REACT_APP_DEBUG === "true") {
      console.error("API Error:", error);
    }

    // 生产环境下可以集成错误监控服务
    if (
      process.env.REACT_APP_ENV === "production" &&
      process.env.REACT_APP_SENTRY_DSN
    ) {
      // 这里可以集成 Sentry 等错误监控服务
      // Sentry.captureException(error);
    }

    return Promise.reject(error);
  }
);

export interface QrCode {
  _id: string;
  code: string;
  content: string;
  name: string;
  description?: string;
  qrImageUrl: string;
  status: "active" | "inactive" | "expired";
  expiresAt?: string;
  scanCount: number;
  lastScannedAt?: string;
  wechatUserId?: string;
  wechatUserInfo?: {
    nickname?: string;
    avatar?: string;
    openid?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateQrCodeData {
  name: string;
  content: string;
  description?: string;
  status?: string;
  expiresAt?: string;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface QrCodeListResponse {
  data: QrCode[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface StatsResponse {
  total: number;
  active: number;
  inactive: number;
  expired: number;
  totalScans: number;
}

// 商品相关接口
export interface Product {
  _id: string;
  name: string;
  productNumber: string;
  alcoholContent: number;
  packagingDate: string;
  description?: string;
  status: "active" | "inactive" | "discontinued";
  brand?: string;
  category?: string;
  volume?: number;
  batchNumber?: string;
  productionLocation?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductData {
  name: string;
  alcoholContent: number;
  packagingDate: string;
  description?: string;
  status?: string;
  brand?: string;
  category?: string;
  volume?: number;
  batchNumber?: string;
  productionLocation?: string;
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  brand?: string;
  category?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface ProductListResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProductStatsResponse {
  total: number;
  active: number;
  inactive: number;
  discontinued: number;
  totalBrands: number;
  totalCategories: number;
}

// 二维码 API
export const qrCodeApi = {
  // 获取二维码列表
  getList: (params?: QueryParams): Promise<QrCodeListResponse> => {
    return api.get("/qr-codes", { params });
  },

  // 获取二维码详情
  getById: (id: string): Promise<QrCode> => {
    return api.get(`/qr-codes/${id}`);
  },

  // 创建二维码
  create: (data: CreateQrCodeData): Promise<QrCode> => {
    return api.post("/qr-codes", data);
  },

  // 更新二维码
  update: (id: string, data: Partial<CreateQrCodeData>): Promise<QrCode> => {
    return api.patch(`/qr-codes/${id}`, data);
  },

  // 删除二维码
  delete: (id: string): Promise<void> => {
    return api.delete(`/qr-codes/${id}`);
  },

  // 获取统计信息
  getStats: (): Promise<StatsResponse> => {
    return api.get("/qr-codes/stats");
  },

  // 验证二维码
  verify: (code: string, wechatUserId?: string): Promise<any> => {
    return api.post("/qr-codes/verify", { code, wechatUserId });
  },
};

// 商品 API
export const productApi = {
  // 获取商品列表
  getList: (params?: ProductQueryParams): Promise<ProductListResponse> => {
    return api.get("/products", { params });
  },

  // 获取商品详情
  getById: (id: string): Promise<Product> => {
    return api.get(`/products/${id}`);
  },

  // 创建商品
  create: (data: CreateProductData): Promise<Product> => {
    return api.post("/products", data);
  },

  // 更新商品
  update: (id: string, data: Partial<CreateProductData>): Promise<Product> => {
    return api.patch(`/products/${id}`, data);
  },

  // 删除商品
  delete: (id: string): Promise<void> => {
    return api.delete(`/products/${id}`);
  },

  // 获取统计信息
  getStats: (): Promise<ProductStatsResponse> => {
    return api.get("/products/stats");
  },

  // 获取所有品牌
  getBrands: (): Promise<string[]> => {
    return api.get("/products/brands");
  },

  // 获取所有分类
  getCategories: (): Promise<string[]> => {
    return api.get("/products/categories");
  },

  // 根据商品编号获取商品
  getByProductNumber: (productNumber: string): Promise<Product> => {
    return api.get(`/products/number/${productNumber}`);
  },
};

export default api;
