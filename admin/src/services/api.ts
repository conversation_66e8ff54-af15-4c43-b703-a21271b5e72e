import axios from "axios";

// 环境配置
const getApiBaseUrl = () => {
  const env = process.env.REACT_APP_ENV || "development";

  switch (env) {
    case "production":
      return process.env.REACT_APP_API_URL || "https://api.your-domain.com";
    case "staging":
      return (
        process.env.REACT_APP_API_URL || "https://api-staging.your-domain.com"
      );
    case "development":
    default:
      return process.env.REACT_APP_API_URL || "http://localhost:3000";
  }
};

const API_BASE_URL = getApiBaseUrl();

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 根据环境决定是否打印详细错误信息
    if (process.env.REACT_APP_DEBUG === "true") {
      console.error("API Error:", error);
    }

    // 401 未授权，清除 token 并跳转到登录页
    if (error.response?.status === 401) {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      window.location.href = "/login";
    }

    // 生产环境下可以集成错误监控服务
    if (
      process.env.REACT_APP_ENV === "production" &&
      process.env.REACT_APP_SENTRY_DSN
    ) {
      // 这里可以集成 Sentry 等错误监控服务
      // Sentry.captureException(error);
    }

    return Promise.reject(error);
  }
);

// 认证相关接口
export interface LoginData {
  username: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  user: {
    id: string;
    username: string;
    role: string;
    lastLoginAt?: string;
  };
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface UserProfile {
  id: string;
  username: string;
  role: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

// 商品相关接口
export interface Product {
  _id: string;
  name: string;
  productNumber: string;
  alcoholContent: number;
  packagingDate: string;
  description?: string;
  status: "active" | "inactive" | "discontinued";
  brand?: string;
  category?: string;
  volume?: number;
  batchNumber?: string;
  productionLocation?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateProductData {
  name: string;
  alcoholContent: number;
  packagingDate: string;
  description?: string;
  status?: string;
  brand?: string;
  category?: string;
  volume?: number;
  batchNumber?: string;
  productionLocation?: string;
}

export interface ProductQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  brand?: string;
  category?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface ProductListResponse {
  data: Product[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface ProductStatsResponse {
  total: number;
  active: number;
  inactive: number;
  discontinued: number;
  totalBrands: number;
  totalCategories: number;
}

// 小程序二维码相关接口
export interface GenerateQRCodeData {
  productNumber: string;
  scene?: string;
  type?: "standard" | "official";
}

export interface QRCodeResponse {
  success: boolean;
  data: {
    productNumber: string;
    productName: string;
    qrCodeImage: string;
    scene: string;
    generatedAt: string;
  };
}

// 认证 API
export const authApi = {
  // 用户登录
  login: (data: LoginData): Promise<LoginResponse> => {
    return api.post("/auth/login", data);
  },

  // 获取用户信息
  getProfile: (): Promise<UserProfile> => {
    return api.get("/auth/profile");
  },

  // 更改密码
  changePassword: (data: ChangePasswordData): Promise<{ message: string }> => {
    return api.post("/auth/change-password", data);
  },
};

// 商品 API
export const productApi = {
  // 获取商品列表
  getList: (params?: ProductQueryParams): Promise<ProductListResponse> => {
    return api.get("/products", { params });
  },

  // 获取商品详情
  getById: (id: string): Promise<Product> => {
    return api.get(`/products/${id}`);
  },

  // 创建商品
  create: (data: CreateProductData): Promise<Product> => {
    return api.post("/products", data);
  },

  // 更新商品
  update: (id: string, data: Partial<CreateProductData>): Promise<Product> => {
    return api.patch(`/products/${id}`, data);
  },

  // 删除商品
  delete: (id: string): Promise<void> => {
    return api.delete(`/products/${id}`);
  },

  // 获取统计信息
  getStats: (): Promise<ProductStatsResponse> => {
    return api.get("/products/stats");
  },

  // 获取所有品牌
  getBrands: (): Promise<string[]> => {
    return api.get("/products/brands");
  },

  // 获取所有分类
  getCategories: (): Promise<string[]> => {
    return api.get("/products/categories");
  },

  // 根据商品编号获取商品
  getByProductNumber: (productNumber: string): Promise<Product> => {
    return api.get(`/products/number/${productNumber}`);
  },
};

// 小程序二维码 API
export const wechatApi = {
  // 生成商品小程序二维码
  generateQRCode: (data: GenerateQRCodeData): Promise<QRCodeResponse> => {
    return api.post("/wechat/qrcode/generate", data);
  },

  // 批量生成二维码
  batchGenerateQRCode: (productNumbers: string[]): Promise<any> => {
    return api.post("/wechat/qrcode/batch-generate", { productNumbers });
  },

  // 通过商品编号获取商品信息（小程序端）
  getProductByNumber: (productNumber: string): Promise<any> => {
    return api.get(`/wechat/product/${productNumber}`);
  },
};

export default api;
