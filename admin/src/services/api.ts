import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证 token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

export interface QrCode {
  _id: string;
  code: string;
  content: string;
  name: string;
  description?: string;
  qrImageUrl: string;
  status: 'active' | 'inactive' | 'expired';
  expiresAt?: string;
  scanCount: number;
  lastScannedAt?: string;
  wechatUserId?: string;
  wechatUserInfo?: {
    nickname?: string;
    avatar?: string;
    openid?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateQrCodeData {
  name: string;
  content: string;
  description?: string;
  status?: string;
  expiresAt?: string;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: string;
}

export interface QrCodeListResponse {
  data: QrCode[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface StatsResponse {
  total: number;
  active: number;
  inactive: number;
  expired: number;
  totalScans: number;
}

// 二维码 API
export const qrCodeApi = {
  // 获取二维码列表
  getList: (params?: QueryParams): Promise<QrCodeListResponse> => {
    return api.get('/qr-codes', { params });
  },

  // 获取二维码详情
  getById: (id: string): Promise<QrCode> => {
    return api.get(`/qr-codes/${id}`);
  },

  // 创建二维码
  create: (data: CreateQrCodeData): Promise<QrCode> => {
    return api.post('/qr-codes', data);
  },

  // 更新二维码
  update: (id: string, data: Partial<CreateQrCodeData>): Promise<QrCode> => {
    return api.patch(`/qr-codes/${id}`, data);
  },

  // 删除二维码
  delete: (id: string): Promise<void> => {
    return api.delete(`/qr-codes/${id}`);
  },

  // 获取统计信息
  getStats: (): Promise<StatsResponse> => {
    return api.get('/qr-codes/stats');
  },

  // 验证二维码
  verify: (code: string, wechatUserId?: string): Promise<any> => {
    return api.post('/qr-codes/verify', { code, wechatUserId });
  },
};

export default api;
