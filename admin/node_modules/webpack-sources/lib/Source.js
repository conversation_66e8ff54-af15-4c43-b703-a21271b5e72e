/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/

"use strict";

/**
 * @typedef {object} MapOptions
 * @property {boolean=} columns
 * @property {boolean=} module
 */

/**
 * @typedef {object} RawSourceMap
 * @property {number} version
 * @property {string[]} sources
 * @property {string[]} names
 * @property {string=} sourceRoot
 * @property {string[]=} sourcesContent
 * @property {string} mappings
 * @property {string} file
 * @property {string=} debugId
 * @property {number[]=} ignoreList
 */

/** @typedef {string | Buffer} SourceValue */

/**
 * @typedef {object} SourceAndMap
 * @property {SourceValue} source
 * @property {RawSourceMap | null} map
 */

/**
 * @typedef {object} HashLike
 * @property {(data: string | Buffer, inputEncoding?: string) => HashLike} update
 * @property {(encoding?: string) => string | Buffer} digest
 */

class Source {
	/**
	 * @returns {SourceValue} source
	 */
	source() {
		throw new Error("Abstract");
	}

	buffer() {
		const source = this.source();
		if (Buffer.isBuffer(source)) return source;
		return Buffer.from(source, "utf-8");
	}

	size() {
		return this.buffer().length;
	}

	/**
	 * @param {MapOptions=} options map options
	 * @returns {RawSourceMap | null} map
	 */
	map(options) {
		return null;
	}

	/**
	 * @param {MapOptions=} options map options
	 * @returns {SourceAndMap} source and map
	 */
	sourceAndMap(options) {
		return {
			source: this.source(),
			map: this.map(options)
		};
	}

	/**
	 * @param {HashLike} hash hash
	 * @returns {void}
	 */
	updateHash(hash) {
		throw new Error("Abstract");
	}
}

module.exports = Source;
