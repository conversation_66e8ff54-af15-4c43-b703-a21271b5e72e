import e from"postcss-value-parser";import t from"path";import{pathToFileURL as r}from"url";import{parse as o}from"postcss";import{promises as s}from"fs";const n=/(!\s*)?postcss-custom-properties:\s*off\b/i,i=new WeakMap;function a(e){if(!e||!e.nodes)return!1;if(i.has(e))return i.get(e);const t=e.some((e=>u(e,n)));return i.set(e,t),t}const c=/(!\s*)?postcss-custom-properties:\s*ignore\s+next\b/i;function l(e){return!!e&&(!!a(e.parent)||u(e.prev(),c))}function u(e,t){return e&&"comment"===e.type&&t.test(e.text)}function p(t,r){const o=new Map,s=new Map;t.nodes.slice().forEach((e=>{if(a(e))return;const t=w(e)?o:v(e)?s:null;t&&(e.nodes.slice().forEach((e=>{if(e.variable&&!l(e)){const{prop:o}=e;t.set(o,e.value),r.preserve||e.remove()}})),!r.preserve&&d(e)&&e.remove())}));const n=new Map;for(const[t,r]of o.entries())n.set(t,e(r));for(const[t,r]of s.entries())n.set(t,e(r));return n}const f=/^html$/i,m=/^:root$/i,w=e=>"rule"===e.type&&e.selector.split(",").some((e=>f.test(e)))&&Object(e.nodes).length,v=e=>"rule"===e.type&&e.selector.split(",").some((e=>m.test(e)))&&Object(e.nodes).length,d=e=>0===Object(e.nodes).length;function y(t){const r=new Map;if("customProperties"in t)for(const[o,s]of Object.entries(t.customProperties))r.set(o,e(s.toString()));if("custom-properties"in t)for(const[o,s]of Object.entries(t["custom-properties"]))r.set(o,e(s.toString()));return r}async function h(e){let t;try{t=await import(e)}catch(o){t=await import(r(e).href)}return y("default"in t?t.default:t)}async function g(e){const r=(await Promise.all(e.map((async e=>{if(e instanceof Promise?e=await e:e instanceof Function&&(e=await e()),"string"==typeof e){const r=t.resolve(e);return{type:t.extname(r).slice(1).toLowerCase(),from:r}}if("customProperties"in e&&Object(e.customProperties)===e.customProperties)return e;if("custom-properties"in e&&Object(e["custom-properties"])===e["custom-properties"])return e;if("from"in e){const r=t.resolve(e.from);let o=e.type;return o||(o=t.extname(r).slice(1).toLowerCase()),{type:o,from:r}}return Object.keys(e).length,null})))).filter((e=>!!e)),n=await Promise.all(r.map((async e=>{if("type"in e&&"from"in e){if("css"===e.type||"pcss"===e.type)return await async function(e){const t=await s.readFile(e);return p(o(t,{from:e.toString()}),{preserve:!0})}(e.from);if("js"===e.type||"cjs"===e.type)return await h(e.from);if("mjs"===e.type)return await h(e.from);if("json"===e.type)return await async function(e){return y(await j(e))}(e.from);throw new Error("Invalid source type: "+e.type)}return y(e)}))),i=new Map;return n.forEach((e=>{for(const[t,r]of e.entries())i.set(t,r)})),i}const j=async e=>JSON.parse((await s.readFile(e)).toString());function b(e,t){return e.nodes&&e.nodes.length&&e.nodes.slice().forEach((r=>{if($(r)){const[o,...s]=r.nodes.filter((e=>"div"!==e.type)),{value:n}=o,i=e.nodes.indexOf(r);if(t.has(n)){const r=t.get(n).nodes;!function(e,t,r){const o=new Map(t);o.delete(r),b(e,o)}({nodes:r},t,n),i>-1&&e.nodes.splice(i,1,...r)}else s.length&&(i>-1&&e.nodes.splice(i,1,...r.nodes.slice(r.nodes.indexOf(s[0]))),b(e,t))}else b(r,t)})),e.toString()}const O=/^var$/i,$=e=>"function"===e.type&&O.test(e.value)&&Object(e.nodes).length>0;var x=(t,r,o)=>{if(F(t)&&!l(t)){const s=t.value;let n=b(e(s),r);const i=new Set;for(;n.includes("--")&&n.includes("var(")&&!i.has(n);){i.add(n);n=b(e(n),r)}if(n!==s){if(function(e,t){if(!e||!e.parent)return!1;let r=!1;const o=e.parent.index(e);return e.parent.each(((s,n)=>s!==e&&(!(n>=o)&&void("decl"===s.type&&s.prop.toLowerCase()===e.prop.toLowerCase()&&s.value===t&&(r=!0))))),r}(t,n))return void(o.preserve||t.remove());if(o.preserve){const e=t.cloneBefore({value:n});P(e)&&(e.raws.value.value=e.value.replace(S,"$1"),e.raws.value.raw=e.raws.value.value+e.raws.value.raw.replace(S,"$2"))}else t.value=n,P(t)&&(t.raws.value.value=t.value.replace(S,"$1"),t.raws.value.raw=t.raws.value.value+t.raws.value.raw.replace(S,"$2"))}}};const F=e=>!e.variable&&e.value.includes("--")&&e.value.includes("var("),P=e=>"value"in Object(Object(e.raws).value)&&"raw"in e.raws.value&&S.test(e.raws.value.raw),S=/^([\W\w]+)(\s*\/\*[\W\w]+?\*\/)$/;async function k(e,t,r){"css"===t&&await async function(e,t){const r=`:root {\n${Object.keys(t).reduce(((e,r)=>(e.push(`\t${r}: ${t[r]};`),e)),[]).join("\n")}\n}\n`;await s.writeFile(e,r)}(e,r),"scss"===t&&await async function(e,t){const r=`${Object.keys(t).reduce(((e,r)=>{const o=r.replace("--","$");return e.push(`${o}: ${t[r]};`),e}),[]).join("\n")}\n`;await s.writeFile(e,r)}(e,r),"js"===t&&await async function(e,t){const r=`module.exports = {\n\tcustomProperties: {\n${Object.keys(t).reduce(((e,r)=>(e.push(`\t\t'${E(r)}': '${E(t[r])}'`),e)),[]).join(",\n")}\n\t}\n};\n`;await s.writeFile(e,r)}(e,r),"json"===t&&await async function(e,t){const r=`${JSON.stringify({"custom-properties":t},null,"  ")}\n`;await s.writeFile(e,r)}(e,r),"mjs"===t&&await async function(e,t){const r=`export const customProperties = {\n${Object.keys(t).reduce(((e,r)=>(e.push(`\t'${E(r)}': '${E(t[r])}'`),e)),[]).join(",\n")}\n};\n`;await s.writeFile(e,r)}(e,r)}function M(e){const t={};for(const[r,o]of e.entries())t[r]=o.toString();return t}const E=e=>e.replace(/\\([\s\S])|(')/g,"\\$1$2").replace(/\n/g,"\\n").replace(/\r/g,"\\r"),C=e=>{const r=!("preserve"in Object(e))||Boolean(e.preserve),o="overrideImportFromWithRoot"in Object(e)&&Boolean(e.overrideImportFromWithRoot),s="disableDeprecationNotice"in Object(e)&&Boolean(e.disableDeprecationNotice);let n=[];Array.isArray(null==e?void 0:e.importFrom)?n=e.importFrom:null!=e&&e.importFrom&&(n=[e.importFrom]);let i=[];Array.isArray(null==e?void 0:e.exportTo)?i=e.exportTo:null!=e&&e.exportTo&&(i=[e.exportTo]);const a=g(n),c=0===n.length&&0===i.length;return{postcssPlugin:"postcss-custom-properties",prepare(){let e=new Map;return c?{Once:t=>{e=p(t,{preserve:r})},Declaration:t=>{x(t,e,{preserve:r})},OnceExit:()=>{e.clear()}}:{Once:async s=>{const n=(await a).entries(),c=p(s,{preserve:r}).entries();if(o)for(const[t,r]of[...n,...c])e.set(t,r);else for(const[t,r]of[...c,...n])e.set(t,r);await function(e,r){return Promise.all(r.map((async r=>{if(r instanceof Function)return void await r(M(e));if("string"==typeof r){const o=t.resolve(r),s=t.extname(o).slice(1).toLowerCase();return void await k(o,s,M(e))}let o={};if(o="toJSON"in r?r.toJSON(M(e)):M(e),"to"in r){const e=t.resolve(r.to);let s=r.type;return s||(s=t.extname(e).slice(1).toLowerCase()),void await k(e,s,o)}"customProperties"in r?r.customProperties=o:"custom-properties"in r&&(r["custom-properties"]=o)})))}(e,i)},Declaration:t=>{x(t,e,{preserve:r})},OnceExit:(t,{result:r})=>{!s&&(n.length>0||i.length>0)&&t.warn(r,'"importFrom" and "exportTo" will be removed in a future version of postcss-custom-properties.\nWe are looking for insights and anecdotes on how these features are used so that we can design the best alternative.\nPlease let us know if our proposal will work for you.\nVisit the discussion on github for more details. https://github.com/csstools/postcss-plugins/discussions/192'),e.clear()}}}}};C.postcss=!0;export{C as default};
