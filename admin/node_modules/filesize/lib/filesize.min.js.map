{"version": 3, "file": "filesize.min.js", "sources": ["../src/filesize.js"], "sourcesContent": ["const b = /^(b|B)$/,\r\n\tsymbol = {\r\n\t\tiec: {\r\n\t\t\tbits: [\"bit\", \"Kibit\", \"Mibit\", \"Gibit\", \"Tibit\", \"Pibit\", \"Eibit\", \"Zibit\", \"Yibit\"],\r\n\t\t\tbytes: [\"B\", \"Ki<PERSON>\", \"Mi<PERSON>\", \"GiB\", \"TiB\", \"Pi<PERSON>\", \"EiB\", \"ZiB\", \"YiB\"]\r\n\t\t},\r\n\t\tjedec: {\r\n\t\t\tbits: [\"bit\", \"Kbit\", \"Mbit\", \"Gbit\", \"Tbit\", \"Pbit\", \"Ebit\", \"Zbit\", \"Ybit\"],\r\n\t\t\tbytes: [\"B\", \"KB\", \"MB\", \"GB\", \"TB\", \"PB\", \"EB\", \"ZB\", \"YB\"]\r\n\t\t}\r\n\t},\r\n\tfullform = {\r\n\t\tiec: [\"\", \"kibi\", \"mebi\", \"gibi\", \"tebi\", \"pebi\", \"exbi\", \"zebi\", \"yobi\"],\r\n\t\tjedec: [\"\", \"kilo\", \"mega\", \"giga\", \"tera\", \"peta\", \"exa\", \"zetta\", \"yotta\"]\r\n\t},\r\n\troundingFuncs = {\r\n\t\tfloor: Math.floor,\r\n\t\tceil: Math.ceil\r\n\t};\r\n\r\n/**\r\n * filesize\r\n *\r\n * @method filesize\r\n * @param  {Mixed}   arg        String, Int or Float to transform\r\n * @param  {Object}  descriptor [Optional] Flags\r\n * @return {String}             Readable file size String\r\n */\r\nfunction filesize (arg, descriptor = {}) {\r\n\tlet result = [],\r\n\t\tval = 0,\r\n\t\te, base, bits, ceil, full, fullforms, locale, localeOptions, neg, num, output, pad, round, u, unix, separator, spacer, standard, symbols, roundingFunc, precision;\r\n\r\n\tif (isNaN(arg)) {\r\n\t\tthrow new TypeError(\"Invalid number\");\r\n\t}\r\n\r\n\tbits = descriptor.bits === true;\r\n\tunix = descriptor.unix === true;\r\n\tpad = descriptor.pad === true;\r\n\tbase = descriptor.base || 10;\r\n\tround = descriptor.round !== void 0 ? descriptor.round : unix ? 1 : 2;\r\n\tlocale = descriptor.locale !== void 0 ? descriptor.locale : \"\";\r\n\tlocaleOptions = descriptor.localeOptions || {};\r\n\tseparator = descriptor.separator !== void 0 ? descriptor.separator : \"\";\r\n\tspacer = descriptor.spacer !== void 0 ? descriptor.spacer : unix ? \"\" : \" \";\r\n\tsymbols = descriptor.symbols || {};\r\n\tstandard = base === 2 ? descriptor.standard || \"iec\" : \"jedec\";\r\n\toutput = descriptor.output || \"string\";\r\n\tfull = descriptor.fullform === true;\r\n\tfullforms = descriptor.fullforms instanceof Array ? descriptor.fullforms : [];\r\n\te = descriptor.exponent !== void 0 ? descriptor.exponent : -1;\r\n\troundingFunc = roundingFuncs[descriptor.roundingMethod] || Math.round;\r\n\tnum = Number(arg);\r\n\tneg = num < 0;\r\n\tceil = base > 2 ? 1000 : 1024;\r\n\tprecision = isNaN(descriptor.precision) === false ? parseInt(descriptor.precision, 10) : 0;\r\n\r\n\t// Flipping a negative number to determine the size\r\n\tif (neg) {\r\n\t\tnum = -num;\r\n\t}\r\n\r\n\t// Determining the exponent\r\n\tif (e === -1 || isNaN(e)) {\r\n\t\te = Math.floor(Math.log(num) / Math.log(ceil));\r\n\r\n\t\tif (e < 0) {\r\n\t\t\te = 0;\r\n\t\t}\r\n\t}\r\n\r\n\t// Exceeding supported length, time to reduce & multiply\r\n\tif (e > 8) {\r\n\t\tif (precision > 0) {\r\n\t\t\tprecision += 8 - e;\r\n\t\t}\r\n\r\n\t\te = 8;\r\n\t}\r\n\r\n\tif (output === \"exponent\") {\r\n\t\treturn e;\r\n\t}\r\n\r\n\t// Zero is now a special case because bytes divide by 1\r\n\tif (num === 0) {\r\n\t\tresult[0] = 0;\r\n\t\tu = result[1] = unix ? \"\" : symbol[standard][bits ? \"bits\" : \"bytes\"][e];\r\n\t} else {\r\n\t\tval = num / (base === 2 ? Math.pow(2, e * 10) : Math.pow(1000, e));\r\n\r\n\t\tif (bits) {\r\n\t\t\tval = val * 8;\r\n\r\n\t\t\tif (val >= ceil && e < 8) {\r\n\t\t\t\tval = val / ceil;\r\n\t\t\t\te++;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tconst p = Math.pow(10, e > 0 ? round : 0);\r\n\t\tresult[0] = roundingFunc(val * p) / p;\r\n\r\n\t\tif (result[0] === ceil && e < 8 && descriptor.exponent === void 0) {\r\n\t\t\tresult[0] = 1;\r\n\t\t\te++;\r\n\t\t}\r\n\r\n\t\tu = result[1] = base === 10 && e === 1 ? bits ? \"kbit\" : \"kB\" : symbol[standard][bits ? \"bits\" : \"bytes\"][e];\r\n\r\n\t\tif (unix) {\r\n\t\t\tresult[1] = result[1].charAt(0);\r\n\r\n\t\t\tif (b.test(result[1])) {\r\n\t\t\t\tresult[0] = Math.floor(result[0]);\r\n\t\t\t\tresult[1] = \"\";\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t// Decorating a 'diff'\r\n\tif (neg) {\r\n\t\tresult[0] = -result[0];\r\n\t}\r\n\r\n\t// Setting optional precision\r\n\tif (precision > 0) {\r\n\t\tresult[0] = result[0].toPrecision(precision);\r\n\t}\r\n\r\n\t// Applying custom symbol\r\n\tresult[1] = symbols[result[1]] || result[1];\r\n\r\n\tif (locale === true) {\r\n\t\tresult[0] = result[0].toLocaleString();\r\n\t} else if (locale.length > 0) {\r\n\t\tresult[0] = result[0].toLocaleString(locale, localeOptions);\r\n\t} else if (separator.length > 0) {\r\n\t\tresult[0] = result[0].toString().replace(\".\", separator);\r\n\t}\r\n\r\n\tif (pad && Number.isInteger(result[0]) === false && round > 0) {\r\n\t\tconst x = separator || \".\",\r\n\t\t\ttmp = result[0].toString().split(x),\r\n\t\t\ts = tmp[1] || \"\",\r\n\t\t\tl = s.length,\r\n\t\t\tn = round - l;\r\n\r\n\t\tresult[0] = `${tmp[0]}${x}${s.padEnd(l + n, \"0\")}`;\r\n\t}\r\n\r\n\tif (full) {\r\n\t\tresult[1] = fullforms[e] ? fullforms[e] : fullform[standard][e] + (bits ? \"bit\" : \"byte\") + (result[0] === 1 ? \"\" : \"s\");\r\n\t}\r\n\r\n\t// Returning Array, Object, or String (default)\r\n\treturn output === \"array\" ? result : output === \"object\" ? {value: result[0], symbol: result[1], exponent: e, unit: u} : result.join(spacer);\r\n}\r\n\r\n// Partial application for functional programming\r\nfilesize.partial = opt => arg => filesize(arg, opt);\r\n\r\nexport default filesize;\r\n"], "names": ["b", "symbol", "iec", "bits", "bytes", "jedec", "fullform", "roundingFuncs", "floor", "Math", "ceil", "filesize", "arg", "e", "base", "full", "fullforms", "locale", "localeOptions", "neg", "num", "output", "pad", "round", "u", "unix", "separator", "spacer", "standard", "symbols", "roundingFunc", "precision", "descriptor", "result", "val", "isNaN", "TypeError", "Array", "exponent", "roundingMethod", "Number", "parseInt", "log", "pow", "p", "char<PERSON>t", "test", "toPrecision", "toLocaleString", "length", "toString", "replace", "isInteger", "x", "tmp", "split", "s", "l", "n", "padEnd", "value", "unit", "join", "partial", "opt"], "mappings": ";;;;yOAAA,IAAMA,EAAI,UACTC,EAAS,CACRC,IAAK,CACJC,KAAM,CAAC,MAAO,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,QAAS,SAC7EC,MAAO,CAAC,IAAK,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,QAE/DC,MAAO,CACNF,KAAM,CAAC,MAAO,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QACtEC,MAAO,CAAC,IAAK,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,QAGzDE,EAAW,CACVJ,IAAK,CAAC,GAAI,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,QAClEG,MAAO,CAAC,GAAI,OAAQ,OAAQ,OAAQ,OAAQ,OAAQ,MAAO,QAAS,UAErEE,EAAgB,CACfC,MAAOC,KAAKD,MACZE,KAAMD,KAAKC,MAWb,SAASC,EAAUC,OAGjBC,EAAGC,EAAMX,EAAMO,EAAMK,EAAMC,EAAWC,EAAQC,EAAeC,EAAKC,EAAKC,EAAQC,EAAKC,EAAOC,EAAGC,EAAMC,EAAWC,EAAQC,EAAUC,EAASC,EAAcC,EAHlIC,yDAAa,GAChCC,EAAS,GACZC,EAAM,KAGHC,MAAMvB,SACH,IAAIwB,UAAU,qBAGrBjC,GAA2B,IAApB6B,EAAW7B,KAClBsB,GAA2B,IAApBO,EAAWP,KAClBH,GAAyB,IAAnBU,EAAWV,IACjBR,EAAOkB,EAAWlB,MAAQ,GAC1BS,OAA6B,IAArBS,EAAWT,MAAmBS,EAAWT,MAAQE,EAAO,EAAI,EACpER,OAA+B,IAAtBe,EAAWf,OAAoBe,EAAWf,OAAS,GAC5DC,EAAgBc,EAAWd,eAAiB,GAC5CQ,OAAqC,IAAzBM,EAAWN,UAAuBM,EAAWN,UAAY,GACrEC,OAA+B,IAAtBK,EAAWL,OAAoBK,EAAWL,OAASF,EAAO,GAAK,IACxEI,EAAUG,EAAWH,SAAW,GAChCD,EAAoB,IAATd,EAAakB,EAAWJ,UAAY,MAAQ,QACvDP,EAASW,EAAWX,QAAU,SAC9BN,GAA+B,IAAxBiB,EAAW1B,SAClBU,EAAYgB,EAAWhB,qBAAqBqB,MAAQL,EAAWhB,UAAY,GAC3EH,OAA4B,IAAxBmB,EAAWM,SAAsBN,EAAWM,UAAY,EAC5DR,EAAevB,EAAcyB,EAAWO,iBAAmB9B,KAAKc,MAEhEJ,GADAC,EAAMoB,OAAO5B,IACD,EACZF,EAAOI,EAAO,EAAI,IAAO,KACzBiB,GAA4C,IAAhCI,MAAMH,EAAWD,WAAuBU,SAAST,EAAWD,UAAW,IAAM,EAGrFZ,IACHC,GAAOA,KAIG,IAAPP,GAAYsB,MAAMtB,MACrBA,EAAIJ,KAAKD,MAAMC,KAAKiC,IAAItB,GAAOX,KAAKiC,IAAIhC,KAEhC,IACPG,EAAI,GAKFA,EAAI,IACHkB,EAAY,IACfA,GAAa,EAAIlB,GAGlBA,EAAI,GAGU,aAAXQ,SACIR,KAII,IAARO,EACHa,EAAO,GAAK,EACZT,EAAIS,EAAO,GAAKR,EAAO,GAAKxB,EAAO2B,GAAUzB,EAAO,OAAS,SAASU,OAChE,CACNqB,EAAMd,GAAgB,IAATN,EAAaL,KAAKkC,IAAI,EAAO,GAAJ9B,GAAUJ,KAAKkC,IAAI,IAAM9B,IAE3DV,IACH+B,GAAY,IAEDxB,GAAQG,EAAI,IACtBqB,GAAYxB,EACZG,SAII+B,EAAInC,KAAKkC,IAAI,GAAI9B,EAAI,EAAIU,EAAQ,GACvCU,EAAO,GAAKH,EAAaI,EAAMU,GAAKA,EAEhCX,EAAO,KAAOvB,GAAQG,EAAI,QAA6B,IAAxBmB,EAAWM,WAC7CL,EAAO,GAAK,EACZpB,KAGDW,EAAIS,EAAO,GAAc,KAATnB,GAAqB,IAAND,EAAUV,EAAO,OAAS,KAAOF,EAAO2B,GAAUzB,EAAO,OAAS,SAASU,GAEtGY,IACHQ,EAAO,GAAKA,EAAO,GAAGY,OAAO,GAEzB7C,EAAE8C,KAAKb,EAAO,MACjBA,EAAO,GAAKxB,KAAKD,MAAMyB,EAAO,IAC9BA,EAAO,GAAK,QAMXd,IACHc,EAAO,IAAMA,EAAO,IAIjBF,EAAY,IACfE,EAAO,GAAKA,EAAO,GAAGc,YAAYhB,IAInCE,EAAO,GAAKJ,EAAQI,EAAO,KAAOA,EAAO,IAE1B,IAAXhB,EACHgB,EAAO,GAAKA,EAAO,GAAGe,iBACZ/B,EAAOgC,OAAS,EAC1BhB,EAAO,GAAKA,EAAO,GAAGe,eAAe/B,EAAQC,GACnCQ,EAAUuB,OAAS,IAC7BhB,EAAO,GAAKA,EAAO,GAAGiB,WAAWC,QAAQ,IAAKzB,IAG3CJ,IAAuC,IAAhCkB,OAAOY,UAAUnB,EAAO,KAAiBV,EAAQ,EAAG,KACxD8B,EAAI3B,GAAa,IACtB4B,EAAMrB,EAAO,GAAGiB,WAAWK,MAAMF,GACjCG,EAAIF,EAAI,IAAM,GACdG,EAAID,EAAEP,OACNS,EAAInC,EAAQkC,EAEbxB,EAAO,aAAQqB,EAAI,WAAKD,UAAIG,EAAEG,OAAOF,EAAIC,EAAG,aAGzC3C,IACHkB,EAAO,GAAKjB,EAAUH,GAAKG,EAAUH,GAAKP,EAASsB,GAAUf,IAAMV,EAAO,MAAQ,SAAyB,IAAd8B,EAAO,GAAW,GAAK,MAInG,UAAXZ,EAAqBY,EAAoB,WAAXZ,EAAsB,CAACuC,MAAO3B,EAAO,GAAIhC,OAAQgC,EAAO,GAAIK,SAAUzB,EAAGgD,KAAMrC,GAAKS,EAAO6B,KAAKnC,UAItIhB,EAASoD,QAAU,SAAAC,UAAO,SAAApD,UAAOD,EAASC,EAAKoD"}