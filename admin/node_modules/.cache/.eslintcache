[{"/Users/<USER>/github/7/admin/src/index.tsx": "1", "/Users/<USER>/github/7/admin/src/App.tsx": "2", "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx": "3", "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx": "4", "/Users/<USER>/github/7/admin/src/components/Header.tsx": "5", "/Users/<USER>/github/7/admin/src/pages/QrCodeList.tsx": "6", "/Users/<USER>/github/7/admin/src/pages/QrCodeDetail.tsx": "7", "/Users/<USER>/github/7/admin/src/pages/CreateQrCode.tsx": "8", "/Users/<USER>/github/7/admin/src/services/api.ts": "9"}, {"size": 610, "mtime": 1749281211208, "results": "10", "hashOfConfig": "11"}, {"size": 964, "mtime": 1749281231828, "results": "12", "hashOfConfig": "11"}, {"size": 1280, "mtime": 1749281267153, "results": "13", "hashOfConfig": "11"}, {"size": 2729, "mtime": 1749281296430, "results": "14", "hashOfConfig": "11"}, {"size": 500, "mtime": 1749281276588, "results": "15", "hashOfConfig": "11"}, {"size": 6428, "mtime": 1749283082679, "results": "16", "hashOfConfig": "11"}, {"size": 8324, "mtime": 1749283156555, "results": "17", "hashOfConfig": "11"}, {"size": 4086, "mtime": 1749283111904, "results": "18", "hashOfConfig": "11"}, {"size": 3519, "mtime": 1749291073022, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10cbklz", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/github/7/admin/src/index.tsx", [], [], "/Users/<USER>/github/7/admin/src/App.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Header.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/QrCodeList.tsx", ["47"], [], "/Users/<USER>/github/7/admin/src/pages/QrCodeDetail.tsx", ["48", "49", "50"], [], "/Users/<USER>/github/7/admin/src/pages/CreateQrCode.tsx", [], [], "/Users/<USER>/github/7/admin/src/services/api.ts", [], [], {"ruleId": "51", "severity": 1, "message": "52", "line": 42, "column": 6, "nodeType": "53", "endLine": 42, "endColumn": 56, "suggestions": "54"}, {"ruleId": "55", "severity": 1, "message": "56", "line": 15, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 15, "endColumn": 8}, {"ruleId": "55", "severity": 1, "message": "59", "line": 24, "column": 3, "nodeType": "57", "messageId": "58", "endLine": 24, "endColumn": 17}, {"ruleId": "51", "severity": 1, "message": "60", "line": 48, "column": 6, "nodeType": "53", "endLine": 48, "endColumn": 10, "suggestions": "61"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["62"], "@typescript-eslint/no-unused-vars", "'Modal' is defined but never used.", "Identifier", "unusedVar", "'QrcodeOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["63"], {"desc": "64", "fix": "65"}, {"desc": "66", "fix": "67"}, "Update the dependencies array to be: [pagination.pageSize, filters, fetchData]", {"range": "68", "text": "69"}, "Update the dependencies array to be: [fetchData, id]", {"range": "70", "text": "71"}, [881, 931], "[pagination.pageSize, filters, fetchData]", [1086, 1090], "[fetchData, id]"]