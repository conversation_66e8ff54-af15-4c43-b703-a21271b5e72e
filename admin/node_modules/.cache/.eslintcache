[{"/Users/<USER>/github/7/admin/src/index.tsx": "1", "/Users/<USER>/github/7/admin/src/App.tsx": "2", "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx": "3", "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx": "4", "/Users/<USER>/github/7/admin/src/components/Header.tsx": "5", "/Users/<USER>/github/7/admin/src/services/api.ts": "6", "/Users/<USER>/github/7/admin/src/pages/ProductList.tsx": "7", "/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx": "8", "/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx": "9", "/Users/<USER>/github/7/admin/src/contexts/AuthContext.tsx": "10", "/Users/<USER>/github/7/admin/src/pages/ChangePassword.tsx": "11", "/Users/<USER>/github/7/admin/src/pages/Login.tsx": "12", "/Users/<USER>/github/7/admin/src/components/ProtectedRoute.tsx": "13", "/Users/<USER>/github/7/admin/src/components/QRCodeModal.tsx": "14"}, {"size": 610, "mtime": 1749281211208, "results": "15", "hashOfConfig": "16"}, {"size": 1989, "mtime": 1749316672941, "results": "17", "hashOfConfig": "16"}, {"size": 2783, "mtime": 1749316515108, "results": "18", "hashOfConfig": "16"}, {"size": 3203, "mtime": 1749316614242, "results": "19", "hashOfConfig": "16"}, {"size": 519, "mtime": 1749316530217, "results": "20", "hashOfConfig": "16"}, {"size": 6023, "mtime": 1749368805078, "results": "21", "hashOfConfig": "16"}, {"size": 9712, "mtime": 1749322714014, "results": "22", "hashOfConfig": "16"}, {"size": 9587, "mtime": 1749315349188, "results": "23", "hashOfConfig": "16"}, {"size": 6881, "mtime": 1749315301741, "results": "24", "hashOfConfig": "16"}, {"size": 2580, "mtime": 1749316373694, "results": "25", "hashOfConfig": "16"}, {"size": 4682, "mtime": 1749316418982, "results": "26", "hashOfConfig": "16"}, {"size": 3729, "mtime": 1749316394685, "results": "27", "hashOfConfig": "16"}, {"size": 840, "mtime": 1749316429940, "results": "28", "hashOfConfig": "16"}, {"size": 6794, "mtime": 1749368805078, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10cbklz", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/github/7/admin/src/index.tsx", [], [], "/Users/<USER>/github/7/admin/src/App.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Header.tsx", [], [], "/Users/<USER>/github/7/admin/src/services/api.ts", [], [], "/Users/<USER>/github/7/admin/src/pages/ProductList.tsx", ["72"], [], "/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx", ["73"], [], "/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx", [], [], "/Users/<USER>/github/7/admin/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/ChangePassword.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/Login.tsx", ["74"], [], "/Users/<USER>/github/7/admin/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/QRCodeModal.tsx", ["75"], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 50, "column": 6, "nodeType": "78", "endLine": 50, "endColumn": 56, "suggestions": "79"}, {"ruleId": "76", "severity": 1, "message": "80", "line": 47, "column": 6, "nodeType": "78", "endLine": 47, "endColumn": 10, "suggestions": "81"}, {"ruleId": "82", "severity": 1, "message": "83", "line": 2, "column": 49, "nodeType": "84", "messageId": "85", "endLine": 2, "endColumn": 54}, {"ruleId": "76", "severity": 1, "message": "86", "line": 107, "column": 6, "nodeType": "78", "endLine": 107, "endColumn": 24, "suggestions": "87"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["88"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["89"], "@typescript-eslint/no-unused-vars", "'Space' is defined but never used.", "Identifier", "unusedVar", "React Hook React.useEffect has missing dependencies: 'generateQRCode' and 'qrCodeData'. Either include them or remove the dependency array.", ["90"], {"desc": "91", "fix": "92"}, {"desc": "93", "fix": "94"}, {"desc": "95", "fix": "96"}, "Update the dependencies array to be: [pagination.pageSize, filters, fetchData]", {"range": "97", "text": "98"}, "Update the dependencies array to be: [fetchData, id]", {"range": "99", "text": "100"}, "Update the dependencies array to be: [visible, product, qrCodeData, generateQRCode]", {"range": "101", "text": "102"}, [1281, 1331], "[pagination.pageSize, filters, fetchData]", [1053, 1057], "[fetchData, id]", [2304, 2322], "[visible, product, qrCodeData, generateQRCode]"]