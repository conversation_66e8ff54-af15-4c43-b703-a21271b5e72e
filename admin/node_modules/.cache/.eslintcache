[{"/Users/<USER>/github/7/admin/src/index.tsx": "1", "/Users/<USER>/github/7/admin/src/App.tsx": "2", "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx": "3", "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx": "4", "/Users/<USER>/github/7/admin/src/components/Header.tsx": "5", "/Users/<USER>/github/7/admin/src/services/api.ts": "6", "/Users/<USER>/github/7/admin/src/pages/ProductList.tsx": "7", "/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx": "8", "/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx": "9", "/Users/<USER>/github/7/admin/src/contexts/AuthContext.tsx": "10", "/Users/<USER>/github/7/admin/src/pages/ChangePassword.tsx": "11", "/Users/<USER>/github/7/admin/src/pages/Login.tsx": "12", "/Users/<USER>/github/7/admin/src/components/ProtectedRoute.tsx": "13"}, {"size": 610, "mtime": 1749281211208, "results": "14", "hashOfConfig": "15"}, {"size": 1989, "mtime": 1749316672941, "results": "16", "hashOfConfig": "15"}, {"size": 2783, "mtime": 1749316515108, "results": "17", "hashOfConfig": "15"}, {"size": 3203, "mtime": 1749316614242, "results": "18", "hashOfConfig": "15"}, {"size": 519, "mtime": 1749316530217, "results": "19", "hashOfConfig": "15"}, {"size": 5088, "mtime": 1749320218252, "results": "20", "hashOfConfig": "15"}, {"size": 9419, "mtime": 1749320238506, "results": "21", "hashOfConfig": "15"}, {"size": 9587, "mtime": 1749315349188, "results": "22", "hashOfConfig": "15"}, {"size": 6881, "mtime": 1749315301741, "results": "23", "hashOfConfig": "15"}, {"size": 2580, "mtime": 1749316373694, "results": "24", "hashOfConfig": "15"}, {"size": 4682, "mtime": 1749316418982, "results": "25", "hashOfConfig": "15"}, {"size": 3729, "mtime": 1749316394685, "results": "26", "hashOfConfig": "15"}, {"size": 840, "mtime": 1749316429940, "results": "27", "hashOfConfig": "15"}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10cbklz", {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/github/7/admin/src/index.tsx", [], [], "/Users/<USER>/github/7/admin/src/App.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Header.tsx", [], [], "/Users/<USER>/github/7/admin/src/services/api.ts", [], [], "/Users/<USER>/github/7/admin/src/pages/ProductList.tsx", ["67"], [], "/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx", ["68"], [], "/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx", [], [], "/Users/<USER>/github/7/admin/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/ChangePassword.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/Login.tsx", ["69"], [], "/Users/<USER>/github/7/admin/src/components/ProtectedRoute.tsx", [], [], {"ruleId": "70", "severity": 1, "message": "71", "line": 47, "column": 6, "nodeType": "72", "endLine": 47, "endColumn": 56, "suggestions": "73"}, {"ruleId": "70", "severity": 1, "message": "74", "line": 47, "column": 6, "nodeType": "72", "endLine": 47, "endColumn": 10, "suggestions": "75"}, {"ruleId": "76", "severity": 1, "message": "77", "line": 2, "column": 49, "nodeType": "78", "messageId": "79", "endLine": 2, "endColumn": 54}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["80"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["81"], "@typescript-eslint/no-unused-vars", "'Space' is defined but never used.", "Identifier", "unusedVar", {"desc": "82", "fix": "83"}, {"desc": "84", "fix": "85"}, "Update the dependencies array to be: [pagination.pageSize, filters, fetchData]", {"range": "86", "text": "87"}, "Update the dependencies array to be: [fetchData, id]", {"range": "88", "text": "89"}, [1077, 1127], "[pagination.pageSize, filters, fetchData]", [1053, 1057], "[fetchData, id]"]