[{"/Users/<USER>/github/7/admin/src/index.tsx": "1", "/Users/<USER>/github/7/admin/src/App.tsx": "2", "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx": "3", "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx": "4", "/Users/<USER>/github/7/admin/src/components/Header.tsx": "5", "/Users/<USER>/github/7/admin/src/pages/QrCodeList.tsx": "6", "/Users/<USER>/github/7/admin/src/pages/QrCodeDetail.tsx": "7", "/Users/<USER>/github/7/admin/src/pages/CreateQrCode.tsx": "8", "/Users/<USER>/github/7/admin/src/services/api.ts": "9", "/Users/<USER>/github/7/admin/src/pages/ProductList.tsx": "10", "/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx": "11", "/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx": "12"}, {"size": 610, "mtime": 1749281211208, "results": "13", "hashOfConfig": "14"}, {"size": 1342, "mtime": 1749315380440, "results": "15", "hashOfConfig": "14"}, {"size": 1561, "mtime": 1749315232219, "results": "16", "hashOfConfig": "14"}, {"size": 5180, "mtime": 1749315476903, "results": "17", "hashOfConfig": "14"}, {"size": 500, "mtime": 1749281276588, "results": "18", "hashOfConfig": "14"}, {"size": 6428, "mtime": 1749283082679, "results": "19", "hashOfConfig": "14"}, {"size": 8324, "mtime": 1749283156555, "results": "20", "hashOfConfig": "14"}, {"size": 4086, "mtime": 1749283111904, "results": "21", "hashOfConfig": "14"}, {"size": 5880, "mtime": 1749315194405, "results": "22", "hashOfConfig": "14"}, {"size": 8854, "mtime": 1749315274258, "results": "23", "hashOfConfig": "14"}, {"size": 9587, "mtime": 1749315349188, "results": "24", "hashOfConfig": "14"}, {"size": 6881, "mtime": 1749315301741, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10cbklz", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/github/7/admin/src/index.tsx", [], [], "/Users/<USER>/github/7/admin/src/App.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Sidebar.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/github/7/admin/src/components/Header.tsx", [], [], "/Users/<USER>/github/7/admin/src/pages/QrCodeList.tsx", ["62"], [], "/Users/<USER>/github/7/admin/src/pages/QrCodeDetail.tsx", ["63", "64", "65"], [], "/Users/<USER>/github/7/admin/src/pages/CreateQrCode.tsx", [], [], "/Users/<USER>/github/7/admin/src/services/api.ts", [], [], "/Users/<USER>/github/7/admin/src/pages/ProductList.tsx", ["66"], [], "/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx", ["67"], [], "/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx", [], [], {"ruleId": "68", "severity": 1, "message": "69", "line": 42, "column": 6, "nodeType": "70", "endLine": 42, "endColumn": 56, "suggestions": "71"}, {"ruleId": "72", "severity": 1, "message": "73", "line": 15, "column": 3, "nodeType": "74", "messageId": "75", "endLine": 15, "endColumn": 8}, {"ruleId": "72", "severity": 1, "message": "76", "line": 24, "column": 3, "nodeType": "74", "messageId": "75", "endLine": 24, "endColumn": 17}, {"ruleId": "68", "severity": 1, "message": "77", "line": 48, "column": 6, "nodeType": "70", "endLine": 48, "endColumn": 10, "suggestions": "78"}, {"ruleId": "68", "severity": 1, "message": "69", "line": 46, "column": 6, "nodeType": "70", "endLine": 46, "endColumn": 56, "suggestions": "79"}, {"ruleId": "68", "severity": 1, "message": "77", "line": 47, "column": 6, "nodeType": "70", "endLine": 47, "endColumn": 10, "suggestions": "80"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["81"], "@typescript-eslint/no-unused-vars", "'Modal' is defined but never used.", "Identifier", "unusedVar", "'QrcodeOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["82"], ["83"], ["84"], {"desc": "85", "fix": "86"}, {"desc": "87", "fix": "88"}, {"desc": "85", "fix": "89"}, {"desc": "87", "fix": "90"}, "Update the dependencies array to be: [pagination.pageSize, filters, fetchData]", {"range": "91", "text": "92"}, "Update the dependencies array to be: [fetchData, id]", {"range": "93", "text": "94"}, {"range": "95", "text": "92"}, {"range": "96", "text": "94"}, [881, 931], "[pagination.pageSize, filters, fetchData]", [1086, 1090], "[fetchData, id]", [1059, 1109], [1053, 1057]]