{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable react-hooks/exhaustive-deps */\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport TabContext from \"../TabContext\";\nimport useIndicator from \"../hooks/useIndicator\";\nimport useOffsets from \"../hooks/useOffsets\";\nimport useSyncState from \"../hooks/useSyncState\";\nimport useTouchMove from \"../hooks/useTouchMove\";\nimport useUpdate, { useUpdateState } from \"../hooks/useUpdate\";\nimport useVisibleRange from \"../hooks/useVisibleRange\";\nimport { genDataNodeKey, getRemovable, stringify } from \"../util\";\nimport AddButton from \"./AddButton\";\nimport ExtraContent from \"./ExtraContent\";\nimport OperationNode from \"./OperationNode\";\nimport TabNode from \"./TabNode\";\nvar getTabSize = function getTabSize(tab, containerRect) {\n  // tabListRef\n  var offsetWidth = tab.offsetWidth,\n    offsetHeight = tab.offsetHeight,\n    offsetTop = tab.offsetTop,\n    offsetLeft = tab.offsetLeft;\n  var _tab$getBoundingClien = tab.getBoundingClientRect(),\n    width = _tab$getBoundingClien.width,\n    height = _tab$getBoundingClien.height,\n    left = _tab$getBoundingClien.left,\n    top = _tab$getBoundingClien.top;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (Math.abs(width - offsetWidth) < 1) {\n    return [width, height, left - containerRect.left, top - containerRect.top];\n  }\n  return [offsetWidth, offsetHeight, offsetLeft, offsetTop];\n};\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (refObj.current) {\n    var _refObj$current$getBo = refObj.current.getBoundingClientRect(),\n      width = _refObj$current$getBo.width,\n      height = _refObj$current$getBo.height;\n    if (Math.abs(width - offsetWidth) < 1) {\n      return [width, height];\n    }\n  }\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nvar TabNavList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    indicator = props.indicator;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var containerRef = useRef(null);\n  var extraLeftRef = useRef(null);\n  var extraRightRef = useRef(null);\n  var tabsWrapperRef = useRef(null);\n  var tabListRef = useRef(null);\n  var operationsRef = useRef(null);\n  var innerAddButtonRef = useRef(null);\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = useUpdateState(new Map()),\n    _useUpdateState2 = _slicedToArray(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = Math.floor(containerExcludeExtraSizeValue) < Math.floor(tabContentSizeValue + addSizeValue);\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = useRef(null);\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    if (touchMovingRef.current) {\n      clearTimeout(touchMovingRef.current);\n    }\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = useEvent(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================= Focus =========================\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    focusKey = _useState12[0],\n    setFocusKey = _useState12[1];\n  var _useState13 = useState(false),\n    _useState14 = _slicedToArray(_useState13, 2),\n    isMouse = _useState14[0],\n    setIsMouse = _useState14[1];\n  var enabledTabs = tabs.filter(function (tab) {\n    return !tab.disabled;\n  }).map(function (tab) {\n    return tab.key;\n  });\n  var onOffset = function onOffset(offset) {\n    var currentIndex = enabledTabs.indexOf(focusKey || activeKey);\n    var len = enabledTabs.length;\n    var nextIndex = (currentIndex + offset + len) % len;\n    var newKey = enabledTabs[nextIndex];\n    setFocusKey(newKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var code = e.code;\n    var isRTL = rtl && tabPositionTopOrBottom;\n    var firstEnabledTab = enabledTabs[0];\n    var lastEnabledTab = enabledTabs[enabledTabs.length - 1];\n    switch (code) {\n      // LEFT\n      case 'ArrowLeft':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? 1 : -1);\n          }\n          break;\n        }\n\n      // RIGHT\n      case 'ArrowRight':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? -1 : 1);\n          }\n          break;\n        }\n\n      // UP\n      case 'ArrowUp':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(-1);\n          }\n          break;\n        }\n\n      // DOWN\n      case 'ArrowDown':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(1);\n          }\n          break;\n        }\n\n      // HOME\n      case 'Home':\n        {\n          e.preventDefault();\n          setFocusKey(firstEnabledTab);\n          break;\n        }\n\n      // END\n      case 'End':\n        {\n          e.preventDefault();\n          setFocusKey(lastEnabledTab);\n          break;\n        }\n\n      // Enter & Space\n      case 'Enter':\n      case 'Space':\n        {\n          e.preventDefault();\n          onTabClick(focusKey !== null && focusKey !== void 0 ? focusKey : activeKey, e);\n          break;\n        }\n      // Backspace\n      case 'Backspace':\n      case 'Delete':\n        {\n          var removeIndex = enabledTabs.indexOf(focusKey);\n          var removeTab = tabs.find(function (tab) {\n            return tab.key === focusKey;\n          });\n          var removable = getRemovable(removeTab === null || removeTab === void 0 ? void 0 : removeTab.closable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.closeIcon, editable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.disabled);\n          if (removable) {\n            e.preventDefault();\n            e.stopPropagation();\n            editable.onEdit('remove', {\n              key: focusKey,\n              event: e\n            });\n            // when remove last tab, focus previous tab\n            if (removeIndex === enabledTabs.length - 1) {\n              onOffset(-1);\n            } else {\n              onOffset(1);\n            }\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPositionTopOrBottom) {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      focus: key === focusKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      tabCount: enabledTabs.length,\n      currentPosition: i + 1,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onKeyDown: handleKeyDown,\n      onFocus: function onFocus() {\n        if (!isMouse) {\n          setFocusKey(key);\n        }\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      },\n      onBlur: function onBlur() {\n        setFocusKey(undefined);\n      },\n      onMouseDown: function onMouseDown() {\n        setIsMouse(true);\n      },\n      onMouseUp: function onMouseUp() {\n        setIsMouse(false);\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var _tabListRef$current;\n      var newSizes = new Map();\n      var listRect = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.getBoundingClientRect();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current2;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.querySelector(\"[data-node-key=\\\"\".concat(genDataNodeKey(key), \"\\\"]\"));\n        if (btnNode) {\n          var _getTabSize = getTabSize(btnNode, listRect),\n            _getTabSize2 = _slicedToArray(_getTabSize, 4),\n            width = _getTabSize2[0],\n            height = _getTabSize2[1],\n            left = _getTabSize2[2],\n            top = _getTabSize2[3];\n          newSizes.set(key, {\n            width: width,\n            height: height,\n            left: left,\n            top: top\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  useEffect(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = useUpdate(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var activeTabOffset = tabOffsets.get(activeKey);\n  var _useIndicator = useIndicator({\n      activeTabOffset: activeTabOffset,\n      horizontal: tabPositionTopOrBottom,\n      indicator: indicator,\n      rtl: rtl\n    }),\n    indicatorStyle = _useIndicator.style;\n\n  // ========================= Effect ========================\n  useEffect(function () {\n    scrollToTab();\n  }, [activeKey, transformMin, transformMax, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  useEffect(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    \"aria-orientation\": tabPositionTopOrBottom ? 'horizontal' : 'vertical',\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: indicatorStyle\n  }))))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n});\nexport default TabNavList;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_toConsumableArray", "_objectSpread", "_slicedToArray", "classNames", "ResizeObserver", "useEvent", "useComposeRef", "React", "useEffect", "useRef", "useState", "TabContext", "useIndicator", "useOffsets", "useSyncState", "useTouchMove", "useUpdate", "useUpdateState", "useVisibleRange", "genDataNodeKey", "getRemovable", "stringify", "AddButton", "ExtraContent", "OperationNode", "TabNode", "getTabSize", "tab", "containerRect", "offsetWidth", "offsetHeight", "offsetTop", "offsetLeft", "_tab$getBoundingClien", "getBoundingClientRect", "width", "height", "left", "top", "Math", "abs", "getSize", "refObj", "_ref", "current", "_ref$offsetWidth", "_ref$offsetHeight", "_refObj$current$getBo", "getUnitValue", "size", "tabPositionTopOrBottom", "TabNavList", "forwardRef", "props", "ref", "className", "style", "id", "animated", "active<PERSON><PERSON>", "rtl", "extra", "editable", "locale", "tabPosition", "tabBarGutter", "children", "onTabClick", "onTabScroll", "indicator", "_React$useContext", "useContext", "prefixCls", "tabs", "containerRef", "extraLeftRef", "extraRightRef", "tabsWrapperRef", "tabListRef", "operationsRef", "innerAddButtonRef", "_useSyncState", "next", "prev", "direction", "_useSyncState2", "transformLeft", "setTransformLeft", "_useSyncState3", "_useSyncState4", "transformTop", "setTransformTop", "_useState", "_useState2", "containerExcludeExtraSize", "setContainerExcludeExtraSize", "_useState3", "_useState4", "tabContentSize", "setTabContentSize", "_useState5", "_useState6", "addSize", "setAddSize", "_useState7", "_useState8", "operationSize", "setOperationSize", "_useUpdateState", "Map", "_useUpdateState2", "tabSizes", "setTabSizes", "tabOffsets", "containerExcludeExtraSizeValue", "tabContentSizeValue", "addSizeValue", "operationSizeValue", "needScroll", "floor", "visibleTabContentValue", "operationsHiddenClassName", "concat", "transformMin", "transformMax", "min", "max", "alignInRange", "value", "touchMovingRef", "_useState9", "_useState10", "lockAnimation", "setLockAnimation", "doLockAnimation", "Date", "now", "clearTouchMoving", "clearTimeout", "offsetX", "offsetY", "do<PERSON>ove", "setState", "offset", "newValue", "setTimeout", "_useVisibleRange", "_useVisibleRange2", "visibleStart", "visibleEnd", "scrollToTab", "key", "arguments", "length", "undefined", "tabOffset", "get", "right", "newTransform", "_newTransform", "_useState11", "_useState12", "focus<PERSON>ey", "setFocus<PERSON>ey", "_useState13", "_useState14", "isMouse", "setIsMouse", "enabledTabs", "filter", "disabled", "map", "onOffset", "currentIndex", "indexOf", "len", "nextIndex", "new<PERSON>ey", "handleKeyDown", "e", "code", "isRTL", "firstEnabledTab", "lastEnabledTab", "preventDefault", "removeIndex", "removeTab", "find", "removable", "closable", "closeIcon", "stopPropagation", "onEdit", "event", "tabNodeStyle", "marginTop", "tabNodes", "i", "createElement", "active", "focus", "renderWrapper", "removeAriaLabel", "tabCount", "currentPosition", "onClick", "onKeyDown", "onFocus", "scrollLeft", "scrollTop", "onBlur", "onMouseDown", "onMouseUp", "updateTabSizes", "_tabListRef$current", "newSizes", "listRect", "for<PERSON>ach", "_ref2", "_tabListRef$current2", "btnNode", "querySelector", "_getTabSize", "_getTabSize2", "set", "join", "onListHolderResize", "containerSize", "extraLeftSize", "extraRightSize", "newAddSize", "newOperationSize", "tabContentFullSize", "startHiddenTabs", "slice", "endHiddenTabs", "hiddenTabs", "activeTabOffset", "_useIndicator", "horizontal", "indicatorStyle", "hasDropdown", "wrapPrefix", "pingLeft", "pingRight", "pingTop", "pingBottom", "onResize", "role", "position", "transform", "transition", "visibility", "inkBar", "tabMoving"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-tabs/es/TabNavList/index.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/* eslint-disable react-hooks/exhaustive-deps */\nimport classNames from 'classnames';\nimport ResizeObserver from 'rc-resize-observer';\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\nimport { useEffect, useRef, useState } from 'react';\nimport TabContext from \"../TabContext\";\nimport useIndicator from \"../hooks/useIndicator\";\nimport useOffsets from \"../hooks/useOffsets\";\nimport useSyncState from \"../hooks/useSyncState\";\nimport useTouchMove from \"../hooks/useTouchMove\";\nimport useUpdate, { useUpdateState } from \"../hooks/useUpdate\";\nimport useVisibleRange from \"../hooks/useVisibleRange\";\nimport { genDataNodeKey, getRemovable, stringify } from \"../util\";\nimport AddButton from \"./AddButton\";\nimport ExtraContent from \"./ExtraContent\";\nimport OperationNode from \"./OperationNode\";\nimport TabNode from \"./TabNode\";\nvar getTabSize = function getTabSize(tab, containerRect) {\n  // tabListRef\n  var offsetWidth = tab.offsetWidth,\n    offsetHeight = tab.offsetHeight,\n    offsetTop = tab.offsetTop,\n    offsetLeft = tab.offsetLeft;\n  var _tab$getBoundingClien = tab.getBoundingClientRect(),\n    width = _tab$getBoundingClien.width,\n    height = _tab$getBoundingClien.height,\n    left = _tab$getBoundingClien.left,\n    top = _tab$getBoundingClien.top;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (Math.abs(width - offsetWidth) < 1) {\n    return [width, height, left - containerRect.left, top - containerRect.top];\n  }\n  return [offsetWidth, offsetHeight, offsetLeft, offsetTop];\n};\nvar getSize = function getSize(refObj) {\n  var _ref = refObj.current || {},\n    _ref$offsetWidth = _ref.offsetWidth,\n    offsetWidth = _ref$offsetWidth === void 0 ? 0 : _ref$offsetWidth,\n    _ref$offsetHeight = _ref.offsetHeight,\n    offsetHeight = _ref$offsetHeight === void 0 ? 0 : _ref$offsetHeight;\n\n  // Use getBoundingClientRect to avoid decimal inaccuracy\n  if (refObj.current) {\n    var _refObj$current$getBo = refObj.current.getBoundingClientRect(),\n      width = _refObj$current$getBo.width,\n      height = _refObj$current$getBo.height;\n    if (Math.abs(width - offsetWidth) < 1) {\n      return [width, height];\n    }\n  }\n  return [offsetWidth, offsetHeight];\n};\n\n/**\n * Convert `SizeInfo` to unit value. Such as [123, 456] with `top` position get `123`\n */\nvar getUnitValue = function getUnitValue(size, tabPositionTopOrBottom) {\n  return size[tabPositionTopOrBottom ? 0 : 1];\n};\nvar TabNavList = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var className = props.className,\n    style = props.style,\n    id = props.id,\n    animated = props.animated,\n    activeKey = props.activeKey,\n    rtl = props.rtl,\n    extra = props.extra,\n    editable = props.editable,\n    locale = props.locale,\n    tabPosition = props.tabPosition,\n    tabBarGutter = props.tabBarGutter,\n    children = props.children,\n    onTabClick = props.onTabClick,\n    onTabScroll = props.onTabScroll,\n    indicator = props.indicator;\n  var _React$useContext = React.useContext(TabContext),\n    prefixCls = _React$useContext.prefixCls,\n    tabs = _React$useContext.tabs;\n  var containerRef = useRef(null);\n  var extraLeftRef = useRef(null);\n  var extraRightRef = useRef(null);\n  var tabsWrapperRef = useRef(null);\n  var tabListRef = useRef(null);\n  var operationsRef = useRef(null);\n  var innerAddButtonRef = useRef(null);\n  var tabPositionTopOrBottom = tabPosition === 'top' || tabPosition === 'bottom';\n  var _useSyncState = useSyncState(0, function (next, prev) {\n      if (tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'left' : 'right'\n        });\n      }\n    }),\n    _useSyncState2 = _slicedToArray(_useSyncState, 2),\n    transformLeft = _useSyncState2[0],\n    setTransformLeft = _useSyncState2[1];\n  var _useSyncState3 = useSyncState(0, function (next, prev) {\n      if (!tabPositionTopOrBottom && onTabScroll) {\n        onTabScroll({\n          direction: next > prev ? 'top' : 'bottom'\n        });\n      }\n    }),\n    _useSyncState4 = _slicedToArray(_useSyncState3, 2),\n    transformTop = _useSyncState4[0],\n    setTransformTop = _useSyncState4[1];\n  var _useState = useState([0, 0]),\n    _useState2 = _slicedToArray(_useState, 2),\n    containerExcludeExtraSize = _useState2[0],\n    setContainerExcludeExtraSize = _useState2[1];\n  var _useState3 = useState([0, 0]),\n    _useState4 = _slicedToArray(_useState3, 2),\n    tabContentSize = _useState4[0],\n    setTabContentSize = _useState4[1];\n  var _useState5 = useState([0, 0]),\n    _useState6 = _slicedToArray(_useState5, 2),\n    addSize = _useState6[0],\n    setAddSize = _useState6[1];\n  var _useState7 = useState([0, 0]),\n    _useState8 = _slicedToArray(_useState7, 2),\n    operationSize = _useState8[0],\n    setOperationSize = _useState8[1];\n  var _useUpdateState = useUpdateState(new Map()),\n    _useUpdateState2 = _slicedToArray(_useUpdateState, 2),\n    tabSizes = _useUpdateState2[0],\n    setTabSizes = _useUpdateState2[1];\n  var tabOffsets = useOffsets(tabs, tabSizes, tabContentSize[0]);\n\n  // ========================== Unit =========================\n  var containerExcludeExtraSizeValue = getUnitValue(containerExcludeExtraSize, tabPositionTopOrBottom);\n  var tabContentSizeValue = getUnitValue(tabContentSize, tabPositionTopOrBottom);\n  var addSizeValue = getUnitValue(addSize, tabPositionTopOrBottom);\n  var operationSizeValue = getUnitValue(operationSize, tabPositionTopOrBottom);\n  var needScroll = Math.floor(containerExcludeExtraSizeValue) < Math.floor(tabContentSizeValue + addSizeValue);\n  var visibleTabContentValue = needScroll ? containerExcludeExtraSizeValue - operationSizeValue : containerExcludeExtraSizeValue - addSizeValue;\n\n  // ========================== Util =========================\n  var operationsHiddenClassName = \"\".concat(prefixCls, \"-nav-operations-hidden\");\n  var transformMin = 0;\n  var transformMax = 0;\n  if (!tabPositionTopOrBottom) {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  } else if (rtl) {\n    transformMin = 0;\n    transformMax = Math.max(0, tabContentSizeValue - visibleTabContentValue);\n  } else {\n    transformMin = Math.min(0, visibleTabContentValue - tabContentSizeValue);\n    transformMax = 0;\n  }\n  function alignInRange(value) {\n    if (value < transformMin) {\n      return transformMin;\n    }\n    if (value > transformMax) {\n      return transformMax;\n    }\n    return value;\n  }\n\n  // ========================= Mobile ========================\n  var touchMovingRef = useRef(null);\n  var _useState9 = useState(),\n    _useState10 = _slicedToArray(_useState9, 2),\n    lockAnimation = _useState10[0],\n    setLockAnimation = _useState10[1];\n  function doLockAnimation() {\n    setLockAnimation(Date.now());\n  }\n  function clearTouchMoving() {\n    if (touchMovingRef.current) {\n      clearTimeout(touchMovingRef.current);\n    }\n  }\n  useTouchMove(tabsWrapperRef, function (offsetX, offsetY) {\n    function doMove(setState, offset) {\n      setState(function (value) {\n        var newValue = alignInRange(value + offset);\n        return newValue;\n      });\n    }\n\n    // Skip scroll if place is enough\n    if (!needScroll) {\n      return false;\n    }\n    if (tabPositionTopOrBottom) {\n      doMove(setTransformLeft, offsetX);\n    } else {\n      doMove(setTransformTop, offsetY);\n    }\n    clearTouchMoving();\n    doLockAnimation();\n    return true;\n  });\n  useEffect(function () {\n    clearTouchMoving();\n    if (lockAnimation) {\n      touchMovingRef.current = setTimeout(function () {\n        setLockAnimation(0);\n      }, 100);\n    }\n    return clearTouchMoving;\n  }, [lockAnimation]);\n\n  // ===================== Visible Range =====================\n  // Render tab node & collect tab offset\n  var _useVisibleRange = useVisibleRange(tabOffsets,\n    // Container\n    visibleTabContentValue,\n    // Transform\n    tabPositionTopOrBottom ? transformLeft : transformTop,\n    // Tabs\n    tabContentSizeValue,\n    // Add\n    addSizeValue,\n    // Operation\n    operationSizeValue, _objectSpread(_objectSpread({}, props), {}, {\n      tabs: tabs\n    })),\n    _useVisibleRange2 = _slicedToArray(_useVisibleRange, 2),\n    visibleStart = _useVisibleRange2[0],\n    visibleEnd = _useVisibleRange2[1];\n\n  // ========================= Scroll ========================\n  var scrollToTab = useEvent(function () {\n    var key = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : activeKey;\n    var tabOffset = tabOffsets.get(key) || {\n      width: 0,\n      height: 0,\n      left: 0,\n      right: 0,\n      top: 0\n    };\n    if (tabPositionTopOrBottom) {\n      // ============ Align with top & bottom ============\n      var newTransform = transformLeft;\n\n      // RTL\n      if (rtl) {\n        if (tabOffset.right < transformLeft) {\n          newTransform = tabOffset.right;\n        } else if (tabOffset.right + tabOffset.width > transformLeft + visibleTabContentValue) {\n          newTransform = tabOffset.right + tabOffset.width - visibleTabContentValue;\n        }\n      }\n      // LTR\n      else if (tabOffset.left < -transformLeft) {\n        newTransform = -tabOffset.left;\n      } else if (tabOffset.left + tabOffset.width > -transformLeft + visibleTabContentValue) {\n        newTransform = -(tabOffset.left + tabOffset.width - visibleTabContentValue);\n      }\n      setTransformTop(0);\n      setTransformLeft(alignInRange(newTransform));\n    } else {\n      // ============ Align with left & right ============\n      var _newTransform = transformTop;\n      if (tabOffset.top < -transformTop) {\n        _newTransform = -tabOffset.top;\n      } else if (tabOffset.top + tabOffset.height > -transformTop + visibleTabContentValue) {\n        _newTransform = -(tabOffset.top + tabOffset.height - visibleTabContentValue);\n      }\n      setTransformLeft(0);\n      setTransformTop(alignInRange(_newTransform));\n    }\n  });\n\n  // ========================= Focus =========================\n  var _useState11 = useState(),\n    _useState12 = _slicedToArray(_useState11, 2),\n    focusKey = _useState12[0],\n    setFocusKey = _useState12[1];\n  var _useState13 = useState(false),\n    _useState14 = _slicedToArray(_useState13, 2),\n    isMouse = _useState14[0],\n    setIsMouse = _useState14[1];\n  var enabledTabs = tabs.filter(function (tab) {\n    return !tab.disabled;\n  }).map(function (tab) {\n    return tab.key;\n  });\n  var onOffset = function onOffset(offset) {\n    var currentIndex = enabledTabs.indexOf(focusKey || activeKey);\n    var len = enabledTabs.length;\n    var nextIndex = (currentIndex + offset + len) % len;\n    var newKey = enabledTabs[nextIndex];\n    setFocusKey(newKey);\n  };\n  var handleKeyDown = function handleKeyDown(e) {\n    var code = e.code;\n    var isRTL = rtl && tabPositionTopOrBottom;\n    var firstEnabledTab = enabledTabs[0];\n    var lastEnabledTab = enabledTabs[enabledTabs.length - 1];\n    switch (code) {\n      // LEFT\n      case 'ArrowLeft':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? 1 : -1);\n          }\n          break;\n        }\n\n      // RIGHT\n      case 'ArrowRight':\n        {\n          if (tabPositionTopOrBottom) {\n            onOffset(isRTL ? -1 : 1);\n          }\n          break;\n        }\n\n      // UP\n      case 'ArrowUp':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(-1);\n          }\n          break;\n        }\n\n      // DOWN\n      case 'ArrowDown':\n        {\n          e.preventDefault();\n          if (!tabPositionTopOrBottom) {\n            onOffset(1);\n          }\n          break;\n        }\n\n      // HOME\n      case 'Home':\n        {\n          e.preventDefault();\n          setFocusKey(firstEnabledTab);\n          break;\n        }\n\n      // END\n      case 'End':\n        {\n          e.preventDefault();\n          setFocusKey(lastEnabledTab);\n          break;\n        }\n\n      // Enter & Space\n      case 'Enter':\n      case 'Space':\n        {\n          e.preventDefault();\n          onTabClick(focusKey !== null && focusKey !== void 0 ? focusKey : activeKey, e);\n          break;\n        }\n      // Backspace\n      case 'Backspace':\n      case 'Delete':\n        {\n          var removeIndex = enabledTabs.indexOf(focusKey);\n          var removeTab = tabs.find(function (tab) {\n            return tab.key === focusKey;\n          });\n          var removable = getRemovable(removeTab === null || removeTab === void 0 ? void 0 : removeTab.closable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.closeIcon, editable, removeTab === null || removeTab === void 0 ? void 0 : removeTab.disabled);\n          if (removable) {\n            e.preventDefault();\n            e.stopPropagation();\n            editable.onEdit('remove', {\n              key: focusKey,\n              event: e\n            });\n            // when remove last tab, focus previous tab\n            if (removeIndex === enabledTabs.length - 1) {\n              onOffset(-1);\n            } else {\n              onOffset(1);\n            }\n          }\n          break;\n        }\n    }\n  };\n\n  // ========================== Tab ==========================\n  var tabNodeStyle = {};\n  if (tabPositionTopOrBottom) {\n    tabNodeStyle[rtl ? 'marginRight' : 'marginLeft'] = tabBarGutter;\n  } else {\n    tabNodeStyle.marginTop = tabBarGutter;\n  }\n  var tabNodes = tabs.map(function (tab, i) {\n    var key = tab.key;\n    return /*#__PURE__*/React.createElement(TabNode, {\n      id: id,\n      prefixCls: prefixCls,\n      key: key,\n      tab: tab\n      /* first node should not have margin left */,\n      style: i === 0 ? undefined : tabNodeStyle,\n      closable: tab.closable,\n      editable: editable,\n      active: key === activeKey,\n      focus: key === focusKey,\n      renderWrapper: children,\n      removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n      tabCount: enabledTabs.length,\n      currentPosition: i + 1,\n      onClick: function onClick(e) {\n        onTabClick(key, e);\n      },\n      onKeyDown: handleKeyDown,\n      onFocus: function onFocus() {\n        if (!isMouse) {\n          setFocusKey(key);\n        }\n        scrollToTab(key);\n        doLockAnimation();\n        if (!tabsWrapperRef.current) {\n          return;\n        }\n        // Focus element will make scrollLeft change which we should reset back\n        if (!rtl) {\n          tabsWrapperRef.current.scrollLeft = 0;\n        }\n        tabsWrapperRef.current.scrollTop = 0;\n      },\n      onBlur: function onBlur() {\n        setFocusKey(undefined);\n      },\n      onMouseDown: function onMouseDown() {\n        setIsMouse(true);\n      },\n      onMouseUp: function onMouseUp() {\n        setIsMouse(false);\n      }\n    });\n  });\n\n  // Update buttons records\n  var updateTabSizes = function updateTabSizes() {\n    return setTabSizes(function () {\n      var _tabListRef$current;\n      var newSizes = new Map();\n      var listRect = (_tabListRef$current = tabListRef.current) === null || _tabListRef$current === void 0 ? void 0 : _tabListRef$current.getBoundingClientRect();\n      tabs.forEach(function (_ref2) {\n        var _tabListRef$current2;\n        var key = _ref2.key;\n        var btnNode = (_tabListRef$current2 = tabListRef.current) === null || _tabListRef$current2 === void 0 ? void 0 : _tabListRef$current2.querySelector(\"[data-node-key=\\\"\".concat(genDataNodeKey(key), \"\\\"]\"));\n        if (btnNode) {\n          var _getTabSize = getTabSize(btnNode, listRect),\n            _getTabSize2 = _slicedToArray(_getTabSize, 4),\n            width = _getTabSize2[0],\n            height = _getTabSize2[1],\n            left = _getTabSize2[2],\n            top = _getTabSize2[3];\n          newSizes.set(key, {\n            width: width,\n            height: height,\n            left: left,\n            top: top\n          });\n        }\n      });\n      return newSizes;\n    });\n  };\n  useEffect(function () {\n    updateTabSizes();\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_')]);\n  var onListHolderResize = useUpdate(function () {\n    // Update wrapper records\n    var containerSize = getSize(containerRef);\n    var extraLeftSize = getSize(extraLeftRef);\n    var extraRightSize = getSize(extraRightRef);\n    setContainerExcludeExtraSize([containerSize[0] - extraLeftSize[0] - extraRightSize[0], containerSize[1] - extraLeftSize[1] - extraRightSize[1]]);\n    var newAddSize = getSize(innerAddButtonRef);\n    setAddSize(newAddSize);\n    var newOperationSize = getSize(operationsRef);\n    setOperationSize(newOperationSize);\n\n    // Which includes add button size\n    var tabContentFullSize = getSize(tabListRef);\n    setTabContentSize([tabContentFullSize[0] - newAddSize[0], tabContentFullSize[1] - newAddSize[1]]);\n\n    // Update buttons records\n    updateTabSizes();\n  });\n\n  // ======================== Dropdown =======================\n  var startHiddenTabs = tabs.slice(0, visibleStart);\n  var endHiddenTabs = tabs.slice(visibleEnd + 1);\n  var hiddenTabs = [].concat(_toConsumableArray(startHiddenTabs), _toConsumableArray(endHiddenTabs));\n\n  // =================== Link & Operations ===================\n  var activeTabOffset = tabOffsets.get(activeKey);\n  var _useIndicator = useIndicator({\n      activeTabOffset: activeTabOffset,\n      horizontal: tabPositionTopOrBottom,\n      indicator: indicator,\n      rtl: rtl\n    }),\n    indicatorStyle = _useIndicator.style;\n\n  // ========================= Effect ========================\n  useEffect(function () {\n    scrollToTab();\n  }, [activeKey, transformMin, transformMax, stringify(activeTabOffset), stringify(tabOffsets), tabPositionTopOrBottom]);\n\n  // Should recalculate when rtl changed\n  useEffect(function () {\n    onListHolderResize();\n    // eslint-disable-next-line\n  }, [rtl]);\n\n  // ========================= Render ========================\n  var hasDropdown = !!hiddenTabs.length;\n  var wrapPrefix = \"\".concat(prefixCls, \"-nav-wrap\");\n  var pingLeft;\n  var pingRight;\n  var pingTop;\n  var pingBottom;\n  if (tabPositionTopOrBottom) {\n    if (rtl) {\n      pingRight = transformLeft > 0;\n      pingLeft = transformLeft !== transformMax;\n    } else {\n      pingLeft = transformLeft < 0;\n      pingRight = transformLeft !== transformMin;\n    }\n  } else {\n    pingTop = transformTop < 0;\n    pingBottom = transformTop !== transformMin;\n  }\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: useComposeRef(ref, containerRef),\n    role: \"tablist\",\n    \"aria-orientation\": tabPositionTopOrBottom ? 'horizontal' : 'vertical',\n    className: classNames(\"\".concat(prefixCls, \"-nav\"), className),\n    style: style,\n    onKeyDown: function onKeyDown() {\n      // No need animation when use keyboard\n      doLockAnimation();\n    }\n  }, /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraLeftRef,\n    position: \"left\",\n    extra: extra,\n    prefixCls: prefixCls\n  }), /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(wrapPrefix, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, \"\".concat(wrapPrefix, \"-ping-left\"), pingLeft), \"\".concat(wrapPrefix, \"-ping-right\"), pingRight), \"\".concat(wrapPrefix, \"-ping-top\"), pingTop), \"\".concat(wrapPrefix, \"-ping-bottom\"), pingBottom)),\n    ref: tabsWrapperRef\n  }, /*#__PURE__*/React.createElement(ResizeObserver, {\n    onResize: onListHolderResize\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: tabListRef,\n    className: \"\".concat(prefixCls, \"-nav-list\"),\n    style: {\n      transform: \"translate(\".concat(transformLeft, \"px, \").concat(transformTop, \"px)\"),\n      transition: lockAnimation ? 'none' : undefined\n    }\n  }, tabNodes, /*#__PURE__*/React.createElement(AddButton, {\n    ref: innerAddButtonRef,\n    prefixCls: prefixCls,\n    locale: locale,\n    editable: editable,\n    style: _objectSpread(_objectSpread({}, tabNodes.length === 0 ? undefined : tabNodeStyle), {}, {\n      visibility: hasDropdown ? 'hidden' : null\n    })\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: classNames(\"\".concat(prefixCls, \"-ink-bar\"), _defineProperty({}, \"\".concat(prefixCls, \"-ink-bar-animated\"), animated.inkBar)),\n    style: indicatorStyle\n  }))))), /*#__PURE__*/React.createElement(OperationNode, _extends({}, props, {\n    removeAriaLabel: locale === null || locale === void 0 ? void 0 : locale.removeAriaLabel,\n    ref: operationsRef,\n    prefixCls: prefixCls,\n    tabs: hiddenTabs,\n    className: !hasDropdown && operationsHiddenClassName,\n    tabMoving: !!lockAnimation\n  })), /*#__PURE__*/React.createElement(ExtraContent, {\n    ref: extraRightRef,\n    position: \"right\",\n    extra: extra,\n    prefixCls: prefixCls\n  })));\n  /* eslint-enable */\n});\nexport default TabNavList;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,SAAS,IAAIC,cAAc,QAAQ,oBAAoB;AAC9D,OAAOC,eAAe,MAAM,0BAA0B;AACtD,SAASC,cAAc,EAAEC,YAAY,EAAEC,SAAS,QAAQ,SAAS;AACjE,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAC/B,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,GAAG,EAAEC,aAAa,EAAE;EACvD;EACA,IAAIC,WAAW,GAAGF,GAAG,CAACE,WAAW;IAC/BC,YAAY,GAAGH,GAAG,CAACG,YAAY;IAC/BC,SAAS,GAAGJ,GAAG,CAACI,SAAS;IACzBC,UAAU,GAAGL,GAAG,CAACK,UAAU;EAC7B,IAAIC,qBAAqB,GAAGN,GAAG,CAACO,qBAAqB,CAAC,CAAC;IACrDC,KAAK,GAAGF,qBAAqB,CAACE,KAAK;IACnCC,MAAM,GAAGH,qBAAqB,CAACG,MAAM;IACrCC,IAAI,GAAGJ,qBAAqB,CAACI,IAAI;IACjCC,GAAG,GAAGL,qBAAqB,CAACK,GAAG;;EAEjC;EACA,IAAIC,IAAI,CAACC,GAAG,CAACL,KAAK,GAAGN,WAAW,CAAC,GAAG,CAAC,EAAE;IACrC,OAAO,CAACM,KAAK,EAAEC,MAAM,EAAEC,IAAI,GAAGT,aAAa,CAACS,IAAI,EAAEC,GAAG,GAAGV,aAAa,CAACU,GAAG,CAAC;EAC5E;EACA,OAAO,CAACT,WAAW,EAAEC,YAAY,EAAEE,UAAU,EAAED,SAAS,CAAC;AAC3D,CAAC;AACD,IAAIU,OAAO,GAAG,SAASA,OAAOA,CAACC,MAAM,EAAE;EACrC,IAAIC,IAAI,GAAGD,MAAM,CAACE,OAAO,IAAI,CAAC,CAAC;IAC7BC,gBAAgB,GAAGF,IAAI,CAACd,WAAW;IACnCA,WAAW,GAAGgB,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;IAChEC,iBAAiB,GAAGH,IAAI,CAACb,YAAY;IACrCA,YAAY,GAAGgB,iBAAiB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,iBAAiB;;EAErE;EACA,IAAIJ,MAAM,CAACE,OAAO,EAAE;IAClB,IAAIG,qBAAqB,GAAGL,MAAM,CAACE,OAAO,CAACV,qBAAqB,CAAC,CAAC;MAChEC,KAAK,GAAGY,qBAAqB,CAACZ,KAAK;MACnCC,MAAM,GAAGW,qBAAqB,CAACX,MAAM;IACvC,IAAIG,IAAI,CAACC,GAAG,CAACL,KAAK,GAAGN,WAAW,CAAC,GAAG,CAAC,EAAE;MACrC,OAAO,CAACM,KAAK,EAAEC,MAAM,CAAC;IACxB;EACF;EACA,OAAO,CAACP,WAAW,EAAEC,YAAY,CAAC;AACpC,CAAC;;AAED;AACA;AACA;AACA,IAAIkB,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,sBAAsB,EAAE;EACrE,OAAOD,IAAI,CAACC,sBAAsB,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7C,CAAC;AACD,IAAIC,UAAU,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EACnE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,EAAE,GAAGJ,KAAK,CAACI,EAAE;IACbC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,SAAS,GAAGN,KAAK,CAACM,SAAS;IAC3BC,GAAG,GAAGP,KAAK,CAACO,GAAG;IACfC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,QAAQ,GAAGT,KAAK,CAACS,QAAQ;IACzBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,WAAW,GAAGX,KAAK,CAACW,WAAW;IAC/BC,YAAY,GAAGZ,KAAK,CAACY,YAAY;IACjCC,QAAQ,GAAGb,KAAK,CAACa,QAAQ;IACzBC,UAAU,GAAGd,KAAK,CAACc,UAAU;IAC7BC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;EAC7B,IAAIC,iBAAiB,GAAG/D,KAAK,CAACgE,UAAU,CAAC5D,UAAU,CAAC;IAClD6D,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,IAAI,GAAGH,iBAAiB,CAACG,IAAI;EAC/B,IAAIC,YAAY,GAAGjE,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAIkE,YAAY,GAAGlE,MAAM,CAAC,IAAI,CAAC;EAC/B,IAAImE,aAAa,GAAGnE,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIoE,cAAc,GAAGpE,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIqE,UAAU,GAAGrE,MAAM,CAAC,IAAI,CAAC;EAC7B,IAAIsE,aAAa,GAAGtE,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIuE,iBAAiB,GAAGvE,MAAM,CAAC,IAAI,CAAC;EACpC,IAAIyC,sBAAsB,GAAGc,WAAW,KAAK,KAAK,IAAIA,WAAW,KAAK,QAAQ;EAC9E,IAAIiB,aAAa,GAAGnE,YAAY,CAAC,CAAC,EAAE,UAAUoE,IAAI,EAAEC,IAAI,EAAE;MACtD,IAAIjC,sBAAsB,IAAIkB,WAAW,EAAE;QACzCA,WAAW,CAAC;UACVgB,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,MAAM,GAAG;QACpC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFE,cAAc,GAAGnF,cAAc,CAAC+E,aAAa,EAAE,CAAC,CAAC;IACjDK,aAAa,GAAGD,cAAc,CAAC,CAAC,CAAC;IACjCE,gBAAgB,GAAGF,cAAc,CAAC,CAAC,CAAC;EACtC,IAAIG,cAAc,GAAG1E,YAAY,CAAC,CAAC,EAAE,UAAUoE,IAAI,EAAEC,IAAI,EAAE;MACvD,IAAI,CAACjC,sBAAsB,IAAIkB,WAAW,EAAE;QAC1CA,WAAW,CAAC;UACVgB,SAAS,EAAEF,IAAI,GAAGC,IAAI,GAAG,KAAK,GAAG;QACnC,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IACFM,cAAc,GAAGvF,cAAc,CAACsF,cAAc,EAAE,CAAC,CAAC;IAClDE,YAAY,GAAGD,cAAc,CAAC,CAAC,CAAC;IAChCE,eAAe,GAAGF,cAAc,CAAC,CAAC,CAAC;EACrC,IAAIG,SAAS,GAAGlF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9BmF,UAAU,GAAG3F,cAAc,CAAC0F,SAAS,EAAE,CAAC,CAAC;IACzCE,yBAAyB,GAAGD,UAAU,CAAC,CAAC,CAAC;IACzCE,4BAA4B,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC9C,IAAIG,UAAU,GAAGtF,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/BuF,UAAU,GAAG/F,cAAc,CAAC8F,UAAU,EAAE,CAAC,CAAC;IAC1CE,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC9BE,iBAAiB,GAAGF,UAAU,CAAC,CAAC,CAAC;EACnC,IAAIG,UAAU,GAAG1F,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B2F,UAAU,GAAGnG,cAAc,CAACkG,UAAU,EAAE,CAAC,CAAC;IAC1CE,OAAO,GAAGD,UAAU,CAAC,CAAC,CAAC;IACvBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC5B,IAAIG,UAAU,GAAG9F,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC/B+F,UAAU,GAAGvG,cAAc,CAACsG,UAAU,EAAE,CAAC,CAAC;IAC1CE,aAAa,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC7BE,gBAAgB,GAAGF,UAAU,CAAC,CAAC,CAAC;EAClC,IAAIG,eAAe,GAAG3F,cAAc,CAAC,IAAI4F,GAAG,CAAC,CAAC,CAAC;IAC7CC,gBAAgB,GAAG5G,cAAc,CAAC0G,eAAe,EAAE,CAAC,CAAC;IACrDG,QAAQ,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC9BE,WAAW,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACnC,IAAIG,UAAU,GAAGpG,UAAU,CAAC4D,IAAI,EAAEsC,QAAQ,EAAEb,cAAc,CAAC,CAAC,CAAC,CAAC;;EAE9D;EACA,IAAIgB,8BAA8B,GAAGlE,YAAY,CAAC8C,yBAAyB,EAAE5C,sBAAsB,CAAC;EACpG,IAAIiE,mBAAmB,GAAGnE,YAAY,CAACkD,cAAc,EAAEhD,sBAAsB,CAAC;EAC9E,IAAIkE,YAAY,GAAGpE,YAAY,CAACsD,OAAO,EAAEpD,sBAAsB,CAAC;EAChE,IAAImE,kBAAkB,GAAGrE,YAAY,CAAC0D,aAAa,EAAExD,sBAAsB,CAAC;EAC5E,IAAIoE,UAAU,GAAG/E,IAAI,CAACgF,KAAK,CAACL,8BAA8B,CAAC,GAAG3E,IAAI,CAACgF,KAAK,CAACJ,mBAAmB,GAAGC,YAAY,CAAC;EAC5G,IAAII,sBAAsB,GAAGF,UAAU,GAAGJ,8BAA8B,GAAGG,kBAAkB,GAAGH,8BAA8B,GAAGE,YAAY;;EAE7I;EACA,IAAIK,yBAAyB,GAAG,EAAE,CAACC,MAAM,CAAClD,SAAS,EAAE,wBAAwB,CAAC;EAC9E,IAAImD,YAAY,GAAG,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAI,CAAC1E,sBAAsB,EAAE;IAC3ByE,YAAY,GAAGpF,IAAI,CAACsF,GAAG,CAAC,CAAC,EAAEL,sBAAsB,GAAGL,mBAAmB,CAAC;IACxES,YAAY,GAAG,CAAC;EAClB,CAAC,MAAM,IAAIhE,GAAG,EAAE;IACd+D,YAAY,GAAG,CAAC;IAChBC,YAAY,GAAGrF,IAAI,CAACuF,GAAG,CAAC,CAAC,EAAEX,mBAAmB,GAAGK,sBAAsB,CAAC;EAC1E,CAAC,MAAM;IACLG,YAAY,GAAGpF,IAAI,CAACsF,GAAG,CAAC,CAAC,EAAEL,sBAAsB,GAAGL,mBAAmB,CAAC;IACxES,YAAY,GAAG,CAAC;EAClB;EACA,SAASG,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIA,KAAK,GAAGL,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IACA,IAAIK,KAAK,GAAGJ,YAAY,EAAE;MACxB,OAAOA,YAAY;IACrB;IACA,OAAOI,KAAK;EACd;;EAEA;EACA,IAAIC,cAAc,GAAGxH,MAAM,CAAC,IAAI,CAAC;EACjC,IAAIyH,UAAU,GAAGxH,QAAQ,CAAC,CAAC;IACzByH,WAAW,GAAGjI,cAAc,CAACgI,UAAU,EAAE,CAAC,CAAC;IAC3CE,aAAa,GAAGD,WAAW,CAAC,CAAC,CAAC;IAC9BE,gBAAgB,GAAGF,WAAW,CAAC,CAAC,CAAC;EACnC,SAASG,eAAeA,CAAA,EAAG;IACzBD,gBAAgB,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;EAC9B;EACA,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,IAAIR,cAAc,CAACrF,OAAO,EAAE;MAC1B8F,YAAY,CAACT,cAAc,CAACrF,OAAO,CAAC;IACtC;EACF;EACA7B,YAAY,CAAC8D,cAAc,EAAE,UAAU8D,OAAO,EAAEC,OAAO,EAAE;IACvD,SAASC,MAAMA,CAACC,QAAQ,EAAEC,MAAM,EAAE;MAChCD,QAAQ,CAAC,UAAUd,KAAK,EAAE;QACxB,IAAIgB,QAAQ,GAAGjB,YAAY,CAACC,KAAK,GAAGe,MAAM,CAAC;QAC3C,OAAOC,QAAQ;MACjB,CAAC,CAAC;IACJ;;IAEA;IACA,IAAI,CAAC1B,UAAU,EAAE;MACf,OAAO,KAAK;IACd;IACA,IAAIpE,sBAAsB,EAAE;MAC1B2F,MAAM,CAACtD,gBAAgB,EAAEoD,OAAO,CAAC;IACnC,CAAC,MAAM;MACLE,MAAM,CAAClD,eAAe,EAAEiD,OAAO,CAAC;IAClC;IACAH,gBAAgB,CAAC,CAAC;IAClBH,eAAe,CAAC,CAAC;IACjB,OAAO,IAAI;EACb,CAAC,CAAC;EACF9H,SAAS,CAAC,YAAY;IACpBiI,gBAAgB,CAAC,CAAC;IAClB,IAAIL,aAAa,EAAE;MACjBH,cAAc,CAACrF,OAAO,GAAGqG,UAAU,CAAC,YAAY;QAC9CZ,gBAAgB,CAAC,CAAC,CAAC;MACrB,CAAC,EAAE,GAAG,CAAC;IACT;IACA,OAAOI,gBAAgB;EACzB,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC;;EAEnB;EACA;EACA,IAAIc,gBAAgB,GAAGhI,eAAe,CAAC+F,UAAU;IAC/C;IACAO,sBAAsB;IACtB;IACAtE,sBAAsB,GAAGoC,aAAa,GAAGI,YAAY;IACrD;IACAyB,mBAAmB;IACnB;IACAC,YAAY;IACZ;IACAC,kBAAkB,EAAEpH,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;MAC9DoB,IAAI,EAAEA;IACR,CAAC,CAAC,CAAC;IACH0E,iBAAiB,GAAGjJ,cAAc,CAACgJ,gBAAgB,EAAE,CAAC,CAAC;IACvDE,YAAY,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IACnCE,UAAU,GAAGF,iBAAiB,CAAC,CAAC,CAAC;;EAEnC;EACA,IAAIG,WAAW,GAAGjJ,QAAQ,CAAC,YAAY;IACrC,IAAIkJ,GAAG,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG7F,SAAS;IACvF,IAAIgG,SAAS,GAAG1C,UAAU,CAAC2C,GAAG,CAACL,GAAG,CAAC,IAAI;MACrCpH,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE,CAAC;MACPwH,KAAK,EAAE,CAAC;MACRvH,GAAG,EAAE;IACP,CAAC;IACD,IAAIY,sBAAsB,EAAE;MAC1B;MACA,IAAI4G,YAAY,GAAGxE,aAAa;;MAEhC;MACA,IAAI1B,GAAG,EAAE;QACP,IAAI+F,SAAS,CAACE,KAAK,GAAGvE,aAAa,EAAE;UACnCwE,YAAY,GAAGH,SAAS,CAACE,KAAK;QAChC,CAAC,MAAM,IAAIF,SAAS,CAACE,KAAK,GAAGF,SAAS,CAACxH,KAAK,GAAGmD,aAAa,GAAGkC,sBAAsB,EAAE;UACrFsC,YAAY,GAAGH,SAAS,CAACE,KAAK,GAAGF,SAAS,CAACxH,KAAK,GAAGqF,sBAAsB;QAC3E;MACF;MACA;MAAA,KACK,IAAImC,SAAS,CAACtH,IAAI,GAAG,CAACiD,aAAa,EAAE;QACxCwE,YAAY,GAAG,CAACH,SAAS,CAACtH,IAAI;MAChC,CAAC,MAAM,IAAIsH,SAAS,CAACtH,IAAI,GAAGsH,SAAS,CAACxH,KAAK,GAAG,CAACmD,aAAa,GAAGkC,sBAAsB,EAAE;QACrFsC,YAAY,GAAG,EAAEH,SAAS,CAACtH,IAAI,GAAGsH,SAAS,CAACxH,KAAK,GAAGqF,sBAAsB,CAAC;MAC7E;MACA7B,eAAe,CAAC,CAAC,CAAC;MAClBJ,gBAAgB,CAACwC,YAAY,CAAC+B,YAAY,CAAC,CAAC;IAC9C,CAAC,MAAM;MACL;MACA,IAAIC,aAAa,GAAGrE,YAAY;MAChC,IAAIiE,SAAS,CAACrH,GAAG,GAAG,CAACoD,YAAY,EAAE;QACjCqE,aAAa,GAAG,CAACJ,SAAS,CAACrH,GAAG;MAChC,CAAC,MAAM,IAAIqH,SAAS,CAACrH,GAAG,GAAGqH,SAAS,CAACvH,MAAM,GAAG,CAACsD,YAAY,GAAG8B,sBAAsB,EAAE;QACpFuC,aAAa,GAAG,EAAEJ,SAAS,CAACrH,GAAG,GAAGqH,SAAS,CAACvH,MAAM,GAAGoF,sBAAsB,CAAC;MAC9E;MACAjC,gBAAgB,CAAC,CAAC,CAAC;MACnBI,eAAe,CAACoC,YAAY,CAACgC,aAAa,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC;;EAEF;EACA,IAAIC,WAAW,GAAGtJ,QAAQ,CAAC,CAAC;IAC1BuJ,WAAW,GAAG/J,cAAc,CAAC8J,WAAW,EAAE,CAAC,CAAC;IAC5CE,QAAQ,GAAGD,WAAW,CAAC,CAAC,CAAC;IACzBE,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAC9B,IAAIG,WAAW,GAAG1J,QAAQ,CAAC,KAAK,CAAC;IAC/B2J,WAAW,GAAGnK,cAAc,CAACkK,WAAW,EAAE,CAAC,CAAC;IAC5CE,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC;IACxBE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;EAC7B,IAAIG,WAAW,GAAG/F,IAAI,CAACgG,MAAM,CAAC,UAAU9I,GAAG,EAAE;IAC3C,OAAO,CAACA,GAAG,CAAC+I,QAAQ;EACtB,CAAC,CAAC,CAACC,GAAG,CAAC,UAAUhJ,GAAG,EAAE;IACpB,OAAOA,GAAG,CAAC4H,GAAG;EAChB,CAAC,CAAC;EACF,IAAIqB,QAAQ,GAAG,SAASA,QAAQA,CAAC7B,MAAM,EAAE;IACvC,IAAI8B,YAAY,GAAGL,WAAW,CAACM,OAAO,CAACZ,QAAQ,IAAIvG,SAAS,CAAC;IAC7D,IAAIoH,GAAG,GAAGP,WAAW,CAACf,MAAM;IAC5B,IAAIuB,SAAS,GAAG,CAACH,YAAY,GAAG9B,MAAM,GAAGgC,GAAG,IAAIA,GAAG;IACnD,IAAIE,MAAM,GAAGT,WAAW,CAACQ,SAAS,CAAC;IACnCb,WAAW,CAACc,MAAM,CAAC;EACrB,CAAC;EACD,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,CAAC,EAAE;IAC5C,IAAIC,IAAI,GAAGD,CAAC,CAACC,IAAI;IACjB,IAAIC,KAAK,GAAGzH,GAAG,IAAIV,sBAAsB;IACzC,IAAIoI,eAAe,GAAGd,WAAW,CAAC,CAAC,CAAC;IACpC,IAAIe,cAAc,GAAGf,WAAW,CAACA,WAAW,CAACf,MAAM,GAAG,CAAC,CAAC;IACxD,QAAQ2B,IAAI;MACV;MACA,KAAK,WAAW;QACd;UACE,IAAIlI,sBAAsB,EAAE;YAC1B0H,QAAQ,CAACS,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;UAC1B;UACA;QACF;;MAEF;MACA,KAAK,YAAY;QACf;UACE,IAAInI,sBAAsB,EAAE;YAC1B0H,QAAQ,CAACS,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;UAC1B;UACA;QACF;;MAEF;MACA,KAAK,SAAS;QACZ;UACEF,CAAC,CAACK,cAAc,CAAC,CAAC;UAClB,IAAI,CAACtI,sBAAsB,EAAE;YAC3B0H,QAAQ,CAAC,CAAC,CAAC,CAAC;UACd;UACA;QACF;;MAEF;MACA,KAAK,WAAW;QACd;UACEO,CAAC,CAACK,cAAc,CAAC,CAAC;UAClB,IAAI,CAACtI,sBAAsB,EAAE;YAC3B0H,QAAQ,CAAC,CAAC,CAAC;UACb;UACA;QACF;;MAEF;MACA,KAAK,MAAM;QACT;UACEO,CAAC,CAACK,cAAc,CAAC,CAAC;UAClBrB,WAAW,CAACmB,eAAe,CAAC;UAC5B;QACF;;MAEF;MACA,KAAK,KAAK;QACR;UACEH,CAAC,CAACK,cAAc,CAAC,CAAC;UAClBrB,WAAW,CAACoB,cAAc,CAAC;UAC3B;QACF;;MAEF;MACA,KAAK,OAAO;MACZ,KAAK,OAAO;QACV;UACEJ,CAAC,CAACK,cAAc,CAAC,CAAC;UAClBrH,UAAU,CAAC+F,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGvG,SAAS,EAAEwH,CAAC,CAAC;UAC9E;QACF;MACF;MACA,KAAK,WAAW;MAChB,KAAK,QAAQ;QACX;UACE,IAAIM,WAAW,GAAGjB,WAAW,CAACM,OAAO,CAACZ,QAAQ,CAAC;UAC/C,IAAIwB,SAAS,GAAGjH,IAAI,CAACkH,IAAI,CAAC,UAAUhK,GAAG,EAAE;YACvC,OAAOA,GAAG,CAAC4H,GAAG,KAAKW,QAAQ;UAC7B,CAAC,CAAC;UACF,IAAI0B,SAAS,GAAGxK,YAAY,CAACsK,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACG,QAAQ,EAAEH,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACI,SAAS,EAAEhI,QAAQ,EAAE4H,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAChB,QAAQ,CAAC;UACrQ,IAAIkB,SAAS,EAAE;YACbT,CAAC,CAACK,cAAc,CAAC,CAAC;YAClBL,CAAC,CAACY,eAAe,CAAC,CAAC;YACnBjI,QAAQ,CAACkI,MAAM,CAAC,QAAQ,EAAE;cACxBzC,GAAG,EAAEW,QAAQ;cACb+B,KAAK,EAAEd;YACT,CAAC,CAAC;YACF;YACA,IAAIM,WAAW,KAAKjB,WAAW,CAACf,MAAM,GAAG,CAAC,EAAE;cAC1CmB,QAAQ,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,MAAM;cACLA,QAAQ,CAAC,CAAC,CAAC;YACb;UACF;UACA;QACF;IACJ;EACF,CAAC;;EAED;EACA,IAAIsB,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIhJ,sBAAsB,EAAE;IAC1BgJ,YAAY,CAACtI,GAAG,GAAG,aAAa,GAAG,YAAY,CAAC,GAAGK,YAAY;EACjE,CAAC,MAAM;IACLiI,YAAY,CAACC,SAAS,GAAGlI,YAAY;EACvC;EACA,IAAImI,QAAQ,GAAG3H,IAAI,CAACkG,GAAG,CAAC,UAAUhJ,GAAG,EAAE0K,CAAC,EAAE;IACxC,IAAI9C,GAAG,GAAG5H,GAAG,CAAC4H,GAAG;IACjB,OAAO,aAAahJ,KAAK,CAAC+L,aAAa,CAAC7K,OAAO,EAAE;MAC/CgC,EAAE,EAAEA,EAAE;MACNe,SAAS,EAAEA,SAAS;MACpB+E,GAAG,EAAEA,GAAG;MACR5H,GAAG,EAAEA;MACL;MACA6B,KAAK,EAAE6I,CAAC,KAAK,CAAC,GAAG3C,SAAS,GAAGwC,YAAY;MACzCL,QAAQ,EAAElK,GAAG,CAACkK,QAAQ;MACtB/H,QAAQ,EAAEA,QAAQ;MAClByI,MAAM,EAAEhD,GAAG,KAAK5F,SAAS;MACzB6I,KAAK,EAAEjD,GAAG,KAAKW,QAAQ;MACvBuC,aAAa,EAAEvI,QAAQ;MACvBwI,eAAe,EAAE3I,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2I,eAAe;MACvFC,QAAQ,EAAEnC,WAAW,CAACf,MAAM;MAC5BmD,eAAe,EAAEP,CAAC,GAAG,CAAC;MACtBQ,OAAO,EAAE,SAASA,OAAOA,CAAC1B,CAAC,EAAE;QAC3BhH,UAAU,CAACoF,GAAG,EAAE4B,CAAC,CAAC;MACpB,CAAC;MACD2B,SAAS,EAAE5B,aAAa;MACxB6B,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;QAC1B,IAAI,CAACzC,OAAO,EAAE;UACZH,WAAW,CAACZ,GAAG,CAAC;QAClB;QACAD,WAAW,CAACC,GAAG,CAAC;QAChBjB,eAAe,CAAC,CAAC;QACjB,IAAI,CAACzD,cAAc,CAACjC,OAAO,EAAE;UAC3B;QACF;QACA;QACA,IAAI,CAACgB,GAAG,EAAE;UACRiB,cAAc,CAACjC,OAAO,CAACoK,UAAU,GAAG,CAAC;QACvC;QACAnI,cAAc,CAACjC,OAAO,CAACqK,SAAS,GAAG,CAAC;MACtC,CAAC;MACDC,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;QACxB/C,WAAW,CAACT,SAAS,CAAC;MACxB,CAAC;MACDyD,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;QAClC5C,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC;MACD6C,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;QAC9B7C,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,IAAI8C,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAG;IAC7C,OAAOrG,WAAW,CAAC,YAAY;MAC7B,IAAIsG,mBAAmB;MACvB,IAAIC,QAAQ,GAAG,IAAI1G,GAAG,CAAC,CAAC;MACxB,IAAI2G,QAAQ,GAAG,CAACF,mBAAmB,GAAGxI,UAAU,CAAClC,OAAO,MAAM,IAAI,IAAI0K,mBAAmB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACpL,qBAAqB,CAAC,CAAC;MAC3JuC,IAAI,CAACgJ,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC5B,IAAIC,oBAAoB;QACxB,IAAIpE,GAAG,GAAGmE,KAAK,CAACnE,GAAG;QACnB,IAAIqE,OAAO,GAAG,CAACD,oBAAoB,GAAG7I,UAAU,CAAClC,OAAO,MAAM,IAAI,IAAI+K,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACE,aAAa,CAAC,mBAAmB,CAACnG,MAAM,CAACvG,cAAc,CAACoI,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;QAC3M,IAAIqE,OAAO,EAAE;UACX,IAAIE,WAAW,GAAGpM,UAAU,CAACkM,OAAO,EAAEJ,QAAQ,CAAC;YAC7CO,YAAY,GAAG7N,cAAc,CAAC4N,WAAW,EAAE,CAAC,CAAC;YAC7C3L,KAAK,GAAG4L,YAAY,CAAC,CAAC,CAAC;YACvB3L,MAAM,GAAG2L,YAAY,CAAC,CAAC,CAAC;YACxB1L,IAAI,GAAG0L,YAAY,CAAC,CAAC,CAAC;YACtBzL,GAAG,GAAGyL,YAAY,CAAC,CAAC,CAAC;UACvBR,QAAQ,CAACS,GAAG,CAACzE,GAAG,EAAE;YAChBpH,KAAK,EAAEA,KAAK;YACZC,MAAM,EAAEA,MAAM;YACdC,IAAI,EAAEA,IAAI;YACVC,GAAG,EAAEA;UACP,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MACF,OAAOiL,QAAQ;IACjB,CAAC,CAAC;EACJ,CAAC;EACD/M,SAAS,CAAC,YAAY;IACpB6M,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC5I,IAAI,CAACkG,GAAG,CAAC,UAAUhJ,GAAG,EAAE;IAC1B,OAAOA,GAAG,CAAC4H,GAAG;EAChB,CAAC,CAAC,CAAC0E,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;EACd,IAAIC,kBAAkB,GAAGlN,SAAS,CAAC,YAAY;IAC7C;IACA,IAAImN,aAAa,GAAG1L,OAAO,CAACiC,YAAY,CAAC;IACzC,IAAI0J,aAAa,GAAG3L,OAAO,CAACkC,YAAY,CAAC;IACzC,IAAI0J,cAAc,GAAG5L,OAAO,CAACmC,aAAa,CAAC;IAC3CmB,4BAA4B,CAAC,CAACoI,aAAa,CAAC,CAAC,CAAC,GAAGC,aAAa,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,EAAEF,aAAa,CAAC,CAAC,CAAC,GAAGC,aAAa,CAAC,CAAC,CAAC,GAAGC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAChJ,IAAIC,UAAU,GAAG7L,OAAO,CAACuC,iBAAiB,CAAC;IAC3CuB,UAAU,CAAC+H,UAAU,CAAC;IACtB,IAAIC,gBAAgB,GAAG9L,OAAO,CAACsC,aAAa,CAAC;IAC7C4B,gBAAgB,CAAC4H,gBAAgB,CAAC;;IAElC;IACA,IAAIC,kBAAkB,GAAG/L,OAAO,CAACqC,UAAU,CAAC;IAC5CqB,iBAAiB,CAAC,CAACqI,kBAAkB,CAAC,CAAC,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,EAAEE,kBAAkB,CAAC,CAAC,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEjG;IACAjB,cAAc,CAAC,CAAC;EAClB,CAAC,CAAC;;EAEF;EACA,IAAIoB,eAAe,GAAGhK,IAAI,CAACiK,KAAK,CAAC,CAAC,EAAEtF,YAAY,CAAC;EACjD,IAAIuF,aAAa,GAAGlK,IAAI,CAACiK,KAAK,CAACrF,UAAU,GAAG,CAAC,CAAC;EAC9C,IAAIuF,UAAU,GAAG,EAAE,CAAClH,MAAM,CAAC1H,kBAAkB,CAACyO,eAAe,CAAC,EAAEzO,kBAAkB,CAAC2O,aAAa,CAAC,CAAC;;EAElG;EACA,IAAIE,eAAe,GAAG5H,UAAU,CAAC2C,GAAG,CAACjG,SAAS,CAAC;EAC/C,IAAImL,aAAa,GAAGlO,YAAY,CAAC;MAC7BiO,eAAe,EAAEA,eAAe;MAChCE,UAAU,EAAE7L,sBAAsB;MAClCmB,SAAS,EAAEA,SAAS;MACpBT,GAAG,EAAEA;IACP,CAAC,CAAC;IACFoL,cAAc,GAAGF,aAAa,CAACtL,KAAK;;EAEtC;EACAhD,SAAS,CAAC,YAAY;IACpB8I,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAAC3F,SAAS,EAAEgE,YAAY,EAAEC,YAAY,EAAEvG,SAAS,CAACwN,eAAe,CAAC,EAAExN,SAAS,CAAC4F,UAAU,CAAC,EAAE/D,sBAAsB,CAAC,CAAC;;EAEtH;EACA1C,SAAS,CAAC,YAAY;IACpB0N,kBAAkB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAACtK,GAAG,CAAC,CAAC;;EAET;EACA,IAAIqL,WAAW,GAAG,CAAC,CAACL,UAAU,CAACnF,MAAM;EACrC,IAAIyF,UAAU,GAAG,EAAE,CAACxH,MAAM,CAAClD,SAAS,EAAE,WAAW,CAAC;EAClD,IAAI2K,QAAQ;EACZ,IAAIC,SAAS;EACb,IAAIC,OAAO;EACX,IAAIC,UAAU;EACd,IAAIpM,sBAAsB,EAAE;IAC1B,IAAIU,GAAG,EAAE;MACPwL,SAAS,GAAG9J,aAAa,GAAG,CAAC;MAC7B6J,QAAQ,GAAG7J,aAAa,KAAKsC,YAAY;IAC3C,CAAC,MAAM;MACLuH,QAAQ,GAAG7J,aAAa,GAAG,CAAC;MAC5B8J,SAAS,GAAG9J,aAAa,KAAKqC,YAAY;IAC5C;EACF,CAAC,MAAM;IACL0H,OAAO,GAAG3J,YAAY,GAAG,CAAC;IAC1B4J,UAAU,GAAG5J,YAAY,KAAKiC,YAAY;EAC5C;EACA,OAAO,aAAapH,KAAK,CAAC+L,aAAa,CAAClM,cAAc,EAAE;IACtDmP,QAAQ,EAAErB;EACZ,CAAC,EAAE,aAAa3N,KAAK,CAAC+L,aAAa,CAAC,KAAK,EAAE;IACzChJ,GAAG,EAAEhD,aAAa,CAACgD,GAAG,EAAEoB,YAAY,CAAC;IACrC8K,IAAI,EAAE,SAAS;IACf,kBAAkB,EAAEtM,sBAAsB,GAAG,YAAY,GAAG,UAAU;IACtEK,SAAS,EAAEpD,UAAU,CAAC,EAAE,CAACuH,MAAM,CAAClD,SAAS,EAAE,MAAM,CAAC,EAAEjB,SAAS,CAAC;IAC9DC,KAAK,EAAEA,KAAK;IACZsJ,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;MAC9B;MACAxE,eAAe,CAAC,CAAC;IACnB;EACF,CAAC,EAAE,aAAa/H,KAAK,CAAC+L,aAAa,CAAC/K,YAAY,EAAE;IAChD+B,GAAG,EAAEqB,YAAY;IACjB8K,QAAQ,EAAE,MAAM;IAChB5L,KAAK,EAAEA,KAAK;IACZW,SAAS,EAAEA;EACb,CAAC,CAAC,EAAE,aAAajE,KAAK,CAAC+L,aAAa,CAAClM,cAAc,EAAE;IACnDmP,QAAQ,EAAErB;EACZ,CAAC,EAAE,aAAa3N,KAAK,CAAC+L,aAAa,CAAC,KAAK,EAAE;IACzC/I,SAAS,EAAEpD,UAAU,CAAC+O,UAAU,EAAEnP,eAAe,CAACA,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2H,MAAM,CAACwH,UAAU,EAAE,YAAY,CAAC,EAAEC,QAAQ,CAAC,EAAE,EAAE,CAACzH,MAAM,CAACwH,UAAU,EAAE,aAAa,CAAC,EAAEE,SAAS,CAAC,EAAE,EAAE,CAAC1H,MAAM,CAACwH,UAAU,EAAE,WAAW,CAAC,EAAEG,OAAO,CAAC,EAAE,EAAE,CAAC3H,MAAM,CAACwH,UAAU,EAAE,cAAc,CAAC,EAAEI,UAAU,CAAC,CAAC;IACzShM,GAAG,EAAEuB;EACP,CAAC,EAAE,aAAatE,KAAK,CAAC+L,aAAa,CAAClM,cAAc,EAAE;IAClDmP,QAAQ,EAAErB;EACZ,CAAC,EAAE,aAAa3N,KAAK,CAAC+L,aAAa,CAAC,KAAK,EAAE;IACzChJ,GAAG,EAAEwB,UAAU;IACfvB,SAAS,EAAE,EAAE,CAACmE,MAAM,CAAClD,SAAS,EAAE,WAAW,CAAC;IAC5ChB,KAAK,EAAE;MACLkM,SAAS,EAAE,YAAY,CAAChI,MAAM,CAACpC,aAAa,EAAE,MAAM,CAAC,CAACoC,MAAM,CAAChC,YAAY,EAAE,KAAK,CAAC;MACjFiK,UAAU,EAAEvH,aAAa,GAAG,MAAM,GAAGsB;IACvC;EACF,CAAC,EAAE0C,QAAQ,EAAE,aAAa7L,KAAK,CAAC+L,aAAa,CAAChL,SAAS,EAAE;IACvDgC,GAAG,EAAE0B,iBAAiB;IACtBR,SAAS,EAAEA,SAAS;IACpBT,MAAM,EAAEA,MAAM;IACdD,QAAQ,EAAEA,QAAQ;IAClBN,KAAK,EAAEvD,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEmM,QAAQ,CAAC3C,MAAM,KAAK,CAAC,GAAGC,SAAS,GAAGwC,YAAY,CAAC,EAAE,CAAC,CAAC,EAAE;MAC5F0D,UAAU,EAAEX,WAAW,GAAG,QAAQ,GAAG;IACvC,CAAC;EACH,CAAC,CAAC,EAAE,aAAa1O,KAAK,CAAC+L,aAAa,CAAC,KAAK,EAAE;IAC1C/I,SAAS,EAAEpD,UAAU,CAAC,EAAE,CAACuH,MAAM,CAAClD,SAAS,EAAE,UAAU,CAAC,EAAEzE,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC2H,MAAM,CAAClD,SAAS,EAAE,mBAAmB,CAAC,EAAEd,QAAQ,CAACmM,MAAM,CAAC,CAAC;IACxIrM,KAAK,EAAEwL;EACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,aAAazO,KAAK,CAAC+L,aAAa,CAAC9K,aAAa,EAAE1B,QAAQ,CAAC,CAAC,CAAC,EAAEuD,KAAK,EAAE;IAC1EqJ,eAAe,EAAE3I,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC2I,eAAe;IACvFpJ,GAAG,EAAEyB,aAAa;IAClBP,SAAS,EAAEA,SAAS;IACpBC,IAAI,EAAEmK,UAAU;IAChBrL,SAAS,EAAE,CAAC0L,WAAW,IAAIxH,yBAAyB;IACpDqI,SAAS,EAAE,CAAC,CAAC1H;EACf,CAAC,CAAC,CAAC,EAAE,aAAa7H,KAAK,CAAC+L,aAAa,CAAC/K,YAAY,EAAE;IAClD+B,GAAG,EAAEsB,aAAa;IAClB6K,QAAQ,EAAE,OAAO;IACjB5L,KAAK,EAAEA,KAAK;IACZW,SAAS,EAAEA;EACb,CAAC,CAAC,CAAC,CAAC;EACJ;AACF,CAAC,CAAC;AACF,eAAerB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}