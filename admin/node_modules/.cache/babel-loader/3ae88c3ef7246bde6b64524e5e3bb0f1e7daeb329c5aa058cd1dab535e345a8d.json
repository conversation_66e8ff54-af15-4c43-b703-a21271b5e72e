{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nfunction genMaxMin(type) {\n  if (type === 'js') {\n    return {\n      max: Math.max,\n      min: Math.min\n    };\n  }\n  return {\n    max: function max() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return \"max(\".concat(args.map(function (value) {\n        return unit(value);\n      }).join(','), \")\");\n    },\n    min: function min() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return \"min(\".concat(args.map(function (value) {\n        return unit(value);\n      }).join(','), \")\");\n    }\n  };\n}\nexport default genMaxMin;", "map": {"version": 3, "names": ["unit", "genMaxMin", "type", "max", "Math", "min", "_len", "arguments", "length", "args", "Array", "_key", "concat", "map", "value", "join", "_len2", "_key2"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@ant-design/cssinjs-utils/es/util/maxmin.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nfunction genMaxMin(type) {\n  if (type === 'js') {\n    return {\n      max: Math.max,\n      min: Math.min\n    };\n  }\n  return {\n    max: function max() {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return \"max(\".concat(args.map(function (value) {\n        return unit(value);\n      }).join(','), \")\");\n    },\n    min: function min() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return \"min(\".concat(args.map(function (value) {\n        return unit(value);\n      }).join(','), \")\");\n    }\n  };\n}\nexport default genMaxMin;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,SAASA,CAACC,IAAI,EAAE;EACvB,IAAIA,IAAI,KAAK,IAAI,EAAE;IACjB,OAAO;MACLC,GAAG,EAAEC,IAAI,CAACD,GAAG;MACbE,GAAG,EAAED,IAAI,CAACC;IACZ,CAAC;EACH;EACA,OAAO;IACLF,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,KAAK,IAAIG,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAC9B;MACA,OAAO,MAAM,CAACC,MAAM,CAACH,IAAI,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC7C,OAAOd,IAAI,CAACc,KAAK,CAAC;MACpB,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;IACpB,CAAC;IACDV,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,KAAK,IAAIW,KAAK,GAAGT,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FR,IAAI,CAACQ,KAAK,CAAC,GAAGV,SAAS,CAACU,KAAK,CAAC;MAChC;MACA,OAAO,MAAM,CAACL,MAAM,CAACH,IAAI,CAACI,GAAG,CAAC,UAAUC,KAAK,EAAE;QAC7C,OAAOd,IAAI,CAACc,KAAK,CAAC;MACpB,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;IACpB;EACF,CAAC;AACH;AACA,eAAed,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}