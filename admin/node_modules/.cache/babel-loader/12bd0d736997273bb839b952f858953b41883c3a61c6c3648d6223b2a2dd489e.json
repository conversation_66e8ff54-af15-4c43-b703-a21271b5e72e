{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { SEARCH_MARK } from \"../hooks/useSearchOptions\";\nimport { getFullPathKeys, toPathKey } from \"../utils/commonUtil\";\nexport default (function (ref, options, fieldNames, activeValueCells, setActiveValueCells, onKeyBoardSelect, contextProps) {\n  var direction = contextProps.direction,\n    searchValue = contextProps.searchValue,\n    toggleOpen = contextProps.toggleOpen,\n    open = contextProps.open;\n  var rtl = direction === 'rtl';\n  var _React$useMemo = React.useMemo(function () {\n      var activeIndex = -1;\n      var currentOptions = options;\n      var mergedActiveIndexes = [];\n      var mergedActiveValueCells = [];\n      var len = activeValueCells.length;\n      var pathKeys = getFullPathKeys(options, fieldNames);\n\n      // Fill validate active value cells and index\n      var _loop = function _loop(i) {\n        // Mark the active index for current options\n        var nextActiveIndex = currentOptions.findIndex(function (option, index) {\n          return (pathKeys[index] ? toPathKey(pathKeys[index]) : option[fieldNames.value]) === activeValueCells[i];\n        });\n        if (nextActiveIndex === -1) {\n          return 1; // break\n        }\n        activeIndex = nextActiveIndex;\n        mergedActiveIndexes.push(activeIndex);\n        mergedActiveValueCells.push(activeValueCells[i]);\n        currentOptions = currentOptions[activeIndex][fieldNames.children];\n      };\n      for (var i = 0; i < len && currentOptions; i += 1) {\n        if (_loop(i)) break;\n      }\n\n      // Fill last active options\n      var activeOptions = options;\n      for (var _i = 0; _i < mergedActiveIndexes.length - 1; _i += 1) {\n        activeOptions = activeOptions[mergedActiveIndexes[_i]][fieldNames.children];\n      }\n      return [mergedActiveValueCells, activeIndex, activeOptions, pathKeys];\n    }, [activeValueCells, fieldNames, options]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 4),\n    validActiveValueCells = _React$useMemo2[0],\n    lastActiveIndex = _React$useMemo2[1],\n    lastActiveOptions = _React$useMemo2[2],\n    fullPathKeys = _React$useMemo2[3];\n\n  // Update active value cells and scroll to target element\n  var internalSetActiveValueCells = function internalSetActiveValueCells(next) {\n    setActiveValueCells(next);\n  };\n\n  // Same options offset\n  var offsetActiveOption = function offsetActiveOption(offset) {\n    var len = lastActiveOptions.length;\n    var currentIndex = lastActiveIndex;\n    if (currentIndex === -1 && offset < 0) {\n      currentIndex = len;\n    }\n    for (var i = 0; i < len; i += 1) {\n      currentIndex = (currentIndex + offset + len) % len;\n      var _option = lastActiveOptions[currentIndex];\n      if (_option && !_option.disabled) {\n        var nextActiveCells = validActiveValueCells.slice(0, -1).concat(fullPathKeys[currentIndex] ? toPathKey(fullPathKeys[currentIndex]) : _option[fieldNames.value]);\n        internalSetActiveValueCells(nextActiveCells);\n        return;\n      }\n    }\n  };\n\n  // Different options offset\n  var prevColumn = function prevColumn() {\n    if (validActiveValueCells.length > 1) {\n      var nextActiveCells = validActiveValueCells.slice(0, -1);\n      internalSetActiveValueCells(nextActiveCells);\n    } else {\n      toggleOpen(false);\n    }\n  };\n  var nextColumn = function nextColumn() {\n    var _lastActiveOptions$la;\n    var nextOptions = ((_lastActiveOptions$la = lastActiveOptions[lastActiveIndex]) === null || _lastActiveOptions$la === void 0 ? void 0 : _lastActiveOptions$la[fieldNames.children]) || [];\n    var nextOption = nextOptions.find(function (option) {\n      return !option.disabled;\n    });\n    if (nextOption) {\n      var nextActiveCells = [].concat(_toConsumableArray(validActiveValueCells), [nextOption[fieldNames.value]]);\n      internalSetActiveValueCells(nextActiveCells);\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      // scrollTo: treeRef.current?.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              }\n              if (offset !== 0) {\n                offsetActiveOption(offset);\n              }\n              break;\n            }\n          case KeyCode.LEFT:\n            {\n              if (searchValue) {\n                break;\n              }\n              if (rtl) {\n                nextColumn();\n              } else {\n                prevColumn();\n              }\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              if (searchValue) {\n                break;\n              }\n              if (rtl) {\n                prevColumn();\n              } else {\n                nextColumn();\n              }\n              break;\n            }\n          case KeyCode.BACKSPACE:\n            {\n              if (!searchValue) {\n                prevColumn();\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              if (validActiveValueCells.length) {\n                var _option2 = lastActiveOptions[lastActiveIndex];\n\n                // Search option should revert back of origin options\n                var originOptions = (_option2 === null || _option2 === void 0 ? void 0 : _option2[SEARCH_MARK]) || [];\n                if (originOptions.length) {\n                  onKeyBoardSelect(originOptions.map(function (opt) {\n                    return opt[fieldNames.value];\n                  }), originOptions[originOptions.length - 1]);\n                } else {\n                  onKeyBoardSelect(validActiveValueCells, lastActiveOptions[lastActiveIndex]);\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n});", "map": {"version": 3, "names": ["_toConsumableArray", "_slicedToArray", "KeyCode", "React", "SEARCH_MARK", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "options", "fieldNames", "activeValueCells", "setActiveValueCells", "onKeyBoardSelect", "contextProps", "direction", "searchValue", "toggle<PERSON><PERSON>", "open", "rtl", "_React$useMemo", "useMemo", "activeIndex", "currentOptions", "mergedActiveIndexes", "mergedActiveValueCells", "len", "length", "pathKeys", "_loop", "i", "nextActiveIndex", "findIndex", "option", "index", "value", "push", "children", "activeOptions", "_i", "_React$useMemo2", "validActiveValueCells", "lastActiveIndex", "lastActiveOptions", "fullPath<PERSON><PERSON><PERSON>", "internalSetActiveValueCells", "next", "offsetActiveOption", "offset", "currentIndex", "_option", "disabled", "nextActiveCells", "slice", "concat", "prevColumn", "nextColumn", "_lastActiveOptions$la", "nextOptions", "nextOption", "find", "useImperativeHandle", "onKeyDown", "event", "which", "UP", "DOWN", "LEFT", "RIGHT", "BACKSPACE", "ENTER", "_option2", "originOptions", "map", "opt", "ESC", "stopPropagation", "onKeyUp"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-cascader/es/OptionList/useKeyboard.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport KeyCode from \"rc-util/es/KeyCode\";\nimport * as React from 'react';\nimport { SEARCH_MARK } from \"../hooks/useSearchOptions\";\nimport { getFullPathKeys, toPathKey } from \"../utils/commonUtil\";\nexport default (function (ref, options, fieldNames, activeValueCells, setActiveValueCells, onKeyBoardSelect, contextProps) {\n  var direction = contextProps.direction,\n    searchValue = contextProps.searchValue,\n    toggleOpen = contextProps.toggleOpen,\n    open = contextProps.open;\n  var rtl = direction === 'rtl';\n  var _React$useMemo = React.useMemo(function () {\n      var activeIndex = -1;\n      var currentOptions = options;\n      var mergedActiveIndexes = [];\n      var mergedActiveValueCells = [];\n      var len = activeValueCells.length;\n      var pathKeys = getFullPathKeys(options, fieldNames);\n\n      // Fill validate active value cells and index\n      var _loop = function _loop(i) {\n        // Mark the active index for current options\n        var nextActiveIndex = currentOptions.findIndex(function (option, index) {\n          return (pathKeys[index] ? toPathKey(pathKeys[index]) : option[fieldNames.value]) === activeValueCells[i];\n        });\n        if (nextActiveIndex === -1) {\n          return 1; // break\n        }\n        activeIndex = nextActiveIndex;\n        mergedActiveIndexes.push(activeIndex);\n        mergedActiveValueCells.push(activeValueCells[i]);\n        currentOptions = currentOptions[activeIndex][fieldNames.children];\n      };\n      for (var i = 0; i < len && currentOptions; i += 1) {\n        if (_loop(i)) break;\n      }\n\n      // Fill last active options\n      var activeOptions = options;\n      for (var _i = 0; _i < mergedActiveIndexes.length - 1; _i += 1) {\n        activeOptions = activeOptions[mergedActiveIndexes[_i]][fieldNames.children];\n      }\n      return [mergedActiveValueCells, activeIndex, activeOptions, pathKeys];\n    }, [activeValueCells, fieldNames, options]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 4),\n    validActiveValueCells = _React$useMemo2[0],\n    lastActiveIndex = _React$useMemo2[1],\n    lastActiveOptions = _React$useMemo2[2],\n    fullPathKeys = _React$useMemo2[3];\n\n  // Update active value cells and scroll to target element\n  var internalSetActiveValueCells = function internalSetActiveValueCells(next) {\n    setActiveValueCells(next);\n  };\n\n  // Same options offset\n  var offsetActiveOption = function offsetActiveOption(offset) {\n    var len = lastActiveOptions.length;\n    var currentIndex = lastActiveIndex;\n    if (currentIndex === -1 && offset < 0) {\n      currentIndex = len;\n    }\n    for (var i = 0; i < len; i += 1) {\n      currentIndex = (currentIndex + offset + len) % len;\n      var _option = lastActiveOptions[currentIndex];\n      if (_option && !_option.disabled) {\n        var nextActiveCells = validActiveValueCells.slice(0, -1).concat(fullPathKeys[currentIndex] ? toPathKey(fullPathKeys[currentIndex]) : _option[fieldNames.value]);\n        internalSetActiveValueCells(nextActiveCells);\n        return;\n      }\n    }\n  };\n\n  // Different options offset\n  var prevColumn = function prevColumn() {\n    if (validActiveValueCells.length > 1) {\n      var nextActiveCells = validActiveValueCells.slice(0, -1);\n      internalSetActiveValueCells(nextActiveCells);\n    } else {\n      toggleOpen(false);\n    }\n  };\n  var nextColumn = function nextColumn() {\n    var _lastActiveOptions$la;\n    var nextOptions = ((_lastActiveOptions$la = lastActiveOptions[lastActiveIndex]) === null || _lastActiveOptions$la === void 0 ? void 0 : _lastActiveOptions$la[fieldNames.children]) || [];\n    var nextOption = nextOptions.find(function (option) {\n      return !option.disabled;\n    });\n    if (nextOption) {\n      var nextActiveCells = [].concat(_toConsumableArray(validActiveValueCells), [nextOption[fieldNames.value]]);\n      internalSetActiveValueCells(nextActiveCells);\n    }\n  };\n  React.useImperativeHandle(ref, function () {\n    return {\n      // scrollTo: treeRef.current?.scrollTo,\n      onKeyDown: function onKeyDown(event) {\n        var which = event.which;\n        switch (which) {\n          // >>> Arrow keys\n          case KeyCode.UP:\n          case KeyCode.DOWN:\n            {\n              var offset = 0;\n              if (which === KeyCode.UP) {\n                offset = -1;\n              } else if (which === KeyCode.DOWN) {\n                offset = 1;\n              }\n              if (offset !== 0) {\n                offsetActiveOption(offset);\n              }\n              break;\n            }\n          case KeyCode.LEFT:\n            {\n              if (searchValue) {\n                break;\n              }\n              if (rtl) {\n                nextColumn();\n              } else {\n                prevColumn();\n              }\n              break;\n            }\n          case KeyCode.RIGHT:\n            {\n              if (searchValue) {\n                break;\n              }\n              if (rtl) {\n                prevColumn();\n              } else {\n                nextColumn();\n              }\n              break;\n            }\n          case KeyCode.BACKSPACE:\n            {\n              if (!searchValue) {\n                prevColumn();\n              }\n              break;\n            }\n\n          // >>> Select\n          case KeyCode.ENTER:\n            {\n              if (validActiveValueCells.length) {\n                var _option2 = lastActiveOptions[lastActiveIndex];\n\n                // Search option should revert back of origin options\n                var originOptions = (_option2 === null || _option2 === void 0 ? void 0 : _option2[SEARCH_MARK]) || [];\n                if (originOptions.length) {\n                  onKeyBoardSelect(originOptions.map(function (opt) {\n                    return opt[fieldNames.value];\n                  }), originOptions[originOptions.length - 1]);\n                } else {\n                  onKeyBoardSelect(validActiveValueCells, lastActiveOptions[lastActiveIndex]);\n                }\n              }\n              break;\n            }\n\n          // >>> Close\n          case KeyCode.ESC:\n            {\n              toggleOpen(false);\n              if (open) {\n                event.stopPropagation();\n              }\n            }\n        }\n      },\n      onKeyUp: function onKeyUp() {}\n    };\n  });\n});"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,2BAA2B;AACvD,SAASC,eAAe,EAAEC,SAAS,QAAQ,qBAAqB;AAChE,gBAAgB,UAAUC,GAAG,EAAEC,OAAO,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,gBAAgB,EAAEC,YAAY,EAAE;EACzH,IAAIC,SAAS,GAAGD,YAAY,CAACC,SAAS;IACpCC,WAAW,GAAGF,YAAY,CAACE,WAAW;IACtCC,UAAU,GAAGH,YAAY,CAACG,UAAU;IACpCC,IAAI,GAAGJ,YAAY,CAACI,IAAI;EAC1B,IAAIC,GAAG,GAAGJ,SAAS,KAAK,KAAK;EAC7B,IAAIK,cAAc,GAAGhB,KAAK,CAACiB,OAAO,CAAC,YAAY;MAC3C,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,IAAIC,cAAc,GAAGd,OAAO;MAC5B,IAAIe,mBAAmB,GAAG,EAAE;MAC5B,IAAIC,sBAAsB,GAAG,EAAE;MAC/B,IAAIC,GAAG,GAAGf,gBAAgB,CAACgB,MAAM;MACjC,IAAIC,QAAQ,GAAGtB,eAAe,CAACG,OAAO,EAAEC,UAAU,CAAC;;MAEnD;MACA,IAAImB,KAAK,GAAG,SAASA,KAAKA,CAACC,CAAC,EAAE;QAC5B;QACA,IAAIC,eAAe,GAAGR,cAAc,CAACS,SAAS,CAAC,UAAUC,MAAM,EAAEC,KAAK,EAAE;UACtE,OAAO,CAACN,QAAQ,CAACM,KAAK,CAAC,GAAG3B,SAAS,CAACqB,QAAQ,CAACM,KAAK,CAAC,CAAC,GAAGD,MAAM,CAACvB,UAAU,CAACyB,KAAK,CAAC,MAAMxB,gBAAgB,CAACmB,CAAC,CAAC;QAC1G,CAAC,CAAC;QACF,IAAIC,eAAe,KAAK,CAAC,CAAC,EAAE;UAC1B,OAAO,CAAC,CAAC,CAAC;QACZ;QACAT,WAAW,GAAGS,eAAe;QAC7BP,mBAAmB,CAACY,IAAI,CAACd,WAAW,CAAC;QACrCG,sBAAsB,CAACW,IAAI,CAACzB,gBAAgB,CAACmB,CAAC,CAAC,CAAC;QAChDP,cAAc,GAAGA,cAAc,CAACD,WAAW,CAAC,CAACZ,UAAU,CAAC2B,QAAQ,CAAC;MACnE,CAAC;MACD,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,IAAIH,cAAc,EAAEO,CAAC,IAAI,CAAC,EAAE;QACjD,IAAID,KAAK,CAACC,CAAC,CAAC,EAAE;MAChB;;MAEA;MACA,IAAIQ,aAAa,GAAG7B,OAAO;MAC3B,KAAK,IAAI8B,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGf,mBAAmB,CAACG,MAAM,GAAG,CAAC,EAAEY,EAAE,IAAI,CAAC,EAAE;QAC7DD,aAAa,GAAGA,aAAa,CAACd,mBAAmB,CAACe,EAAE,CAAC,CAAC,CAAC7B,UAAU,CAAC2B,QAAQ,CAAC;MAC7E;MACA,OAAO,CAACZ,sBAAsB,EAAEH,WAAW,EAAEgB,aAAa,EAAEV,QAAQ,CAAC;IACvE,CAAC,EAAE,CAACjB,gBAAgB,EAAED,UAAU,EAAED,OAAO,CAAC,CAAC;IAC3C+B,eAAe,GAAGtC,cAAc,CAACkB,cAAc,EAAE,CAAC,CAAC;IACnDqB,qBAAqB,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC1CE,eAAe,GAAGF,eAAe,CAAC,CAAC,CAAC;IACpCG,iBAAiB,GAAGH,eAAe,CAAC,CAAC,CAAC;IACtCI,YAAY,GAAGJ,eAAe,CAAC,CAAC,CAAC;;EAEnC;EACA,IAAIK,2BAA2B,GAAG,SAASA,2BAA2BA,CAACC,IAAI,EAAE;IAC3ElC,mBAAmB,CAACkC,IAAI,CAAC;EAC3B,CAAC;;EAED;EACA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,MAAM,EAAE;IAC3D,IAAItB,GAAG,GAAGiB,iBAAiB,CAAChB,MAAM;IAClC,IAAIsB,YAAY,GAAGP,eAAe;IAClC,IAAIO,YAAY,KAAK,CAAC,CAAC,IAAID,MAAM,GAAG,CAAC,EAAE;MACrCC,YAAY,GAAGvB,GAAG;IACpB;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,EAAEI,CAAC,IAAI,CAAC,EAAE;MAC/BmB,YAAY,GAAG,CAACA,YAAY,GAAGD,MAAM,GAAGtB,GAAG,IAAIA,GAAG;MAClD,IAAIwB,OAAO,GAAGP,iBAAiB,CAACM,YAAY,CAAC;MAC7C,IAAIC,OAAO,IAAI,CAACA,OAAO,CAACC,QAAQ,EAAE;QAChC,IAAIC,eAAe,GAAGX,qBAAqB,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,MAAM,CAACV,YAAY,CAACK,YAAY,CAAC,GAAG1C,SAAS,CAACqC,YAAY,CAACK,YAAY,CAAC,CAAC,GAAGC,OAAO,CAACxC,UAAU,CAACyB,KAAK,CAAC,CAAC;QAC/JU,2BAA2B,CAACO,eAAe,CAAC;QAC5C;MACF;IACF;EACF,CAAC;;EAED;EACA,IAAIG,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAId,qBAAqB,CAACd,MAAM,GAAG,CAAC,EAAE;MACpC,IAAIyB,eAAe,GAAGX,qBAAqB,CAACY,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACxDR,2BAA2B,CAACO,eAAe,CAAC;IAC9C,CAAC,MAAM;MACLnC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EACD,IAAIuC,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;IACrC,IAAIC,qBAAqB;IACzB,IAAIC,WAAW,GAAG,CAAC,CAACD,qBAAqB,GAAGd,iBAAiB,CAACD,eAAe,CAAC,MAAM,IAAI,IAAIe,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAAC/C,UAAU,CAAC2B,QAAQ,CAAC,KAAK,EAAE;IACzL,IAAIsB,UAAU,GAAGD,WAAW,CAACE,IAAI,CAAC,UAAU3B,MAAM,EAAE;MAClD,OAAO,CAACA,MAAM,CAACkB,QAAQ;IACzB,CAAC,CAAC;IACF,IAAIQ,UAAU,EAAE;MACd,IAAIP,eAAe,GAAG,EAAE,CAACE,MAAM,CAACrD,kBAAkB,CAACwC,qBAAqB,CAAC,EAAE,CAACkB,UAAU,CAACjD,UAAU,CAACyB,KAAK,CAAC,CAAC,CAAC;MAC1GU,2BAA2B,CAACO,eAAe,CAAC;IAC9C;EACF,CAAC;EACDhD,KAAK,CAACyD,mBAAmB,CAACrD,GAAG,EAAE,YAAY;IACzC,OAAO;MACL;MACAsD,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;QACnC,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;QACvB,QAAQA,KAAK;UACX;UACA,KAAK7D,OAAO,CAAC8D,EAAE;UACf,KAAK9D,OAAO,CAAC+D,IAAI;YACf;cACE,IAAIlB,MAAM,GAAG,CAAC;cACd,IAAIgB,KAAK,KAAK7D,OAAO,CAAC8D,EAAE,EAAE;gBACxBjB,MAAM,GAAG,CAAC,CAAC;cACb,CAAC,MAAM,IAAIgB,KAAK,KAAK7D,OAAO,CAAC+D,IAAI,EAAE;gBACjClB,MAAM,GAAG,CAAC;cACZ;cACA,IAAIA,MAAM,KAAK,CAAC,EAAE;gBAChBD,kBAAkB,CAACC,MAAM,CAAC;cAC5B;cACA;YACF;UACF,KAAK7C,OAAO,CAACgE,IAAI;YACf;cACE,IAAInD,WAAW,EAAE;gBACf;cACF;cACA,IAAIG,GAAG,EAAE;gBACPqC,UAAU,CAAC,CAAC;cACd,CAAC,MAAM;gBACLD,UAAU,CAAC,CAAC;cACd;cACA;YACF;UACF,KAAKpD,OAAO,CAACiE,KAAK;YAChB;cACE,IAAIpD,WAAW,EAAE;gBACf;cACF;cACA,IAAIG,GAAG,EAAE;gBACPoC,UAAU,CAAC,CAAC;cACd,CAAC,MAAM;gBACLC,UAAU,CAAC,CAAC;cACd;cACA;YACF;UACF,KAAKrD,OAAO,CAACkE,SAAS;YACpB;cACE,IAAI,CAACrD,WAAW,EAAE;gBAChBuC,UAAU,CAAC,CAAC;cACd;cACA;YACF;;UAEF;UACA,KAAKpD,OAAO,CAACmE,KAAK;YAChB;cACE,IAAI7B,qBAAqB,CAACd,MAAM,EAAE;gBAChC,IAAI4C,QAAQ,GAAG5B,iBAAiB,CAACD,eAAe,CAAC;;gBAEjD;gBACA,IAAI8B,aAAa,GAAG,CAACD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAClE,WAAW,CAAC,KAAK,EAAE;gBACrG,IAAImE,aAAa,CAAC7C,MAAM,EAAE;kBACxBd,gBAAgB,CAAC2D,aAAa,CAACC,GAAG,CAAC,UAAUC,GAAG,EAAE;oBAChD,OAAOA,GAAG,CAAChE,UAAU,CAACyB,KAAK,CAAC;kBAC9B,CAAC,CAAC,EAAEqC,aAAa,CAACA,aAAa,CAAC7C,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC9C,CAAC,MAAM;kBACLd,gBAAgB,CAAC4B,qBAAqB,EAAEE,iBAAiB,CAACD,eAAe,CAAC,CAAC;gBAC7E;cACF;cACA;YACF;;UAEF;UACA,KAAKvC,OAAO,CAACwE,GAAG;YACd;cACE1D,UAAU,CAAC,KAAK,CAAC;cACjB,IAAIC,IAAI,EAAE;gBACR6C,KAAK,CAACa,eAAe,CAAC,CAAC;cACzB;YACF;QACJ;MACF,CAAC;MACDC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG,CAAC;IAC/B,CAAC;EACH,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}