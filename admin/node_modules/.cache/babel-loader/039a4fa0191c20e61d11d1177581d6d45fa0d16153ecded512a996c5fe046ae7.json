{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/App.tsx\";\nimport React from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\nimport { Layout } from \"antd\";\nimport { AuthProvider } from \"./contexts/AuthContext\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Sidebar from \"./components/Sidebar\";\nimport Header from \"./components/Header\";\nimport Login from \"./pages/Login\";\nimport Dashboard from \"./pages/Dashboard\";\nimport ProductList from \"./pages/ProductList\";\nimport ProductDetail from \"./pages/ProductDetail\";\nimport CreateProduct from \"./pages/CreateProduct\";\nimport ChangePassword from \"./pages/ChangePassword\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Content\n} = Layout;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(AuthProvider, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/*\",\n        element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n          children: /*#__PURE__*/_jsxDEV(Layout, {\n            children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Layout, {\n              children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 32,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Content, {\n                style: {\n                  margin: \"24px 16px\",\n                  padding: 24,\n                  background: \"#fff\"\n                },\n                children: /*#__PURE__*/_jsxDEV(Routes, {\n                  children: [/*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/\",\n                    element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 41,\n                      columnNumber: 48\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 41,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/products\",\n                    element: /*#__PURE__*/_jsxDEV(ProductList, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 42,\n                      columnNumber: 56\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 42,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/products/create\",\n                    element: /*#__PURE__*/_jsxDEV(CreateProduct, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 45,\n                      columnNumber: 34\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 43,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/products/:id\",\n                    element: /*#__PURE__*/_jsxDEV(ProductDetail, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 47,\n                      columnNumber: 60\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 47,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Route, {\n                    path: \"/change-password\",\n                    element: /*#__PURE__*/_jsxDEV(ChangePassword, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 50,\n                      columnNumber: 34\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 48,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 40,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 33,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Layout", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "Sidebar", "Header", "<PERSON><PERSON>", "Dashboard", "ProductList", "ProductDetail", "CreateProduct", "ChangePassword", "jsxDEV", "_jsxDEV", "Content", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "margin", "padding", "background", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/App.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\nimport { Layout } from \"antd\";\nimport { AuthProvider } from \"./contexts/AuthContext\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Sidebar from \"./components/Sidebar\";\nimport Header from \"./components/Header\";\nimport Login from \"./pages/Login\";\nimport Dashboard from \"./pages/Dashboard\";\nimport ProductList from \"./pages/ProductList\";\nimport ProductDetail from \"./pages/ProductDetail\";\nimport CreateProduct from \"./pages/CreateProduct\";\nimport ChangePassword from \"./pages/ChangePassword\";\n\nconst { Content } = Layout;\n\nfunction App() {\n  return (\n    <AuthProvider>\n      <Routes>\n        {/* 登录页面 */}\n        <Route path=\"/login\" element={<Login />} />\n\n        {/* 受保护的路由 */}\n        <Route\n          path=\"/*\"\n          element={\n            <ProtectedRoute>\n              <Layout>\n                <Sidebar />\n                <Layout>\n                  <Header />\n                  <Content\n                    style={{\n                      margin: \"24px 16px\",\n                      padding: 24,\n                      background: \"#fff\",\n                    }}\n                  >\n                    <Routes>\n                      <Route path=\"/\" element={<Dashboard />} />\n                      <Route path=\"/products\" element={<ProductList />} />\n                      <Route\n                        path=\"/products/create\"\n                        element={<CreateProduct />}\n                      />\n                      <Route path=\"/products/:id\" element={<ProductDetail />} />\n                      <Route\n                        path=\"/change-password\"\n                        element={<ChangePassword />}\n                      />\n                    </Routes>\n                  </Content>\n                </Layout>\n              </Layout>\n            </ProtectedRoute>\n          }\n        />\n      </Routes>\n    </AuthProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,MAAM;AAC7B,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,cAAc,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAM;EAAEC;AAAQ,CAAC,GAAGb,MAAM;AAE1B,SAASc,GAAGA,CAAA,EAAG;EACb,oBACEF,OAAA,CAACX,YAAY;IAAAc,QAAA,eACXH,OAAA,CAACd,MAAM;MAAAiB,QAAA,gBAELH,OAAA,CAACb,KAAK;QAACiB,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEL,OAAA,CAACP,KAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG3CT,OAAA,CAACb,KAAK;QACJiB,IAAI,EAAC,IAAI;QACTC,OAAO,eACLL,OAAA,CAACV,cAAc;UAAAa,QAAA,eACbH,OAAA,CAACZ,MAAM;YAAAe,QAAA,gBACLH,OAAA,CAACT,OAAO;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACXT,OAAA,CAACZ,MAAM;cAAAe,QAAA,gBACLH,OAAA,CAACR,MAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACVT,OAAA,CAACC,OAAO;gBACNS,KAAK,EAAE;kBACLC,MAAM,EAAE,WAAW;kBACnBC,OAAO,EAAE,EAAE;kBACXC,UAAU,EAAE;gBACd,CAAE;gBAAAV,QAAA,eAEFH,OAAA,CAACd,MAAM;kBAAAiB,QAAA,gBACLH,OAAA,CAACb,KAAK;oBAACiB,IAAI,EAAC,GAAG;oBAACC,OAAO,eAAEL,OAAA,CAACN,SAAS;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1CT,OAAA,CAACb,KAAK;oBAACiB,IAAI,EAAC,WAAW;oBAACC,OAAO,eAAEL,OAAA,CAACL,WAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpDT,OAAA,CAACb,KAAK;oBACJiB,IAAI,EAAC,kBAAkB;oBACvBC,OAAO,eAAEL,OAAA,CAACH,aAAa;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5B,CAAC,eACFT,OAAA,CAACb,KAAK;oBAACiB,IAAI,EAAC,eAAe;oBAACC,OAAO,eAAEL,OAAA,CAACJ,aAAa;sBAAAU,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC1DT,OAAA,CAACb,KAAK;oBACJiB,IAAI,EAAC,kBAAkB;oBACvBC,OAAO,eAAEL,OAAA,CAACF,cAAc;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MACjB;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEnB;AAACK,EAAA,GA7CQZ,GAAG;AA+CZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}