{"ast": null, "code": "import axios from \"axios\";\n\n// 环境配置\nconst getApiBaseUrl = () => {\n  const env = process.env.REACT_APP_ENV || \"development\";\n  switch (env) {\n    case \"production\":\n      return process.env.REACT_APP_API_URL || \"https://api.your-domain.com\";\n    case \"staging\":\n      return process.env.REACT_APP_API_URL || \"https://api-staging.your-domain.com\";\n    case \"development\":\n    default:\n      return process.env.REACT_APP_API_URL || \"http://localhost:3000\";\n  }\n};\nconst API_BASE_URL = getApiBaseUrl();\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 添加认证 token\n  const token = localStorage.getItem(\"token\");\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  var _error$response;\n  // 根据环境决定是否打印详细错误信息\n  if (process.env.REACT_APP_DEBUG === \"true\") {\n    console.error(\"API Error:\", error);\n  }\n\n  // 401 未授权，清除 token 并跳转到登录页\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401) {\n    localStorage.removeItem(\"token\");\n    localStorage.removeItem(\"user\");\n    window.location.href = \"/login\";\n  }\n\n  // 生产环境下可以集成错误监控服务\n  if (process.env.REACT_APP_ENV === \"production\" && process.env.REACT_APP_SENTRY_DSN) {\n    // 这里可以集成 Sentry 等错误监控服务\n    // Sentry.captureException(error);\n  }\n  return Promise.reject(error);\n});\n\n// 认证相关接口\n\n// 商品相关接口\n\n// 认证 API\nexport const authApi = {\n  // 用户登录\n  login: data => {\n    return api.post(\"/auth/login\", data);\n  },\n  // 获取用户信息\n  getProfile: () => {\n    return api.get(\"/auth/profile\");\n  },\n  // 更改密码\n  changePassword: data => {\n    return api.post(\"/auth/change-password\", data);\n  }\n};\n\n// 商品 API\nexport const productApi = {\n  // 获取商品列表\n  getList: params => {\n    return api.get(\"/products\", {\n      params\n    });\n  },\n  // 获取商品详情\n  getById: id => {\n    return api.get(`/products/${id}`);\n  },\n  // 创建商品\n  create: data => {\n    return api.post(\"/products\", data);\n  },\n  // 更新商品\n  update: (id, data) => {\n    return api.patch(`/products/${id}`, data);\n  },\n  // 删除商品\n  delete: id => {\n    return api.delete(`/products/${id}`);\n  },\n  // 获取统计信息\n  getStats: () => {\n    return api.get(\"/products/stats\");\n  },\n  // 获取所有品牌\n  getBrands: () => {\n    return api.get(\"/products/brands\");\n  },\n  // 获取所有分类\n  getCategories: () => {\n    return api.get(\"/products/categories\");\n  },\n  // 根据商品编号获取商品\n  getByProductNumber: productNumber => {\n    return api.get(`/products/number/${productNumber}`);\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "getApiBaseUrl", "env", "process", "REACT_APP_ENV", "REACT_APP_API_URL", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "data", "_error$response", "REACT_APP_DEBUG", "console", "status", "removeItem", "window", "location", "href", "REACT_APP_SENTRY_DSN", "authApi", "login", "post", "getProfile", "get", "changePassword", "productApi", "getList", "params", "getById", "id", "update", "patch", "delete", "getStats", "getBrands", "getCategories", "getByProductNumber", "productNumber"], "sources": ["/Users/<USER>/github/7/admin/src/services/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\n// 环境配置\nconst getApiBaseUrl = () => {\n  const env = process.env.REACT_APP_ENV || \"development\";\n\n  switch (env) {\n    case \"production\":\n      return process.env.REACT_APP_API_URL || \"https://api.your-domain.com\";\n    case \"staging\":\n      return (\n        process.env.REACT_APP_API_URL || \"https://api-staging.your-domain.com\"\n      );\n    case \"development\":\n    default:\n      return process.env.REACT_APP_API_URL || \"http://localhost:3000\";\n  }\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 添加认证 token\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    // 根据环境决定是否打印详细错误信息\n    if (process.env.REACT_APP_DEBUG === \"true\") {\n      console.error(\"API Error:\", error);\n    }\n\n    // 401 未授权，清除 token 并跳转到登录页\n    if (error.response?.status === 401) {\n      localStorage.removeItem(\"token\");\n      localStorage.removeItem(\"user\");\n      window.location.href = \"/login\";\n    }\n\n    // 生产环境下可以集成错误监控服务\n    if (\n      process.env.REACT_APP_ENV === \"production\" &&\n      process.env.REACT_APP_SENTRY_DSN\n    ) {\n      // 这里可以集成 Sentry 等错误监控服务\n      // Sentry.captureException(error);\n    }\n\n    return Promise.reject(error);\n  }\n);\n\n// 认证相关接口\nexport interface LoginData {\n  username: string;\n  password: string;\n}\n\nexport interface LoginResponse {\n  access_token: string;\n  user: {\n    id: string;\n    username: string;\n    role: string;\n    lastLoginAt?: string;\n  };\n}\n\nexport interface ChangePasswordData {\n  currentPassword: string;\n  newPassword: string;\n}\n\nexport interface UserProfile {\n  _id: string;\n  username: string;\n  role: string;\n  isActive: boolean;\n  lastLoginAt?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 商品相关接口\nexport interface Product {\n  _id: string;\n  name: string;\n  productNumber: string;\n  alcoholContent: number;\n  packagingDate: string;\n  description?: string;\n  status: \"active\" | \"inactive\" | \"discontinued\";\n  brand?: string;\n  category?: string;\n  volume?: number;\n  batchNumber?: string;\n  productionLocation?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface CreateProductData {\n  name: string;\n  alcoholContent: number;\n  packagingDate: string;\n  description?: string;\n  status?: string;\n  brand?: string;\n  category?: string;\n  volume?: number;\n  batchNumber?: string;\n  productionLocation?: string;\n}\n\nexport interface ProductQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  status?: string;\n  brand?: string;\n  category?: string;\n  sortBy?: string;\n  sortOrder?: string;\n}\n\nexport interface ProductListResponse {\n  data: Product[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface ProductStatsResponse {\n  total: number;\n  active: number;\n  inactive: number;\n  discontinued: number;\n  totalBrands: number;\n  totalCategories: number;\n}\n\n// 认证 API\nexport const authApi = {\n  // 用户登录\n  login: (data: LoginData): Promise<LoginResponse> => {\n    return api.post(\"/auth/login\", data);\n  },\n\n  // 获取用户信息\n  getProfile: (): Promise<UserProfile> => {\n    return api.get(\"/auth/profile\");\n  },\n\n  // 更改密码\n  changePassword: (data: ChangePasswordData): Promise<{ message: string }> => {\n    return api.post(\"/auth/change-password\", data);\n  },\n};\n\n// 商品 API\nexport const productApi = {\n  // 获取商品列表\n  getList: (params?: ProductQueryParams): Promise<ProductListResponse> => {\n    return api.get(\"/products\", { params });\n  },\n\n  // 获取商品详情\n  getById: (id: string): Promise<Product> => {\n    return api.get(`/products/${id}`);\n  },\n\n  // 创建商品\n  create: (data: CreateProductData): Promise<Product> => {\n    return api.post(\"/products\", data);\n  },\n\n  // 更新商品\n  update: (id: string, data: Partial<CreateProductData>): Promise<Product> => {\n    return api.patch(`/products/${id}`, data);\n  },\n\n  // 删除商品\n  delete: (id: string): Promise<void> => {\n    return api.delete(`/products/${id}`);\n  },\n\n  // 获取统计信息\n  getStats: (): Promise<ProductStatsResponse> => {\n    return api.get(\"/products/stats\");\n  },\n\n  // 获取所有品牌\n  getBrands: (): Promise<string[]> => {\n    return api.get(\"/products/brands\");\n  },\n\n  // 获取所有分类\n  getCategories: (): Promise<string[]> => {\n    return api.get(\"/products/categories\");\n  },\n\n  // 根据商品编号获取商品\n  getByProductNumber: (productNumber: string): Promise<Product> => {\n    return api.get(`/products/number/${productNumber}`);\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAMC,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,aAAa,IAAI,aAAa;EAEtD,QAAQF,GAAG;IACT,KAAK,YAAY;MACf,OAAOC,OAAO,CAACD,GAAG,CAACG,iBAAiB,IAAI,6BAA6B;IACvE,KAAK,SAAS;MACZ,OACEF,OAAO,CAACD,GAAG,CAACG,iBAAiB,IAAI,qCAAqC;IAE1E,KAAK,aAAa;IAClB;MACE,OAAOF,OAAO,CAACD,GAAG,CAACG,iBAAiB,IAAI,uBAAuB;EACnE;AACF,CAAC;AAED,MAAMC,YAAY,GAAGL,aAAa,CAAC,CAAC;AAEpC,MAAMM,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAb,GAAG,CAACK,YAAY,CAACW,QAAQ,CAACT,GAAG,CAC1BS,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EAAA,IAAAK,eAAA;EACT;EACA,IAAItB,OAAO,CAACD,GAAG,CAACwB,eAAe,KAAK,MAAM,EAAE;IAC1CC,OAAO,CAACP,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;;EAEA;EACA,IAAI,EAAAK,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,uBAAdA,eAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;IAClCX,YAAY,CAACY,UAAU,CAAC,OAAO,CAAC;IAChCZ,YAAY,CAACY,UAAU,CAAC,MAAM,CAAC;IAC/BC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;EACjC;;EAEA;EACA,IACE7B,OAAO,CAACD,GAAG,CAACE,aAAa,KAAK,YAAY,IAC1CD,OAAO,CAACD,GAAG,CAAC+B,oBAAoB,EAChC;IACA;IACA;EAAA;EAGF,OAAOZ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;;AA+BA;;AA2DA;AACA,OAAO,MAAMc,OAAO,GAAG;EACrB;EACAC,KAAK,EAAGX,IAAe,IAA6B;IAClD,OAAOjB,GAAG,CAAC6B,IAAI,CAAC,aAAa,EAAEZ,IAAI,CAAC;EACtC,CAAC;EAED;EACAa,UAAU,EAAEA,CAAA,KAA4B;IACtC,OAAO9B,GAAG,CAAC+B,GAAG,CAAC,eAAe,CAAC;EACjC,CAAC;EAED;EACAC,cAAc,EAAGf,IAAwB,IAAmC;IAC1E,OAAOjB,GAAG,CAAC6B,IAAI,CAAC,uBAAuB,EAAEZ,IAAI,CAAC;EAChD;AACF,CAAC;;AAED;AACA,OAAO,MAAMgB,UAAU,GAAG;EACxB;EACAC,OAAO,EAAGC,MAA2B,IAAmC;IACtE,OAAOnC,GAAG,CAAC+B,GAAG,CAAC,WAAW,EAAE;MAAEI;IAAO,CAAC,CAAC;EACzC,CAAC;EAED;EACAC,OAAO,EAAGC,EAAU,IAAuB;IACzC,OAAOrC,GAAG,CAAC+B,GAAG,CAAC,aAAaM,EAAE,EAAE,CAAC;EACnC,CAAC;EAED;EACApC,MAAM,EAAGgB,IAAuB,IAAuB;IACrD,OAAOjB,GAAG,CAAC6B,IAAI,CAAC,WAAW,EAAEZ,IAAI,CAAC;EACpC,CAAC;EAED;EACAqB,MAAM,EAAEA,CAACD,EAAU,EAAEpB,IAAgC,KAAuB;IAC1E,OAAOjB,GAAG,CAACuC,KAAK,CAAC,aAAaF,EAAE,EAAE,EAAEpB,IAAI,CAAC;EAC3C,CAAC;EAED;EACAuB,MAAM,EAAGH,EAAU,IAAoB;IACrC,OAAOrC,GAAG,CAACwC,MAAM,CAAC,aAAaH,EAAE,EAAE,CAAC;EACtC,CAAC;EAED;EACAI,QAAQ,EAAEA,CAAA,KAAqC;IAC7C,OAAOzC,GAAG,CAAC+B,GAAG,CAAC,iBAAiB,CAAC;EACnC,CAAC;EAED;EACAW,SAAS,EAAEA,CAAA,KAAyB;IAClC,OAAO1C,GAAG,CAAC+B,GAAG,CAAC,kBAAkB,CAAC;EACpC,CAAC;EAED;EACAY,aAAa,EAAEA,CAAA,KAAyB;IACtC,OAAO3C,GAAG,CAAC+B,GAAG,CAAC,sBAAsB,CAAC;EACxC,CAAC;EAED;EACAa,kBAAkB,EAAGC,aAAqB,IAAuB;IAC/D,OAAO7C,GAAG,CAAC+B,GAAG,CAAC,oBAAoBc,aAAa,EAAE,CAAC;EACrD;AACF,CAAC;AAED,eAAe7C,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}