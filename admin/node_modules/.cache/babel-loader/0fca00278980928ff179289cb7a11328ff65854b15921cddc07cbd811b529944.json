{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"active\", \"showActiveCls\", \"suffixIcon\", \"format\", \"validateFormat\", \"onChange\", \"onInput\", \"helped\", \"onHelp\", \"onSubmit\", \"onKeyDown\", \"preserveInvalidOnBlur\", \"invalid\", \"clearIcon\"];\nimport classNames from 'classnames';\nimport { useEvent } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { leftPad } from \"../../utils/miscUtil\";\nimport PickerContext from \"../context\";\nimport useLockEffect from \"../hooks/useLockEffect\";\nimport Icon from \"./Icon\";\nimport MaskFormat from \"./MaskFormat\";\nimport { getMaskRange } from \"./util\";\n\n// Format logic\n//\n// First time on focus:\n//  1. check if the text is valid, if not fill with format\n//  2. set highlight cell to the first cell\n// Cells\n//  1. Selection the index cell, set inner `cacheValue` to ''\n//  2. Key input filter non-number char, patch after the `cacheValue`\n//    1. Replace the `cacheValue` with input align the cell length\n//    2. Re-selection the mask cell\n//  3. If `cacheValue` match the limit length or cell format (like 1 ~ 12 month), go to next cell\n\nvar Input = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var active = props.active,\n    _props$showActiveCls = props.showActiveCls,\n    showActiveCls = _props$showActiveCls === void 0 ? true : _props$showActiveCls,\n    suffixIcon = props.suffixIcon,\n    format = props.format,\n    validateFormat = props.validateFormat,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    helped = props.helped,\n    onHelp = props.onHelp,\n    onSubmit = props.onSubmit,\n    onKeyDown = props.onKeyDown,\n    _props$preserveInvali = props.preserveInvalidOnBlur,\n    preserveInvalidOnBlur = _props$preserveInvali === void 0 ? false : _props$preserveInvali,\n    invalid = props.invalid,\n    clearIcon = props.clearIcon,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var value = props.value,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onMouseUp = props.onMouseUp;\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls,\n    _React$useContext$inp = _React$useContext.input,\n    Component = _React$useContext$inp === void 0 ? 'input' : _React$useContext$inp;\n  var inputPrefixCls = \"\".concat(prefixCls, \"-input\");\n\n  // ======================== Value =========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var _React$useState3 = React.useState(value),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    internalInputValue = _React$useState4[0],\n    setInputValue = _React$useState4[1];\n  var _React$useState5 = React.useState(''),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    focusCellText = _React$useState6[0],\n    setFocusCellText = _React$useState6[1];\n  var _React$useState7 = React.useState(null),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    focusCellIndex = _React$useState8[0],\n    setFocusCellIndex = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    forceSelectionSyncMark = _React$useState10[0],\n    forceSelectionSync = _React$useState10[1];\n  var inputValue = internalInputValue || '';\n\n  // Sync value if needed\n  React.useEffect(function () {\n    setInputValue(value);\n  }, [value]);\n\n  // ========================= Refs =========================\n  var holderRef = React.useRef();\n  var inputRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: holderRef.current,\n      inputElement: inputRef.current,\n      focus: function focus(options) {\n        inputRef.current.focus(options);\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  });\n\n  // ======================== Format ========================\n  var maskFormat = React.useMemo(function () {\n    return new MaskFormat(format || '');\n  }, [format]);\n  var _React$useMemo = React.useMemo(function () {\n      if (helped) {\n        return [0, 0];\n      }\n      return maskFormat.getSelection(focusCellIndex);\n    }, [maskFormat, focusCellIndex, helped]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    selectionStart = _React$useMemo2[0],\n    selectionEnd = _React$useMemo2[1];\n\n  // ======================== Modify ========================\n  // When input modify content, trigger `onHelp` if is not the format\n  var onModify = function onModify(text) {\n    if (text && text !== format && text !== value) {\n      onHelp();\n    }\n  };\n\n  // ======================== Change ========================\n  /**\n   * Triggered by paste, keyDown and focus to show format\n   */\n  var triggerInputChange = useEvent(function (text) {\n    if (validateFormat(text)) {\n      onChange(text);\n    }\n    setInputValue(text);\n    onModify(text);\n  });\n\n  // Directly trigger `onChange` if `format` is empty\n  var onInternalChange = function onInternalChange(event) {\n    // Hack `onChange` with format to do nothing\n    if (!format) {\n      var text = event.target.value;\n      onModify(text);\n      setInputValue(text);\n      onChange(text);\n    }\n  };\n  var onFormatPaste = function onFormatPaste(event) {\n    // Get paste text\n    var pasteText = event.clipboardData.getData('text');\n    if (validateFormat(pasteText)) {\n      triggerInputChange(pasteText);\n    }\n  };\n\n  // ======================== Mouse =========================\n  // When `mouseDown` get focus, it's better to not to change the selection\n  // Since the up position maybe not is the first cell\n  var mouseDownRef = React.useRef(false);\n  var onFormatMouseDown = function onFormatMouseDown() {\n    mouseDownRef.current = true;\n  };\n  var onFormatMouseUp = function onFormatMouseUp(event) {\n    var _ref = event.target,\n      start = _ref.selectionStart;\n    var closeMaskIndex = maskFormat.getMaskCellIndex(start);\n    setFocusCellIndex(closeMaskIndex);\n\n    // Force update the selection\n    forceSelectionSync({});\n    onMouseUp === null || onMouseUp === void 0 || onMouseUp(event);\n    mouseDownRef.current = false;\n  };\n\n  // ====================== Focus Blur ======================\n  var onFormatFocus = function onFormatFocus(event) {\n    setFocused(true);\n    setFocusCellIndex(0);\n    setFocusCellText('');\n    onFocus(event);\n  };\n  var onSharedBlur = function onSharedBlur(event) {\n    onBlur(event);\n  };\n  var onFormatBlur = function onFormatBlur(event) {\n    setFocused(false);\n    onSharedBlur(event);\n  };\n\n  // ======================== Active ========================\n  // Check if blur need reset input value\n  useLockEffect(active, function () {\n    if (!active && !preserveInvalidOnBlur) {\n      setInputValue(value);\n    }\n  });\n\n  // ======================= Keyboard =======================\n  var onSharedKeyDown = function onSharedKeyDown(event) {\n    if (event.key === 'Enter' && validateFormat(inputValue)) {\n      onSubmit();\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n  };\n  var onFormatKeyDown = function onFormatKeyDown(event) {\n    onSharedKeyDown(event);\n    var key = event.key;\n\n    // Save the cache with cell text\n    var nextCellText = null;\n\n    // Fill in the input\n    var nextFillText = null;\n    var maskCellLen = selectionEnd - selectionStart;\n    var cellFormat = format.slice(selectionStart, selectionEnd);\n\n    // Cell Index\n    var offsetCellIndex = function offsetCellIndex(offset) {\n      setFocusCellIndex(function (idx) {\n        var nextIndex = idx + offset;\n        nextIndex = Math.max(nextIndex, 0);\n        nextIndex = Math.min(nextIndex, maskFormat.size() - 1);\n        return nextIndex;\n      });\n    };\n\n    // Range\n    var offsetCellValue = function offsetCellValue(offset) {\n      var _getMaskRange = getMaskRange(cellFormat),\n        _getMaskRange2 = _slicedToArray(_getMaskRange, 3),\n        rangeStart = _getMaskRange2[0],\n        rangeEnd = _getMaskRange2[1],\n        rangeDefault = _getMaskRange2[2];\n      var currentText = inputValue.slice(selectionStart, selectionEnd);\n      var currentTextNum = Number(currentText);\n      if (isNaN(currentTextNum)) {\n        return String(rangeDefault ? rangeDefault : offset > 0 ? rangeStart : rangeEnd);\n      }\n      var num = currentTextNum + offset;\n      var range = rangeEnd - rangeStart + 1;\n      return String(rangeStart + (range + num - rangeStart) % range);\n    };\n    switch (key) {\n      // =============== Remove ===============\n      case 'Backspace':\n      case 'Delete':\n        nextCellText = '';\n        nextFillText = cellFormat;\n        break;\n\n      // =============== Arrows ===============\n      // Left key\n      case 'ArrowLeft':\n        nextCellText = '';\n        offsetCellIndex(-1);\n        break;\n\n      // Right key\n      case 'ArrowRight':\n        nextCellText = '';\n        offsetCellIndex(1);\n        break;\n\n      // Up key\n      case 'ArrowUp':\n        nextCellText = '';\n        nextFillText = offsetCellValue(1);\n        break;\n\n      // Down key\n      case 'ArrowDown':\n        nextCellText = '';\n        nextFillText = offsetCellValue(-1);\n        break;\n\n      // =============== Number ===============\n      default:\n        if (!isNaN(Number(key))) {\n          nextCellText = focusCellText + key;\n          nextFillText = nextCellText;\n        }\n        break;\n    }\n\n    // Update cell text\n    if (nextCellText !== null) {\n      setFocusCellText(nextCellText);\n      if (nextCellText.length >= maskCellLen) {\n        // Go to next cell\n        offsetCellIndex(1);\n        setFocusCellText('');\n      }\n    }\n\n    // Update the input text\n    if (nextFillText !== null) {\n      // Replace selection range with `nextCellText`\n      var nextFocusValue =\n      // before\n      inputValue.slice(0, selectionStart) +\n      // replace\n      leftPad(nextFillText, maskCellLen) +\n      // after\n      inputValue.slice(selectionEnd);\n      triggerInputChange(nextFocusValue.slice(0, format.length));\n    }\n\n    // Always trigger selection sync after key down\n    forceSelectionSync({});\n  };\n\n  // ======================== Format ========================\n  var rafRef = React.useRef();\n  useLayoutEffect(function () {\n    if (!focused || !format || mouseDownRef.current) {\n      return;\n    }\n\n    // Reset with format if not match\n    if (!maskFormat.match(inputValue)) {\n      triggerInputChange(format);\n      return;\n    }\n\n    // Match the selection range\n    inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n\n    // Chrome has the bug anchor position looks not correct but actually correct\n    rafRef.current = raf(function () {\n      inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n    });\n    return function () {\n      raf.cancel(rafRef.current);\n    };\n  }, [maskFormat, format, focused, inputValue, focusCellIndex, selectionStart, selectionEnd, forceSelectionSyncMark, triggerInputChange]);\n\n  // ======================== Render ========================\n  // Input props for format\n  var inputProps = format ? {\n    onFocus: onFormatFocus,\n    onBlur: onFormatBlur,\n    onKeyDown: onFormatKeyDown,\n    onMouseDown: onFormatMouseDown,\n    onMouseUp: onFormatMouseUp,\n    onPaste: onFormatPaste\n  } : {};\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: holderRef,\n    className: classNames(inputPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(inputPrefixCls, \"-active\"), active && showActiveCls), \"\".concat(inputPrefixCls, \"-placeholder\"), helped))\n  }, /*#__PURE__*/React.createElement(Component, _extends({\n    ref: inputRef,\n    \"aria-invalid\": invalid,\n    autoComplete: \"off\"\n  }, restProps, {\n    onKeyDown: onSharedKeyDown,\n    onBlur: onSharedBlur\n    // Replace with format\n  }, inputProps, {\n    // Value\n    value: inputValue,\n    onChange: onInternalChange\n  })), /*#__PURE__*/React.createElement(Icon, {\n    type: \"suffix\",\n    icon: suffixIcon\n  }), clearIcon);\n});\nif (process.env.NODE_ENV !== 'production') {\n  Input.displayName = 'Input';\n}\nexport default Input;", "map": {"version": 3, "names": ["_extends", "_defineProperty", "_slicedToArray", "_objectWithoutProperties", "_excluded", "classNames", "useEvent", "useLayoutEffect", "raf", "React", "leftPad", "<PERSON>er<PERSON>ontext", "useLockEffect", "Icon", "MaskFormat", "getMaskRange", "Input", "forwardRef", "props", "ref", "active", "_props$showActiveCls", "showActiveCls", "suffixIcon", "format", "validateFormat", "onChange", "onInput", "helped", "onHelp", "onSubmit", "onKeyDown", "_props$preserveInvali", "preserveInvalidOnBlur", "invalid", "clearIcon", "restProps", "value", "onFocus", "onBlur", "onMouseUp", "_React$useContext", "useContext", "prefixCls", "_React$useContext$inp", "input", "Component", "inputPrefixCls", "concat", "_React$useState", "useState", "_React$useState2", "focused", "setFocused", "_React$useState3", "_React$useState4", "internalInputValue", "setInputValue", "_React$useState5", "_React$useState6", "focusCellText", "setFocusCellText", "_React$useState7", "_React$useState8", "focusCellIndex", "setFocusCellIndex", "_React$useState9", "_React$useState10", "forceSelectionSyncMark", "forceSelectionSync", "inputValue", "useEffect", "holder<PERSON><PERSON>", "useRef", "inputRef", "useImperativeHandle", "nativeElement", "current", "inputElement", "focus", "options", "blur", "maskFormat", "useMemo", "_React$useMemo", "getSelection", "_React$useMemo2", "selectionStart", "selectionEnd", "onModify", "text", "triggerInputChange", "onInternalChange", "event", "target", "onFormatPaste", "pasteText", "clipboardData", "getData", "mouseDownRef", "onFormatMouseDown", "onFormatMouseUp", "_ref", "start", "closeMaskIndex", "getMaskCellIndex", "onFormatFocus", "onSharedBlur", "onFormatBlur", "onSharedKeyDown", "key", "onFormatKeyDown", "nextCellText", "nextFillText", "maskCellLen", "cellFormat", "slice", "offsetCellIndex", "offset", "idx", "nextIndex", "Math", "max", "min", "size", "offsetCellValue", "_getMaskRange", "_getMaskRange2", "rangeStart", "rangeEnd", "rangeDefault", "currentText", "currentTextNum", "Number", "isNaN", "String", "num", "range", "length", "nextFocusValue", "rafRef", "match", "setSelectionRange", "cancel", "inputProps", "onMouseDown", "onPaste", "createElement", "className", "autoComplete", "type", "icon", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-picker/es/PickerInput/Selector/Input.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"active\", \"showActiveCls\", \"suffixIcon\", \"format\", \"validateFormat\", \"onChange\", \"onInput\", \"helped\", \"onHelp\", \"onSubmit\", \"onKeyDown\", \"preserveInvalidOnBlur\", \"invalid\", \"clearIcon\"];\nimport classNames from 'classnames';\nimport { useEvent } from 'rc-util';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { leftPad } from \"../../utils/miscUtil\";\nimport PickerContext from \"../context\";\nimport useLockEffect from \"../hooks/useLockEffect\";\nimport Icon from \"./Icon\";\nimport MaskFormat from \"./MaskFormat\";\nimport { getMaskRange } from \"./util\";\n\n// Format logic\n//\n// First time on focus:\n//  1. check if the text is valid, if not fill with format\n//  2. set highlight cell to the first cell\n// Cells\n//  1. Selection the index cell, set inner `cacheValue` to ''\n//  2. Key input filter non-number char, patch after the `cacheValue`\n//    1. Replace the `cacheValue` with input align the cell length\n//    2. Re-selection the mask cell\n//  3. If `cacheValue` match the limit length or cell format (like 1 ~ 12 month), go to next cell\n\nvar Input = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var active = props.active,\n    _props$showActiveCls = props.showActiveCls,\n    showActiveCls = _props$showActiveCls === void 0 ? true : _props$showActiveCls,\n    suffixIcon = props.suffixIcon,\n    format = props.format,\n    validateFormat = props.validateFormat,\n    onChange = props.onChange,\n    onInput = props.onInput,\n    helped = props.helped,\n    onHelp = props.onHelp,\n    onSubmit = props.onSubmit,\n    onKeyDown = props.onKeyDown,\n    _props$preserveInvali = props.preserveInvalidOnBlur,\n    preserveInvalidOnBlur = _props$preserveInvali === void 0 ? false : _props$preserveInvali,\n    invalid = props.invalid,\n    clearIcon = props.clearIcon,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var value = props.value,\n    onFocus = props.onFocus,\n    onBlur = props.onBlur,\n    onMouseUp = props.onMouseUp;\n  var _React$useContext = React.useContext(PickerContext),\n    prefixCls = _React$useContext.prefixCls,\n    _React$useContext$inp = _React$useContext.input,\n    Component = _React$useContext$inp === void 0 ? 'input' : _React$useContext$inp;\n  var inputPrefixCls = \"\".concat(prefixCls, \"-input\");\n\n  // ======================== Value =========================\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    focused = _React$useState2[0],\n    setFocused = _React$useState2[1];\n  var _React$useState3 = React.useState(value),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    internalInputValue = _React$useState4[0],\n    setInputValue = _React$useState4[1];\n  var _React$useState5 = React.useState(''),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    focusCellText = _React$useState6[0],\n    setFocusCellText = _React$useState6[1];\n  var _React$useState7 = React.useState(null),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    focusCellIndex = _React$useState8[0],\n    setFocusCellIndex = _React$useState8[1];\n  var _React$useState9 = React.useState(null),\n    _React$useState10 = _slicedToArray(_React$useState9, 2),\n    forceSelectionSyncMark = _React$useState10[0],\n    forceSelectionSync = _React$useState10[1];\n  var inputValue = internalInputValue || '';\n\n  // Sync value if needed\n  React.useEffect(function () {\n    setInputValue(value);\n  }, [value]);\n\n  // ========================= Refs =========================\n  var holderRef = React.useRef();\n  var inputRef = React.useRef();\n  React.useImperativeHandle(ref, function () {\n    return {\n      nativeElement: holderRef.current,\n      inputElement: inputRef.current,\n      focus: function focus(options) {\n        inputRef.current.focus(options);\n      },\n      blur: function blur() {\n        inputRef.current.blur();\n      }\n    };\n  });\n\n  // ======================== Format ========================\n  var maskFormat = React.useMemo(function () {\n    return new MaskFormat(format || '');\n  }, [format]);\n  var _React$useMemo = React.useMemo(function () {\n      if (helped) {\n        return [0, 0];\n      }\n      return maskFormat.getSelection(focusCellIndex);\n    }, [maskFormat, focusCellIndex, helped]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    selectionStart = _React$useMemo2[0],\n    selectionEnd = _React$useMemo2[1];\n\n  // ======================== Modify ========================\n  // When input modify content, trigger `onHelp` if is not the format\n  var onModify = function onModify(text) {\n    if (text && text !== format && text !== value) {\n      onHelp();\n    }\n  };\n\n  // ======================== Change ========================\n  /**\n   * Triggered by paste, keyDown and focus to show format\n   */\n  var triggerInputChange = useEvent(function (text) {\n    if (validateFormat(text)) {\n      onChange(text);\n    }\n    setInputValue(text);\n    onModify(text);\n  });\n\n  // Directly trigger `onChange` if `format` is empty\n  var onInternalChange = function onInternalChange(event) {\n    // Hack `onChange` with format to do nothing\n    if (!format) {\n      var text = event.target.value;\n      onModify(text);\n      setInputValue(text);\n      onChange(text);\n    }\n  };\n  var onFormatPaste = function onFormatPaste(event) {\n    // Get paste text\n    var pasteText = event.clipboardData.getData('text');\n    if (validateFormat(pasteText)) {\n      triggerInputChange(pasteText);\n    }\n  };\n\n  // ======================== Mouse =========================\n  // When `mouseDown` get focus, it's better to not to change the selection\n  // Since the up position maybe not is the first cell\n  var mouseDownRef = React.useRef(false);\n  var onFormatMouseDown = function onFormatMouseDown() {\n    mouseDownRef.current = true;\n  };\n  var onFormatMouseUp = function onFormatMouseUp(event) {\n    var _ref = event.target,\n      start = _ref.selectionStart;\n    var closeMaskIndex = maskFormat.getMaskCellIndex(start);\n    setFocusCellIndex(closeMaskIndex);\n\n    // Force update the selection\n    forceSelectionSync({});\n    onMouseUp === null || onMouseUp === void 0 || onMouseUp(event);\n    mouseDownRef.current = false;\n  };\n\n  // ====================== Focus Blur ======================\n  var onFormatFocus = function onFormatFocus(event) {\n    setFocused(true);\n    setFocusCellIndex(0);\n    setFocusCellText('');\n    onFocus(event);\n  };\n  var onSharedBlur = function onSharedBlur(event) {\n    onBlur(event);\n  };\n  var onFormatBlur = function onFormatBlur(event) {\n    setFocused(false);\n    onSharedBlur(event);\n  };\n\n  // ======================== Active ========================\n  // Check if blur need reset input value\n  useLockEffect(active, function () {\n    if (!active && !preserveInvalidOnBlur) {\n      setInputValue(value);\n    }\n  });\n\n  // ======================= Keyboard =======================\n  var onSharedKeyDown = function onSharedKeyDown(event) {\n    if (event.key === 'Enter' && validateFormat(inputValue)) {\n      onSubmit();\n    }\n    onKeyDown === null || onKeyDown === void 0 || onKeyDown(event);\n  };\n  var onFormatKeyDown = function onFormatKeyDown(event) {\n    onSharedKeyDown(event);\n    var key = event.key;\n\n    // Save the cache with cell text\n    var nextCellText = null;\n\n    // Fill in the input\n    var nextFillText = null;\n    var maskCellLen = selectionEnd - selectionStart;\n    var cellFormat = format.slice(selectionStart, selectionEnd);\n\n    // Cell Index\n    var offsetCellIndex = function offsetCellIndex(offset) {\n      setFocusCellIndex(function (idx) {\n        var nextIndex = idx + offset;\n        nextIndex = Math.max(nextIndex, 0);\n        nextIndex = Math.min(nextIndex, maskFormat.size() - 1);\n        return nextIndex;\n      });\n    };\n\n    // Range\n    var offsetCellValue = function offsetCellValue(offset) {\n      var _getMaskRange = getMaskRange(cellFormat),\n        _getMaskRange2 = _slicedToArray(_getMaskRange, 3),\n        rangeStart = _getMaskRange2[0],\n        rangeEnd = _getMaskRange2[1],\n        rangeDefault = _getMaskRange2[2];\n      var currentText = inputValue.slice(selectionStart, selectionEnd);\n      var currentTextNum = Number(currentText);\n      if (isNaN(currentTextNum)) {\n        return String(rangeDefault ? rangeDefault : offset > 0 ? rangeStart : rangeEnd);\n      }\n      var num = currentTextNum + offset;\n      var range = rangeEnd - rangeStart + 1;\n      return String(rangeStart + (range + num - rangeStart) % range);\n    };\n    switch (key) {\n      // =============== Remove ===============\n      case 'Backspace':\n      case 'Delete':\n        nextCellText = '';\n        nextFillText = cellFormat;\n        break;\n\n      // =============== Arrows ===============\n      // Left key\n      case 'ArrowLeft':\n        nextCellText = '';\n        offsetCellIndex(-1);\n        break;\n\n      // Right key\n      case 'ArrowRight':\n        nextCellText = '';\n        offsetCellIndex(1);\n        break;\n\n      // Up key\n      case 'ArrowUp':\n        nextCellText = '';\n        nextFillText = offsetCellValue(1);\n        break;\n\n      // Down key\n      case 'ArrowDown':\n        nextCellText = '';\n        nextFillText = offsetCellValue(-1);\n        break;\n\n      // =============== Number ===============\n      default:\n        if (!isNaN(Number(key))) {\n          nextCellText = focusCellText + key;\n          nextFillText = nextCellText;\n        }\n        break;\n    }\n\n    // Update cell text\n    if (nextCellText !== null) {\n      setFocusCellText(nextCellText);\n      if (nextCellText.length >= maskCellLen) {\n        // Go to next cell\n        offsetCellIndex(1);\n        setFocusCellText('');\n      }\n    }\n\n    // Update the input text\n    if (nextFillText !== null) {\n      // Replace selection range with `nextCellText`\n      var nextFocusValue =\n      // before\n      inputValue.slice(0, selectionStart) +\n      // replace\n      leftPad(nextFillText, maskCellLen) +\n      // after\n      inputValue.slice(selectionEnd);\n      triggerInputChange(nextFocusValue.slice(0, format.length));\n    }\n\n    // Always trigger selection sync after key down\n    forceSelectionSync({});\n  };\n\n  // ======================== Format ========================\n  var rafRef = React.useRef();\n  useLayoutEffect(function () {\n    if (!focused || !format || mouseDownRef.current) {\n      return;\n    }\n\n    // Reset with format if not match\n    if (!maskFormat.match(inputValue)) {\n      triggerInputChange(format);\n      return;\n    }\n\n    // Match the selection range\n    inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n\n    // Chrome has the bug anchor position looks not correct but actually correct\n    rafRef.current = raf(function () {\n      inputRef.current.setSelectionRange(selectionStart, selectionEnd);\n    });\n    return function () {\n      raf.cancel(rafRef.current);\n    };\n  }, [maskFormat, format, focused, inputValue, focusCellIndex, selectionStart, selectionEnd, forceSelectionSyncMark, triggerInputChange]);\n\n  // ======================== Render ========================\n  // Input props for format\n  var inputProps = format ? {\n    onFocus: onFormatFocus,\n    onBlur: onFormatBlur,\n    onKeyDown: onFormatKeyDown,\n    onMouseDown: onFormatMouseDown,\n    onMouseUp: onFormatMouseUp,\n    onPaste: onFormatPaste\n  } : {};\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: holderRef,\n    className: classNames(inputPrefixCls, _defineProperty(_defineProperty({}, \"\".concat(inputPrefixCls, \"-active\"), active && showActiveCls), \"\".concat(inputPrefixCls, \"-placeholder\"), helped))\n  }, /*#__PURE__*/React.createElement(Component, _extends({\n    ref: inputRef,\n    \"aria-invalid\": invalid,\n    autoComplete: \"off\"\n  }, restProps, {\n    onKeyDown: onSharedKeyDown,\n    onBlur: onSharedBlur\n    // Replace with format\n  }, inputProps, {\n    // Value\n    value: inputValue,\n    onChange: onInternalChange\n  })), /*#__PURE__*/React.createElement(Icon, {\n    type: \"suffix\",\n    icon: suffixIcon\n  }), clearIcon);\n});\nif (process.env.NODE_ENV !== 'production') {\n  Input.displayName = 'Input';\n}\nexport default Input;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,uBAAuB,EAAE,SAAS,EAAE,WAAW,CAAC;AAC1M,OAAOC,UAAU,MAAM,YAAY;AACnC,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,sBAAsB;AAC9C,OAAOC,aAAa,MAAM,YAAY;AACtC,OAAOC,aAAa,MAAM,wBAAwB;AAClD,OAAOC,IAAI,MAAM,QAAQ;AACzB,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,YAAY,QAAQ,QAAQ;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,KAAK,GAAG,aAAaP,KAAK,CAACQ,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAC9D,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACvBC,oBAAoB,GAAGH,KAAK,CAACI,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,oBAAoB;IAC7EE,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,cAAc,GAAGP,KAAK,CAACO,cAAc;IACrCC,QAAQ,GAAGR,KAAK,CAACQ,QAAQ;IACzBC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,MAAM,GAAGV,KAAK,CAACU,MAAM;IACrBC,MAAM,GAAGX,KAAK,CAACW,MAAM;IACrBC,QAAQ,GAAGZ,KAAK,CAACY,QAAQ;IACzBC,SAAS,GAAGb,KAAK,CAACa,SAAS;IAC3BC,qBAAqB,GAAGd,KAAK,CAACe,qBAAqB;IACnDA,qBAAqB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;IACxFE,OAAO,GAAGhB,KAAK,CAACgB,OAAO;IACvBC,SAAS,GAAGjB,KAAK,CAACiB,SAAS;IAC3BC,SAAS,GAAGjC,wBAAwB,CAACe,KAAK,EAAEd,SAAS,CAAC;EACxD,IAAIiC,KAAK,GAAGnB,KAAK,CAACmB,KAAK;IACrBC,OAAO,GAAGpB,KAAK,CAACoB,OAAO;IACvBC,MAAM,GAAGrB,KAAK,CAACqB,MAAM;IACrBC,SAAS,GAAGtB,KAAK,CAACsB,SAAS;EAC7B,IAAIC,iBAAiB,GAAGhC,KAAK,CAACiC,UAAU,CAAC/B,aAAa,CAAC;IACrDgC,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,qBAAqB,GAAGH,iBAAiB,CAACI,KAAK;IAC/CC,SAAS,GAAGF,qBAAqB,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,qBAAqB;EAChF,IAAIG,cAAc,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,QAAQ,CAAC;;EAEnD;EACA,IAAIM,eAAe,GAAGxC,KAAK,CAACyC,QAAQ,CAAC,KAAK,CAAC;IACzCC,gBAAgB,GAAGjD,cAAc,CAAC+C,eAAe,EAAE,CAAC,CAAC;IACrDG,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC7BE,UAAU,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAClC,IAAIG,gBAAgB,GAAG7C,KAAK,CAACyC,QAAQ,CAACb,KAAK,CAAC;IAC1CkB,gBAAgB,GAAGrD,cAAc,CAACoD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,kBAAkB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACxCE,aAAa,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACrC,IAAIG,gBAAgB,GAAGjD,KAAK,CAACyC,QAAQ,CAAC,EAAE,CAAC;IACvCS,gBAAgB,GAAGzD,cAAc,CAACwD,gBAAgB,EAAE,CAAC,CAAC;IACtDE,aAAa,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACnCE,gBAAgB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACxC,IAAIG,gBAAgB,GAAGrD,KAAK,CAACyC,QAAQ,CAAC,IAAI,CAAC;IACzCa,gBAAgB,GAAG7D,cAAc,CAAC4D,gBAAgB,EAAE,CAAC,CAAC;IACtDE,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzC,IAAIG,gBAAgB,GAAGzD,KAAK,CAACyC,QAAQ,CAAC,IAAI,CAAC;IACzCiB,iBAAiB,GAAGjE,cAAc,CAACgE,gBAAgB,EAAE,CAAC,CAAC;IACvDE,sBAAsB,GAAGD,iBAAiB,CAAC,CAAC,CAAC;IAC7CE,kBAAkB,GAAGF,iBAAiB,CAAC,CAAC,CAAC;EAC3C,IAAIG,UAAU,GAAGd,kBAAkB,IAAI,EAAE;;EAEzC;EACA/C,KAAK,CAAC8D,SAAS,CAAC,YAAY;IAC1Bd,aAAa,CAACpB,KAAK,CAAC;EACtB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,IAAImC,SAAS,GAAG/D,KAAK,CAACgE,MAAM,CAAC,CAAC;EAC9B,IAAIC,QAAQ,GAAGjE,KAAK,CAACgE,MAAM,CAAC,CAAC;EAC7BhE,KAAK,CAACkE,mBAAmB,CAACxD,GAAG,EAAE,YAAY;IACzC,OAAO;MACLyD,aAAa,EAAEJ,SAAS,CAACK,OAAO;MAChCC,YAAY,EAAEJ,QAAQ,CAACG,OAAO;MAC9BE,KAAK,EAAE,SAASA,KAAKA,CAACC,OAAO,EAAE;QAC7BN,QAAQ,CAACG,OAAO,CAACE,KAAK,CAACC,OAAO,CAAC;MACjC,CAAC;MACDC,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;QACpBP,QAAQ,CAACG,OAAO,CAACI,IAAI,CAAC,CAAC;MACzB;IACF,CAAC;EACH,CAAC,CAAC;;EAEF;EACA,IAAIC,UAAU,GAAGzE,KAAK,CAAC0E,OAAO,CAAC,YAAY;IACzC,OAAO,IAAIrE,UAAU,CAACU,MAAM,IAAI,EAAE,CAAC;EACrC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZ,IAAI4D,cAAc,GAAG3E,KAAK,CAAC0E,OAAO,CAAC,YAAY;MAC3C,IAAIvD,MAAM,EAAE;QACV,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;MACf;MACA,OAAOsD,UAAU,CAACG,YAAY,CAACrB,cAAc,CAAC;IAChD,CAAC,EAAE,CAACkB,UAAU,EAAElB,cAAc,EAAEpC,MAAM,CAAC,CAAC;IACxC0D,eAAe,GAAGpF,cAAc,CAACkF,cAAc,EAAE,CAAC,CAAC;IACnDG,cAAc,GAAGD,eAAe,CAAC,CAAC,CAAC;IACnCE,YAAY,GAAGF,eAAe,CAAC,CAAC,CAAC;;EAEnC;EACA;EACA,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAIA,IAAI,IAAIA,IAAI,KAAKlE,MAAM,IAAIkE,IAAI,KAAKrD,KAAK,EAAE;MAC7CR,MAAM,CAAC,CAAC;IACV;EACF,CAAC;;EAED;EACA;AACF;AACA;EACE,IAAI8D,kBAAkB,GAAGrF,QAAQ,CAAC,UAAUoF,IAAI,EAAE;IAChD,IAAIjE,cAAc,CAACiE,IAAI,CAAC,EAAE;MACxBhE,QAAQ,CAACgE,IAAI,CAAC;IAChB;IACAjC,aAAa,CAACiC,IAAI,CAAC;IACnBD,QAAQ,CAACC,IAAI,CAAC;EAChB,CAAC,CAAC;;EAEF;EACA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAE;IACtD;IACA,IAAI,CAACrE,MAAM,EAAE;MACX,IAAIkE,IAAI,GAAGG,KAAK,CAACC,MAAM,CAACzD,KAAK;MAC7BoD,QAAQ,CAACC,IAAI,CAAC;MACdjC,aAAa,CAACiC,IAAI,CAAC;MACnBhE,QAAQ,CAACgE,IAAI,CAAC;IAChB;EACF,CAAC;EACD,IAAIK,aAAa,GAAG,SAASA,aAAaA,CAACF,KAAK,EAAE;IAChD;IACA,IAAIG,SAAS,GAAGH,KAAK,CAACI,aAAa,CAACC,OAAO,CAAC,MAAM,CAAC;IACnD,IAAIzE,cAAc,CAACuE,SAAS,CAAC,EAAE;MAC7BL,kBAAkB,CAACK,SAAS,CAAC;IAC/B;EACF,CAAC;;EAED;EACA;EACA;EACA,IAAIG,YAAY,GAAG1F,KAAK,CAACgE,MAAM,CAAC,KAAK,CAAC;EACtC,IAAI2B,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;IACnDD,YAAY,CAACtB,OAAO,GAAG,IAAI;EAC7B,CAAC;EACD,IAAIwB,eAAe,GAAG,SAASA,eAAeA,CAACR,KAAK,EAAE;IACpD,IAAIS,IAAI,GAAGT,KAAK,CAACC,MAAM;MACrBS,KAAK,GAAGD,IAAI,CAACf,cAAc;IAC7B,IAAIiB,cAAc,GAAGtB,UAAU,CAACuB,gBAAgB,CAACF,KAAK,CAAC;IACvDtC,iBAAiB,CAACuC,cAAc,CAAC;;IAEjC;IACAnC,kBAAkB,CAAC,CAAC,CAAC,CAAC;IACtB7B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACqD,KAAK,CAAC;IAC9DM,YAAY,CAACtB,OAAO,GAAG,KAAK;EAC9B,CAAC;;EAED;EACA,IAAI6B,aAAa,GAAG,SAASA,aAAaA,CAACb,KAAK,EAAE;IAChDxC,UAAU,CAAC,IAAI,CAAC;IAChBY,iBAAiB,CAAC,CAAC,CAAC;IACpBJ,gBAAgB,CAAC,EAAE,CAAC;IACpBvB,OAAO,CAACuD,KAAK,CAAC;EAChB,CAAC;EACD,IAAIc,YAAY,GAAG,SAASA,YAAYA,CAACd,KAAK,EAAE;IAC9CtD,MAAM,CAACsD,KAAK,CAAC;EACf,CAAC;EACD,IAAIe,YAAY,GAAG,SAASA,YAAYA,CAACf,KAAK,EAAE;IAC9CxC,UAAU,CAAC,KAAK,CAAC;IACjBsD,YAAY,CAACd,KAAK,CAAC;EACrB,CAAC;;EAED;EACA;EACAjF,aAAa,CAACQ,MAAM,EAAE,YAAY;IAChC,IAAI,CAACA,MAAM,IAAI,CAACa,qBAAqB,EAAE;MACrCwB,aAAa,CAACpB,KAAK,CAAC;IACtB;EACF,CAAC,CAAC;;EAEF;EACA,IAAIwE,eAAe,GAAG,SAASA,eAAeA,CAAChB,KAAK,EAAE;IACpD,IAAIA,KAAK,CAACiB,GAAG,KAAK,OAAO,IAAIrF,cAAc,CAAC6C,UAAU,CAAC,EAAE;MACvDxC,QAAQ,CAAC,CAAC;IACZ;IACAC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAAC8D,KAAK,CAAC;EAChE,CAAC;EACD,IAAIkB,eAAe,GAAG,SAASA,eAAeA,CAAClB,KAAK,EAAE;IACpDgB,eAAe,CAAChB,KAAK,CAAC;IACtB,IAAIiB,GAAG,GAAGjB,KAAK,CAACiB,GAAG;;IAEnB;IACA,IAAIE,YAAY,GAAG,IAAI;;IAEvB;IACA,IAAIC,YAAY,GAAG,IAAI;IACvB,IAAIC,WAAW,GAAG1B,YAAY,GAAGD,cAAc;IAC/C,IAAI4B,UAAU,GAAG3F,MAAM,CAAC4F,KAAK,CAAC7B,cAAc,EAAEC,YAAY,CAAC;;IAE3D;IACA,IAAI6B,eAAe,GAAG,SAASA,eAAeA,CAACC,MAAM,EAAE;MACrDrD,iBAAiB,CAAC,UAAUsD,GAAG,EAAE;QAC/B,IAAIC,SAAS,GAAGD,GAAG,GAAGD,MAAM;QAC5BE,SAAS,GAAGC,IAAI,CAACC,GAAG,CAACF,SAAS,EAAE,CAAC,CAAC;QAClCA,SAAS,GAAGC,IAAI,CAACE,GAAG,CAACH,SAAS,EAAEtC,UAAU,CAAC0C,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACtD,OAAOJ,SAAS;MAClB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAIK,eAAe,GAAG,SAASA,eAAeA,CAACP,MAAM,EAAE;MACrD,IAAIQ,aAAa,GAAG/G,YAAY,CAACoG,UAAU,CAAC;QAC1CY,cAAc,GAAG7H,cAAc,CAAC4H,aAAa,EAAE,CAAC,CAAC;QACjDE,UAAU,GAAGD,cAAc,CAAC,CAAC,CAAC;QAC9BE,QAAQ,GAAGF,cAAc,CAAC,CAAC,CAAC;QAC5BG,YAAY,GAAGH,cAAc,CAAC,CAAC,CAAC;MAClC,IAAII,WAAW,GAAG7D,UAAU,CAAC8C,KAAK,CAAC7B,cAAc,EAAEC,YAAY,CAAC;MAChE,IAAI4C,cAAc,GAAGC,MAAM,CAACF,WAAW,CAAC;MACxC,IAAIG,KAAK,CAACF,cAAc,CAAC,EAAE;QACzB,OAAOG,MAAM,CAACL,YAAY,GAAGA,YAAY,GAAGZ,MAAM,GAAG,CAAC,GAAGU,UAAU,GAAGC,QAAQ,CAAC;MACjF;MACA,IAAIO,GAAG,GAAGJ,cAAc,GAAGd,MAAM;MACjC,IAAImB,KAAK,GAAGR,QAAQ,GAAGD,UAAU,GAAG,CAAC;MACrC,OAAOO,MAAM,CAACP,UAAU,GAAG,CAACS,KAAK,GAAGD,GAAG,GAAGR,UAAU,IAAIS,KAAK,CAAC;IAChE,CAAC;IACD,QAAQ3B,GAAG;MACT;MACA,KAAK,WAAW;MAChB,KAAK,QAAQ;QACXE,YAAY,GAAG,EAAE;QACjBC,YAAY,GAAGE,UAAU;QACzB;;MAEF;MACA;MACA,KAAK,WAAW;QACdH,YAAY,GAAG,EAAE;QACjBK,eAAe,CAAC,CAAC,CAAC,CAAC;QACnB;;MAEF;MACA,KAAK,YAAY;QACfL,YAAY,GAAG,EAAE;QACjBK,eAAe,CAAC,CAAC,CAAC;QAClB;;MAEF;MACA,KAAK,SAAS;QACZL,YAAY,GAAG,EAAE;QACjBC,YAAY,GAAGY,eAAe,CAAC,CAAC,CAAC;QACjC;;MAEF;MACA,KAAK,WAAW;QACdb,YAAY,GAAG,EAAE;QACjBC,YAAY,GAAGY,eAAe,CAAC,CAAC,CAAC,CAAC;QAClC;;MAEF;MACA;QACE,IAAI,CAACS,KAAK,CAACD,MAAM,CAACvB,GAAG,CAAC,CAAC,EAAE;UACvBE,YAAY,GAAGpD,aAAa,GAAGkD,GAAG;UAClCG,YAAY,GAAGD,YAAY;QAC7B;QACA;IACJ;;IAEA;IACA,IAAIA,YAAY,KAAK,IAAI,EAAE;MACzBnD,gBAAgB,CAACmD,YAAY,CAAC;MAC9B,IAAIA,YAAY,CAAC0B,MAAM,IAAIxB,WAAW,EAAE;QACtC;QACAG,eAAe,CAAC,CAAC,CAAC;QAClBxD,gBAAgB,CAAC,EAAE,CAAC;MACtB;IACF;;IAEA;IACA,IAAIoD,YAAY,KAAK,IAAI,EAAE;MACzB;MACA,IAAI0B,cAAc;MAClB;MACArE,UAAU,CAAC8C,KAAK,CAAC,CAAC,EAAE7B,cAAc,CAAC;MACnC;MACA7E,OAAO,CAACuG,YAAY,EAAEC,WAAW,CAAC;MAClC;MACA5C,UAAU,CAAC8C,KAAK,CAAC5B,YAAY,CAAC;MAC9BG,kBAAkB,CAACgD,cAAc,CAACvB,KAAK,CAAC,CAAC,EAAE5F,MAAM,CAACkH,MAAM,CAAC,CAAC;IAC5D;;IAEA;IACArE,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC;;EAED;EACA,IAAIuE,MAAM,GAAGnI,KAAK,CAACgE,MAAM,CAAC,CAAC;EAC3BlE,eAAe,CAAC,YAAY;IAC1B,IAAI,CAAC6C,OAAO,IAAI,CAAC5B,MAAM,IAAI2E,YAAY,CAACtB,OAAO,EAAE;MAC/C;IACF;;IAEA;IACA,IAAI,CAACK,UAAU,CAAC2D,KAAK,CAACvE,UAAU,CAAC,EAAE;MACjCqB,kBAAkB,CAACnE,MAAM,CAAC;MAC1B;IACF;;IAEA;IACAkD,QAAQ,CAACG,OAAO,CAACiE,iBAAiB,CAACvD,cAAc,EAAEC,YAAY,CAAC;;IAEhE;IACAoD,MAAM,CAAC/D,OAAO,GAAGrE,GAAG,CAAC,YAAY;MAC/BkE,QAAQ,CAACG,OAAO,CAACiE,iBAAiB,CAACvD,cAAc,EAAEC,YAAY,CAAC;IAClE,CAAC,CAAC;IACF,OAAO,YAAY;MACjBhF,GAAG,CAACuI,MAAM,CAACH,MAAM,CAAC/D,OAAO,CAAC;IAC5B,CAAC;EACH,CAAC,EAAE,CAACK,UAAU,EAAE1D,MAAM,EAAE4B,OAAO,EAAEkB,UAAU,EAAEN,cAAc,EAAEuB,cAAc,EAAEC,YAAY,EAAEpB,sBAAsB,EAAEuB,kBAAkB,CAAC,CAAC;;EAEvI;EACA;EACA,IAAIqD,UAAU,GAAGxH,MAAM,GAAG;IACxBc,OAAO,EAAEoE,aAAa;IACtBnE,MAAM,EAAEqE,YAAY;IACpB7E,SAAS,EAAEgF,eAAe;IAC1BkC,WAAW,EAAE7C,iBAAiB;IAC9B5D,SAAS,EAAE6D,eAAe;IAC1B6C,OAAO,EAAEnD;EACX,CAAC,GAAG,CAAC,CAAC;EACN,OAAO,aAAatF,KAAK,CAAC0I,aAAa,CAAC,KAAK,EAAE;IAC7ChI,GAAG,EAAEqD,SAAS;IACd4E,SAAS,EAAE/I,UAAU,CAAC0C,cAAc,EAAE9C,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC+C,MAAM,CAACD,cAAc,EAAE,SAAS,CAAC,EAAE3B,MAAM,IAAIE,aAAa,CAAC,EAAE,EAAE,CAAC0B,MAAM,CAACD,cAAc,EAAE,cAAc,CAAC,EAAEnB,MAAM,CAAC;EAC9L,CAAC,EAAE,aAAanB,KAAK,CAAC0I,aAAa,CAACrG,SAAS,EAAE9C,QAAQ,CAAC;IACtDmB,GAAG,EAAEuD,QAAQ;IACb,cAAc,EAAExC,OAAO;IACvBmH,YAAY,EAAE;EAChB,CAAC,EAAEjH,SAAS,EAAE;IACZL,SAAS,EAAE8E,eAAe;IAC1BtE,MAAM,EAAEoE;IACR;EACF,CAAC,EAAEqC,UAAU,EAAE;IACb;IACA3G,KAAK,EAAEiC,UAAU;IACjB5C,QAAQ,EAAEkE;EACZ,CAAC,CAAC,CAAC,EAAE,aAAanF,KAAK,CAAC0I,aAAa,CAACtI,IAAI,EAAE;IAC1CyI,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAEhI;EACR,CAAC,CAAC,EAAEY,SAAS,CAAC;AAChB,CAAC,CAAC;AACF,IAAIqH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC1I,KAAK,CAAC2I,WAAW,GAAG,OAAO;AAC7B;AACA,eAAe3I,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}