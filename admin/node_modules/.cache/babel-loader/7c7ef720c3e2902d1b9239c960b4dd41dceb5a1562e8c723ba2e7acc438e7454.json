{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Card, Descriptions, Button, Space, Tag, message, Spin, Form, Input, Select, DatePicker, InputNumber, Divider, Row, Col } from \"antd\";\nimport { useParams, useNavigate, useSearchParams } from \"react-router-dom\";\nimport { ArrowLeftOutlined, EditOutlined, SaveOutlined } from \"@ant-design/icons\";\nimport dayjs from \"dayjs\";\nimport { productApi } from \"../services/api\";\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst ProductDetail = () => {\n  _s();\n  const {\n    id\n  } = useParams();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const isEdit = searchParams.get(\"edit\") === \"true\";\n  const [data, setData] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [editMode, setEditMode] = useState(isEdit);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  useEffect(() => {\n    if (id) {\n      fetchData();\n    }\n  }, [id]);\n  useEffect(() => {\n    if (data && editMode) {\n      form.setFieldsValue({\n        name: data.name,\n        alcoholContent: data.alcoholContent,\n        packagingDate: dayjs(data.packagingDate),\n        description: data.description,\n        status: data.status,\n        brand: data.brand,\n        category: data.category,\n        volume: data.volume,\n        batchNumber: data.batchNumber,\n        productionLocation: data.productionLocation\n      });\n    }\n  }, [data, editMode, form]);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const response = await productApi.getById(id);\n      setData(response);\n    } catch (error) {\n      message.error(\"获取数据失败\");\n      navigate(\"/products\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSave = async values => {\n    try {\n      setSaving(true);\n      const updateData = {\n        name: values.name,\n        alcoholContent: values.alcoholContent,\n        packagingDate: values.packagingDate.toISOString(),\n        description: values.description,\n        status: values.status,\n        brand: values.brand,\n        category: values.category,\n        volume: values.volume,\n        batchNumber: values.batchNumber,\n        productionLocation: values.productionLocation\n      };\n      await productApi.update(id, updateData);\n      message.success(\"更新成功\");\n      setEditMode(false);\n      fetchData();\n    } catch (error) {\n      message.error(\"更新失败\");\n    } finally {\n      setSaving(false);\n    }\n  };\n  const getStatusTag = status => {\n    const statusMap = {\n      active: {\n        color: \"green\",\n        text: \"活跃\"\n      },\n      inactive: {\n        color: \"orange\",\n        text: \"停用\"\n      },\n      discontinued: {\n        color: \"red\",\n        text: \"停产\"\n      }\n    };\n    const config = statusMap[status];\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: config.color,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 12\n    }, this);\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        padding: \"50px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 118,\n      columnNumber: 7\n    }, this);\n  }\n  if (!data) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: \"\\u6570\\u636E\\u4E0D\\u5B58\\u5728\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 21\n            }, this),\n            onClick: () => navigate(\"/products\"),\n            children: \"\\u8FD4\\u56DE\\u5217\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            type: \"vertical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0\n            },\n            children: \"\\u5546\\u54C1\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this), !editMode && /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Divider, {\n              type: \"vertical\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 25\n              }, this),\n              onClick: () => setEditMode(true),\n              children: \"\\u7F16\\u8F91\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), editMode ? /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              name: \"name\",\n              rules: [{\n                required: true,\n                message: \"请输入商品名称\"\n              }, {\n                max: 100,\n                message: \"名称不能超过100个字符\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u9152\\u7CBE\\u5EA6 (%)\",\n              name: \"alcoholContent\",\n              rules: [{\n                required: true,\n                message: \"请输入酒精度\"\n              }, {\n                type: \"number\",\n                min: 0,\n                max: 100,\n                message: \"酒精度应在0-100之间\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: \"100%\"\n                },\n                min: 0,\n                max: 100,\n                precision: 1,\n                addonAfter: \"%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5305\\u88C5\\u65E5\\u671F\",\n              name: \"packagingDate\",\n              rules: [{\n                required: true,\n                message: \"请选择包装日期\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: \"100%\"\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u72B6\\u6001\",\n              name: \"status\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u6D3B\\u8DC3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"inactive\",\n                  children: \"\\u505C\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"discontinued\",\n                  children: \"\\u505C\\u4EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u54C1\\u724C\",\n              name: \"brand\",\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5206\\u7C7B\",\n              name: \"category\",\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 219,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5BB9\\u91CF (ml)\",\n              name: \"volume\",\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: \"100%\"\n                },\n                min: 1,\n                addonAfter: \"ml\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u6279\\u6B21\\u53F7\",\n              name: \"batchNumber\",\n              children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u751F\\u4EA7\\u5730\",\n          name: \"productionLocation\",\n          children: /*#__PURE__*/_jsxDEV(Input, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          name: \"description\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: saving,\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 25\n              }, this),\n              children: \"\\u4FDD\\u5B58\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setEditMode(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: data.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 275,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u7F16\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            style: {\n              fontFamily: \"monospace\",\n              fontWeight: \"bold\"\n            },\n            children: data.productNumber\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9152\\u7CBE\\u5EA6\",\n          children: [data.alcoholContent, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5305\\u88C5\\u65E5\\u671F\",\n          children: dayjs(data.packagingDate).format(\"YYYY-MM-DD\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u72B6\\u6001\",\n          children: getStatusTag(data.status)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u54C1\\u724C\",\n          children: data.brand || \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5206\\u7C7B\",\n          children: data.category || \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BB9\\u91CF\",\n          children: data.volume ? `${data.volume}ml` : \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u6279\\u6B21\\u53F7\",\n          children: data.batchNumber || \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u751F\\u4EA7\\u5730\",\n          children: data.productionLocation || \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: dayjs(data.createdAt).format(\"YYYY-MM-DD HH:mm:ss\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n          children: dayjs(data.updatedAt).format(\"YYYY-MM-DD HH:mm:ss\")\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          span: 2,\n          children: data.description || \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 274,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 130,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 129,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductDetail, \"QqnBALePdvqDejM3IK4l8FGlYvc=\", false, function () {\n  return [useParams, useNavigate, useSearchParams, Form.useForm];\n});\n_c = ProductDetail;\nexport default ProductDetail;\nvar _c;\n$RefreshReg$(_c, \"ProductDetail\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Card", "Descriptions", "<PERSON><PERSON>", "Space", "Tag", "message", "Spin", "Form", "Input", "Select", "DatePicker", "InputNumber", "Divider", "Row", "Col", "useParams", "useNavigate", "useSearchParams", "ArrowLeftOutlined", "EditOutlined", "SaveOutlined", "dayjs", "productApi", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "TextArea", "Option", "ProductDetail", "_s", "id", "navigate", "searchParams", "isEdit", "get", "data", "setData", "loading", "setLoading", "editMode", "setEditMode", "saving", "setSaving", "form", "useForm", "fetchData", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "alcoholContent", "packagingDate", "description", "status", "brand", "category", "volume", "batchNumber", "productionLocation", "response", "getById", "error", "handleSave", "values", "updateData", "toISOString", "update", "success", "getStatusTag", "statusMap", "active", "color", "text", "inactive", "discontinued", "config", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "textAlign", "padding", "size", "marginBottom", "icon", "onClick", "type", "margin", "layout", "onFinish", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "rules", "required", "max", "min", "width", "precision", "addonAfter", "value", "rows", "htmlType", "column", "bordered", "span", "fontFamily", "fontWeight", "productNumber", "format", "createdAt", "updatedAt", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/pages/ProductDetail.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport {\n  Card,\n  Descriptions,\n  Button,\n  Space,\n  Tag,\n  message,\n  Spin,\n  Form,\n  Input,\n  Select,\n  DatePicker,\n  InputNumber,\n  Divider,\n  Row,\n  Col,\n} from \"antd\";\nimport { useParams, useNavigate, useSearchParams } from \"react-router-dom\";\nimport {\n  ArrowLeftOutlined,\n  EditOutlined,\n  SaveOutlined,\n} from \"@ant-design/icons\";\nimport dayjs from \"dayjs\";\nimport { productApi, Product } from \"../services/api\";\n\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst ProductDetail: React.FC = () => {\n  const { id } = useParams<{ id: string }>();\n  const navigate = useNavigate();\n  const [searchParams] = useSearchParams();\n  const isEdit = searchParams.get(\"edit\") === \"true\";\n\n  const [data, setData] = useState<Product | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [editMode, setEditMode] = useState(isEdit);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    if (id) {\n      fetchData();\n    }\n  }, [id]);\n\n  useEffect(() => {\n    if (data && editMode) {\n      form.setFieldsValue({\n        name: data.name,\n        alcoholContent: data.alcoholContent,\n        packagingDate: dayjs(data.packagingDate),\n        description: data.description,\n        status: data.status,\n        brand: data.brand,\n        category: data.category,\n        volume: data.volume,\n        batchNumber: data.batchNumber,\n        productionLocation: data.productionLocation,\n      });\n    }\n  }, [data, editMode, form]);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const response = await productApi.getById(id!);\n      setData(response);\n    } catch (error) {\n      message.error(\"获取数据失败\");\n      navigate(\"/products\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async (values: any) => {\n    try {\n      setSaving(true);\n      const updateData = {\n        name: values.name,\n        alcoholContent: values.alcoholContent,\n        packagingDate: values.packagingDate.toISOString(),\n        description: values.description,\n        status: values.status,\n        brand: values.brand,\n        category: values.category,\n        volume: values.volume,\n        batchNumber: values.batchNumber,\n        productionLocation: values.productionLocation,\n      };\n\n      await productApi.update(id!, updateData);\n      message.success(\"更新成功\");\n      setEditMode(false);\n      fetchData();\n    } catch (error) {\n      message.error(\"更新失败\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const getStatusTag = (status: string) => {\n    const statusMap = {\n      active: { color: \"green\", text: \"活跃\" },\n      inactive: { color: \"orange\", text: \"停用\" },\n      discontinued: { color: \"red\", text: \"停产\" },\n    };\n    const config = statusMap[status as keyof typeof statusMap];\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: \"center\", padding: \"50px\" }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  if (!data) {\n    return <div>数据不存在</div>;\n  }\n\n  return (\n    <div>\n      <Card>\n        <div style={{ marginBottom: 24 }}>\n          <Space>\n            <Button\n              icon={<ArrowLeftOutlined />}\n              onClick={() => navigate(\"/products\")}\n            >\n              返回列表\n            </Button>\n            <Divider type=\"vertical\" />\n            <h1 style={{ margin: 0 }}>商品详情</h1>\n            {!editMode && (\n              <>\n                <Divider type=\"vertical\" />\n                <Button\n                  type=\"primary\"\n                  icon={<EditOutlined />}\n                  onClick={() => setEditMode(true)}\n                >\n                  编辑\n                </Button>\n              </>\n            )}\n          </Space>\n        </div>\n\n        {editMode ? (\n          <Form form={form} layout=\"vertical\" onFinish={handleSave}>\n            <Row gutter={24}>\n              <Col xs={24} md={12}>\n                <Form.Item\n                  label=\"商品名称\"\n                  name=\"name\"\n                  rules={[\n                    { required: true, message: \"请输入商品名称\" },\n                    { max: 100, message: \"名称不能超过100个字符\" },\n                  ]}\n                >\n                  <Input />\n                </Form.Item>\n              </Col>\n\n              <Col xs={24} md={12}>\n                <Form.Item\n                  label=\"酒精度 (%)\"\n                  name=\"alcoholContent\"\n                  rules={[\n                    { required: true, message: \"请输入酒精度\" },\n                    {\n                      type: \"number\",\n                      min: 0,\n                      max: 100,\n                      message: \"酒精度应在0-100之间\",\n                    },\n                  ]}\n                >\n                  <InputNumber\n                    style={{ width: \"100%\" }}\n                    min={0}\n                    max={100}\n                    precision={1}\n                    addonAfter=\"%\"\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n\n            <Row gutter={24}>\n              <Col xs={24} md={12}>\n                <Form.Item\n                  label=\"包装日期\"\n                  name=\"packagingDate\"\n                  rules={[{ required: true, message: \"请选择包装日期\" }]}\n                >\n                  <DatePicker style={{ width: \"100%\" }} />\n                </Form.Item>\n              </Col>\n\n              <Col xs={24} md={12}>\n                <Form.Item label=\"状态\" name=\"status\">\n                  <Select>\n                    <Option value=\"active\">活跃</Option>\n                    <Option value=\"inactive\">停用</Option>\n                    <Option value=\"discontinued\">停产</Option>\n                  </Select>\n                </Form.Item>\n              </Col>\n            </Row>\n\n            <Row gutter={24}>\n              <Col xs={24} md={12}>\n                <Form.Item label=\"品牌\" name=\"brand\">\n                  <Input />\n                </Form.Item>\n              </Col>\n\n              <Col xs={24} md={12}>\n                <Form.Item label=\"分类\" name=\"category\">\n                  <Input />\n                </Form.Item>\n              </Col>\n            </Row>\n\n            <Row gutter={24}>\n              <Col xs={24} md={12}>\n                <Form.Item label=\"容量 (ml)\" name=\"volume\">\n                  <InputNumber\n                    style={{ width: \"100%\" }}\n                    min={1}\n                    addonAfter=\"ml\"\n                  />\n                </Form.Item>\n              </Col>\n\n              <Col xs={24} md={12}>\n                <Form.Item label=\"批次号\" name=\"batchNumber\">\n                  <Input />\n                </Form.Item>\n              </Col>\n            </Row>\n\n            <Form.Item label=\"生产地\" name=\"productionLocation\">\n              <Input />\n            </Form.Item>\n\n            <Form.Item label=\"商品描述\" name=\"description\">\n              <TextArea rows={4} />\n            </Form.Item>\n\n            <Form.Item>\n              <Space>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={saving}\n                  icon={<SaveOutlined />}\n                >\n                  保存\n                </Button>\n                <Button onClick={() => setEditMode(false)}>取消</Button>\n              </Space>\n            </Form.Item>\n          </Form>\n        ) : (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"商品名称\" span={2}>\n              {data.name}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"商品编号\">\n              <span style={{ fontFamily: \"monospace\", fontWeight: \"bold\" }}>\n                {data.productNumber}\n              </span>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"酒精度\">\n              {data.alcoholContent}%\n            </Descriptions.Item>\n            <Descriptions.Item label=\"包装日期\">\n              {dayjs(data.packagingDate).format(\"YYYY-MM-DD\")}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"状态\">\n              {getStatusTag(data.status)}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"品牌\">\n              {data.brand || \"-\"}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"分类\">\n              {data.category || \"-\"}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"容量\">\n              {data.volume ? `${data.volume}ml` : \"-\"}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"批次号\">\n              {data.batchNumber || \"-\"}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"生产地\">\n              {data.productionLocation || \"-\"}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">\n              {dayjs(data.createdAt).format(\"YYYY-MM-DD HH:mm:ss\")}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"更新时间\">\n              {dayjs(data.updatedAt).format(\"YYYY-MM-DD HH:mm:ss\")}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"商品描述\" span={2}>\n              {data.description || \"-\"}\n            </Descriptions.Item>\n          </Descriptions>\n        )}\n      </Card>\n    </div>\n  );\n};\n\nexport default ProductDetail;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,YAAY,EACZC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,OAAO,EACPC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,WAAW,EACXC,OAAO,EACPC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SAASC,SAAS,EAAEC,WAAW,EAAEC,eAAe,QAAQ,kBAAkB;AAC1E,SACEC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,QACP,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAiB,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtD,MAAM;EAAEC;AAAS,CAAC,GAAGnB,KAAK;AAC1B,MAAM;EAAEoB;AAAO,CAAC,GAAGnB,MAAM;AAEzB,MAAMoB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAM;IAAEC;EAAG,CAAC,GAAGhB,SAAS,CAAiB,CAAC;EAC1C,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACiB,YAAY,CAAC,GAAGhB,eAAe,CAAC,CAAC;EACxC,MAAMiB,MAAM,GAAGD,YAAY,CAACE,GAAG,CAAC,MAAM,CAAC,KAAK,MAAM;EAElD,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGtC,QAAQ,CAAiB,IAAI,CAAC;EACtD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAACmC,MAAM,CAAC;EAChD,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC6C,IAAI,CAAC,GAAGrC,IAAI,CAACsC,OAAO,CAAC,CAAC;EAE7B/C,SAAS,CAAC,MAAM;IACd,IAAIiC,EAAE,EAAE;MACNe,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACf,EAAE,CAAC,CAAC;EAERjC,SAAS,CAAC,MAAM;IACd,IAAIsC,IAAI,IAAII,QAAQ,EAAE;MACpBI,IAAI,CAACG,cAAc,CAAC;QAClBC,IAAI,EAAEZ,IAAI,CAACY,IAAI;QACfC,cAAc,EAAEb,IAAI,CAACa,cAAc;QACnCC,aAAa,EAAE7B,KAAK,CAACe,IAAI,CAACc,aAAa,CAAC;QACxCC,WAAW,EAAEf,IAAI,CAACe,WAAW;QAC7BC,MAAM,EAAEhB,IAAI,CAACgB,MAAM;QACnBC,KAAK,EAAEjB,IAAI,CAACiB,KAAK;QACjBC,QAAQ,EAAElB,IAAI,CAACkB,QAAQ;QACvBC,MAAM,EAAEnB,IAAI,CAACmB,MAAM;QACnBC,WAAW,EAAEpB,IAAI,CAACoB,WAAW;QAC7BC,kBAAkB,EAAErB,IAAI,CAACqB;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,IAAI,EAAEI,QAAQ,EAAEI,IAAI,CAAC,CAAC;EAE1B,MAAME,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFP,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMpC,UAAU,CAACqC,OAAO,CAAC5B,EAAG,CAAC;MAC9CM,OAAO,CAACqB,QAAQ,CAAC;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAAC,QAAQ,CAAC;MACvB5B,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,SAAS;MACRO,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsB,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxC,IAAI;MACFnB,SAAS,CAAC,IAAI,CAAC;MACf,MAAMoB,UAAU,GAAG;QACjBf,IAAI,EAAEc,MAAM,CAACd,IAAI;QACjBC,cAAc,EAAEa,MAAM,CAACb,cAAc;QACrCC,aAAa,EAAEY,MAAM,CAACZ,aAAa,CAACc,WAAW,CAAC,CAAC;QACjDb,WAAW,EAAEW,MAAM,CAACX,WAAW;QAC/BC,MAAM,EAAEU,MAAM,CAACV,MAAM;QACrBC,KAAK,EAAES,MAAM,CAACT,KAAK;QACnBC,QAAQ,EAAEQ,MAAM,CAACR,QAAQ;QACzBC,MAAM,EAAEO,MAAM,CAACP,MAAM;QACrBC,WAAW,EAAEM,MAAM,CAACN,WAAW;QAC/BC,kBAAkB,EAAEK,MAAM,CAACL;MAC7B,CAAC;MAED,MAAMnC,UAAU,CAAC2C,MAAM,CAAClC,EAAE,EAAGgC,UAAU,CAAC;MACxC1D,OAAO,CAAC6D,OAAO,CAAC,MAAM,CAAC;MACvBzB,WAAW,CAAC,KAAK,CAAC;MAClBK,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdvD,OAAO,CAACuD,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,SAAS;MACRjB,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;EAED,MAAMwB,YAAY,GAAIf,MAAc,IAAK;IACvC,MAAMgB,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,QAAQ,EAAE;QAAEF,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACzCE,YAAY,EAAE;QAAEH,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK;IAC3C,CAAC;IACD,MAAMG,MAAM,GAAGN,SAAS,CAAChB,MAAM,CAA2B;IAC1D,oBAAO5B,OAAA,CAACpB,GAAG;MAACkE,KAAK,EAAEI,MAAM,CAACJ,KAAM;MAAAK,QAAA,EAAED,MAAM,CAACH;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACtD,CAAC;EAED,IAAIzC,OAAO,EAAE;IACX,oBACEd,OAAA;MAAKwD,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAP,QAAA,eACnDnD,OAAA,CAAClB,IAAI;QAAC6E,IAAI,EAAC;MAAO;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,IAAI,CAAC3C,IAAI,EAAE;IACT,oBAAOZ,OAAA;MAAAmD,QAAA,EAAK;IAAK;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EACzB;EAEA,oBACEvD,OAAA;IAAAmD,QAAA,eACEnD,OAAA,CAACxB,IAAI;MAAA2E,QAAA,gBACHnD,OAAA;QAAKwD,KAAK,EAAE;UAAEI,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,eAC/BnD,OAAA,CAACrB,KAAK;UAAAwE,QAAA,gBACJnD,OAAA,CAACtB,MAAM;YACLmF,IAAI,eAAE7D,OAAA,CAACN,iBAAiB;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC5BO,OAAO,EAAEA,CAAA,KAAMtD,QAAQ,CAAC,WAAW,CAAE;YAAA2C,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTvD,OAAA,CAACZ,OAAO;YAAC2E,IAAI,EAAC;UAAU;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3BvD,OAAA;YAAIwD,KAAK,EAAE;cAAEQ,MAAM,EAAE;YAAE,CAAE;YAAAb,QAAA,EAAC;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,EAClC,CAACvC,QAAQ,iBACRhB,OAAA,CAAAE,SAAA;YAAAiD,QAAA,gBACEnD,OAAA,CAACZ,OAAO;cAAC2E,IAAI,EAAC;YAAU;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3BvD,OAAA,CAACtB,MAAM;cACLqF,IAAI,EAAC,SAAS;cACdF,IAAI,eAAE7D,OAAA,CAACL,YAAY;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBO,OAAO,EAAEA,CAAA,KAAM7C,WAAW,CAAC,IAAI,CAAE;cAAAkC,QAAA,EAClC;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA,eACT,CACH;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAELvC,QAAQ,gBACPhB,OAAA,CAACjB,IAAI;QAACqC,IAAI,EAAEA,IAAK;QAAC6C,MAAM,EAAC,UAAU;QAACC,QAAQ,EAAE7B,UAAW;QAAAc,QAAA,gBACvDnD,OAAA,CAACX,GAAG;UAAC8E,MAAM,EAAE,EAAG;UAAAhB,QAAA,gBACdnD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZ/C,IAAI,EAAC,MAAM;cACXgD,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5F,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAE6F,GAAG,EAAE,GAAG;gBAAE7F,OAAO,EAAE;cAAe,CAAC,CACrC;cAAAsE,QAAA,eAEFnD,OAAA,CAAChB,KAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENvD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cACRC,KAAK,EAAC,wBAAS;cACf/C,IAAI,EAAC,gBAAgB;cACrBgD,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5F,OAAO,EAAE;cAAS,CAAC,EACrC;gBACEkF,IAAI,EAAE,QAAQ;gBACdY,GAAG,EAAE,CAAC;gBACND,GAAG,EAAE,GAAG;gBACR7F,OAAO,EAAE;cACX,CAAC,CACD;cAAAsE,QAAA,eAEFnD,OAAA,CAACb,WAAW;gBACVqE,KAAK,EAAE;kBAAEoB,KAAK,EAAE;gBAAO,CAAE;gBACzBD,GAAG,EAAE,CAAE;gBACPD,GAAG,EAAE,GAAI;gBACTG,SAAS,EAAE,CAAE;gBACbC,UAAU,EAAC;cAAG;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA,CAACX,GAAG;UAAC8E,MAAM,EAAE,EAAG;UAAAhB,QAAA,gBACdnD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZ/C,IAAI,EAAC,eAAe;cACpBgD,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5F,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAsE,QAAA,eAEhDnD,OAAA,CAACd,UAAU;gBAACsE,KAAK,EAAE;kBAAEoB,KAAK,EAAE;gBAAO;cAAE;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENvD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cAACC,KAAK,EAAC,cAAI;cAAC/C,IAAI,EAAC,QAAQ;cAAA2B,QAAA,eACjCnD,OAAA,CAACf,MAAM;gBAAAkE,QAAA,gBACLnD,OAAA,CAACI,MAAM;kBAAC2E,KAAK,EAAC,QAAQ;kBAAA5B,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCvD,OAAA,CAACI,MAAM;kBAAC2E,KAAK,EAAC,UAAU;kBAAA5B,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCvD,OAAA,CAACI,MAAM;kBAAC2E,KAAK,EAAC,cAAc;kBAAA5B,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA,CAACX,GAAG;UAAC8E,MAAM,EAAE,EAAG;UAAAhB,QAAA,gBACdnD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cAACC,KAAK,EAAC,cAAI;cAAC/C,IAAI,EAAC,OAAO;cAAA2B,QAAA,eAChCnD,OAAA,CAAChB,KAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENvD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cAACC,KAAK,EAAC,cAAI;cAAC/C,IAAI,EAAC,UAAU;cAAA2B,QAAA,eACnCnD,OAAA,CAAChB,KAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA,CAACX,GAAG;UAAC8E,MAAM,EAAE,EAAG;UAAAhB,QAAA,gBACdnD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cAACC,KAAK,EAAC,mBAAS;cAAC/C,IAAI,EAAC,QAAQ;cAAA2B,QAAA,eACtCnD,OAAA,CAACb,WAAW;gBACVqE,KAAK,EAAE;kBAAEoB,KAAK,EAAE;gBAAO,CAAE;gBACzBD,GAAG,EAAE,CAAE;gBACPG,UAAU,EAAC;cAAI;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENvD,OAAA,CAACV,GAAG;YAAC8E,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAlB,QAAA,eAClBnD,OAAA,CAACjB,IAAI,CAACuF,IAAI;cAACC,KAAK,EAAC,oBAAK;cAAC/C,IAAI,EAAC,aAAa;cAAA2B,QAAA,eACvCnD,OAAA,CAAChB,KAAK;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA,CAACjB,IAAI,CAACuF,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAC/C,IAAI,EAAC,oBAAoB;UAAA2B,QAAA,eAC9CnD,OAAA,CAAChB,KAAK;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZvD,OAAA,CAACjB,IAAI,CAACuF,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAC/C,IAAI,EAAC,aAAa;UAAA2B,QAAA,eACxCnD,OAAA,CAACG,QAAQ;YAAC6E,IAAI,EAAE;UAAE;YAAA5B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEZvD,OAAA,CAACjB,IAAI,CAACuF,IAAI;UAAAnB,QAAA,eACRnD,OAAA,CAACrB,KAAK;YAAAwE,QAAA,gBACJnD,OAAA,CAACtB,MAAM;cACLqF,IAAI,EAAC,SAAS;cACdkB,QAAQ,EAAC,QAAQ;cACjBnE,OAAO,EAAEI,MAAO;cAChB2C,IAAI,eAAE7D,OAAA,CAACJ,YAAY;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAJ,QAAA,EACxB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTvD,OAAA,CAACtB,MAAM;cAACoF,OAAO,EAAEA,CAAA,KAAM7C,WAAW,CAAC,KAAK,CAAE;cAAAkC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,gBAEPvD,OAAA,CAACvB,YAAY;QAACyG,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAAhC,QAAA,gBAC/BnD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACa,IAAI,EAAE,CAAE;UAAAjC,QAAA,EACrCvC,IAAI,CAACY;QAAI;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAApB,QAAA,eAC7BnD,OAAA;YAAMwD,KAAK,EAAE;cAAE6B,UAAU,EAAE,WAAW;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAAnC,QAAA,EAC1DvC,IAAI,CAAC2E;UAAa;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACU,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAApB,QAAA,GAC3BvC,IAAI,CAACa,cAAc,EAAC,GACvB;QAAA;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAApB,QAAA,EAC5BtD,KAAK,CAACe,IAAI,CAACc,aAAa,CAAC,CAAC8D,MAAM,CAAC,YAAY;QAAC;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,cAAI;UAAApB,QAAA,EAC1BR,YAAY,CAAC/B,IAAI,CAACgB,MAAM;QAAC;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,cAAI;UAAApB,QAAA,EAC1BvC,IAAI,CAACiB,KAAK,IAAI;QAAG;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,cAAI;UAAApB,QAAA,EAC1BvC,IAAI,CAACkB,QAAQ,IAAI;QAAG;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,cAAI;UAAApB,QAAA,EAC1BvC,IAAI,CAACmB,MAAM,GAAG,GAAGnB,IAAI,CAACmB,MAAM,IAAI,GAAG;QAAG;UAAAqB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAApB,QAAA,EAC3BvC,IAAI,CAACoB,WAAW,IAAI;QAAG;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAApB,QAAA,EAC3BvC,IAAI,CAACqB,kBAAkB,IAAI;QAAG;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACd,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAApB,QAAA,EAC5BtD,KAAK,CAACe,IAAI,CAAC6E,SAAS,CAAC,CAACD,MAAM,CAAC,qBAAqB;QAAC;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAApB,QAAA,EAC5BtD,KAAK,CAACe,IAAI,CAAC8E,SAAS,CAAC,CAACF,MAAM,CAAC,qBAAqB;QAAC;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACpBvD,OAAA,CAACvB,YAAY,CAAC6F,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACa,IAAI,EAAE,CAAE;UAAAjC,QAAA,EACrCvC,IAAI,CAACe,WAAW,IAAI;QAAG;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CACf;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjD,EAAA,CAlSID,aAAuB;EAAA,QACZd,SAAS,EACPC,WAAW,EACLC,eAAe,EAOvBV,IAAI,CAACsC,OAAO;AAAA;AAAAsE,EAAA,GAVvBtF,aAAuB;AAoS7B,eAAeA,aAAa;AAAC,IAAAsF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}