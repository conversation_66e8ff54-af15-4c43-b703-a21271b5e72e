{"ast": null, "code": "// This icon file is generated automatically.\nvar SkinOutlined = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M870 126H663.8c-17.4 0-32.9 11.9-37 29.3C614.3 208.1 567 246 512 246s-102.3-37.9-114.8-90.7a37.93 37.93 0 00-37-29.3H154a44 44 0 00-44 44v252a44 44 0 0044 44h75v388a44 44 0 0044 44h478a44 44 0 0044-44V466h75a44 44 0 0044-44V170a44 44 0 00-44-44zm-28 268H723v432H301V394H182V198h153.3c28.2 71.2 97.5 120 176.7 120s148.5-48.8 176.7-120H842v196z\"\n      }\n    }]\n  },\n  \"name\": \"skin\",\n  \"theme\": \"outlined\"\n};\nexport default SkinOutlined;", "map": {"version": 3, "names": ["SkinOutlined"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@ant-design/icons-svg/es/asn/SkinOutlined.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar SkinOutlined = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M870 126H663.8c-17.4 0-32.9 11.9-37 29.3C614.3 208.1 567 246 512 246s-102.3-37.9-114.8-90.7a37.93 37.93 0 00-37-29.3H154a44 44 0 00-44 44v252a44 44 0 0044 44h75v388a44 44 0 0044 44h478a44 44 0 0044-44V466h75a44 44 0 0044-44V170a44 44 0 00-44-44zm-28 268H723v432H301V394H182V198h153.3c28.2 71.2 97.5 120 176.7 120s148.5-48.8 176.7-120H842v196z\" } }] }, \"name\": \"skin\", \"theme\": \"outlined\" };\nexport default SkinOutlined;\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAyV;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,MAAM;EAAE,OAAO,EAAE;AAAW,CAAC;AAChiB,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}