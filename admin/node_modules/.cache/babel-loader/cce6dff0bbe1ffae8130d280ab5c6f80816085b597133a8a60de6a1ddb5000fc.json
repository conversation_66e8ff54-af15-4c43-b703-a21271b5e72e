{"ast": null, "code": "import { composeRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport React, { forwardRef, useMemo } from 'react';\nvar Overlay = /*#__PURE__*/forwardRef(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = useMemo(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = composeRef(ref, getNodeRef(overlayNode));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/React.cloneElement(overlayNode, {\n    ref: supportRef(overlayNode) ? composedRef : undefined\n  }));\n});\nexport default Overlay;", "map": {"version": 3, "names": ["composeRef", "getNodeRef", "supportRef", "React", "forwardRef", "useMemo", "Overlay", "props", "ref", "overlay", "arrow", "prefixCls", "overlayNode", "overlayElement", "composedRef", "createElement", "Fragment", "className", "concat", "cloneElement", "undefined"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-dropdown/es/Overlay.js"], "sourcesContent": ["import { composeRef, getNodeRef, supportRef } from \"rc-util/es/ref\";\nimport React, { forwardRef, useMemo } from 'react';\nvar Overlay = /*#__PURE__*/forwardRef(function (props, ref) {\n  var overlay = props.overlay,\n    arrow = props.arrow,\n    prefixCls = props.prefixCls;\n  var overlayNode = useMemo(function () {\n    var overlayElement;\n    if (typeof overlay === 'function') {\n      overlayElement = overlay();\n    } else {\n      overlayElement = overlay;\n    }\n    return overlayElement;\n  }, [overlay]);\n  var composedRef = composeRef(ref, getNodeRef(overlayNode));\n  return /*#__PURE__*/React.createElement(React.Fragment, null, arrow && /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-arrow\")\n  }), /*#__PURE__*/React.cloneElement(overlayNode, {\n    ref: supportRef(overlayNode) ? composedRef : undefined\n  }));\n});\nexport default Overlay;"], "mappings": "AAAA,SAASA,UAAU,EAAEC,UAAU,EAAEC,UAAU,QAAQ,gBAAgB;AACnE,OAAOC,KAAK,IAAIC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAClD,IAAIC,OAAO,GAAG,aAAaF,UAAU,CAAC,UAAUG,KAAK,EAAEC,GAAG,EAAE;EAC1D,IAAIC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACzBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;EAC7B,IAAIC,WAAW,GAAGP,OAAO,CAAC,YAAY;IACpC,IAAIQ,cAAc;IAClB,IAAI,OAAOJ,OAAO,KAAK,UAAU,EAAE;MACjCI,cAAc,GAAGJ,OAAO,CAAC,CAAC;IAC5B,CAAC,MAAM;MACLI,cAAc,GAAGJ,OAAO;IAC1B;IACA,OAAOI,cAAc;EACvB,CAAC,EAAE,CAACJ,OAAO,CAAC,CAAC;EACb,IAAIK,WAAW,GAAGd,UAAU,CAACQ,GAAG,EAAEP,UAAU,CAACW,WAAW,CAAC,CAAC;EAC1D,OAAO,aAAaT,KAAK,CAACY,aAAa,CAACZ,KAAK,CAACa,QAAQ,EAAE,IAAI,EAAEN,KAAK,IAAI,aAAaP,KAAK,CAACY,aAAa,CAAC,KAAK,EAAE;IAC7GE,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,QAAQ;EAC1C,CAAC,CAAC,EAAE,aAAaR,KAAK,CAACgB,YAAY,CAACP,WAAW,EAAE;IAC/CJ,GAAG,EAAEN,UAAU,CAACU,WAAW,CAAC,GAAGE,WAAW,GAAGM;EAC/C,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,eAAed,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}