{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { warning } from \"rc-util/es/warning\";\nimport { useEffect, useRef, useState } from 'react';\nimport getFixScaleEleTransPosition from \"../getFixScaleEleTransPosition\";\nimport { BASE_SCALE_RATIO, WHEEL_MAX_SCALE_RATIO } from \"../previewConfig\";\nexport default function useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange) {\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isMoving = _useState2[0],\n    setMoving = _useState2[1];\n  var startPositionInfo = useRef({\n    diffX: 0,\n    diffY: 0,\n    transformX: 0,\n    transformY: 0\n  });\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (!movable || event.button !== 0) return;\n    event.preventDefault();\n    event.stopPropagation();\n    startPositionInfo.current = {\n      diffX: event.pageX - x,\n      diffY: event.pageY - y,\n      transformX: x,\n      transformY: y\n    };\n    setMoving(true);\n  };\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      updateTransform({\n        x: event.pageX - startPositionInfo.current.diffX,\n        y: event.pageY - startPositionInfo.current.diffY\n      }, 'move');\n    }\n  };\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      setMoving(false);\n\n      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */\n      var _startPositionInfo$cu = startPositionInfo.current,\n        transformX = _startPositionInfo$cu.transformX,\n        transformY = _startPositionInfo$cu.transformY;\n      var hasChangedPosition = x !== transformX && y !== transformY;\n      if (!hasChangedPosition) return;\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale;\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n        left = _imgRef$current$getBo.left,\n        top = _imgRef$current$getBo.top;\n      var isRotate = rotate % 180 !== 0;\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n      if (fixState) {\n        updateTransform(_objectSpread({}, fixState), 'dragRebound');\n      }\n    }\n  };\n  var onWheel = function onWheel(event) {\n    if (!visible || event.deltaY == 0) return;\n    // Scale ratio depends on the deltaY size\n    var scaleRatio = Math.abs(event.deltaY / 100);\n    // Limit the maximum scale ratio\n    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);\n    // Scale the ratio each time\n    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;\n    if (event.deltaY > 0) {\n      ratio = BASE_SCALE_RATIO / ratio;\n    }\n    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);\n  };\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener;\n    var onMouseMoveListener;\n    if (movable) {\n      onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n      onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n      try {\n        // Resolve if in iframe lost event\n        /* istanbul ignore next */\n        if (window.top !== window.self) {\n          onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n          onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n        }\n      } catch (error) {\n        /* istanbul ignore next */\n        warning(false, \"[rc-image] \".concat(error));\n      }\n    }\n    return function () {\n      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;\n      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 || _onMouseUpListener.remove();\n      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 || _onMouseMoveListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 || _onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 || _onTopMouseMoveListen.remove();\n    };\n  }, [visible, isMoving, x, y, rotate, movable]);\n  return {\n    isMoving: isMoving,\n    onMouseDown: onMouseDown,\n    onMouseMove: onMouseMove,\n    onMouseUp: onMouseUp,\n    onWheel: onWheel\n  };\n}", "map": {"version": 3, "names": ["_objectSpread", "_slicedToArray", "addEventListener", "warning", "useEffect", "useRef", "useState", "getFixScaleEleTransPosition", "BASE_SCALE_RATIO", "WHEEL_MAX_SCALE_RATIO", "useMouseEvent", "imgRef", "movable", "visible", "scaleStep", "transform", "updateTransform", "dispatchZoomChange", "rotate", "scale", "x", "y", "_useState", "_useState2", "isMoving", "setMoving", "startPositionInfo", "diffX", "diffY", "transformX", "transformY", "onMouseDown", "event", "button", "preventDefault", "stopPropagation", "current", "pageX", "pageY", "onMouseMove", "onMouseUp", "_startPositionInfo$cu", "hasChangedPosition", "width", "offsetWidth", "height", "offsetHeight", "_imgRef$current$getBo", "getBoundingClientRect", "left", "top", "isRotate", "fixState", "onWheel", "deltaY", "scaleRatio", "Math", "abs", "mergedScaleRatio", "min", "ratio", "clientX", "clientY", "onTopMouseUpListener", "onTopMouseMoveListener", "onMouseUpListener", "onMouseMoveListener", "window", "self", "error", "concat", "_onMouseUpListener", "_onMouseMoveListener", "_onTopMouseUpListener", "_onTopMouseMoveListen", "remove"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-image/es/hooks/useMouseEvent.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport addEventListener from \"rc-util/es/Dom/addEventListener\";\nimport { warning } from \"rc-util/es/warning\";\nimport { useEffect, useRef, useState } from 'react';\nimport getFixScaleEleTransPosition from \"../getFixScaleEleTransPosition\";\nimport { BASE_SCALE_RATIO, WHEEL_MAX_SCALE_RATIO } from \"../previewConfig\";\nexport default function useMouseEvent(imgRef, movable, visible, scaleStep, transform, updateTransform, dispatchZoomChange) {\n  var rotate = transform.rotate,\n    scale = transform.scale,\n    x = transform.x,\n    y = transform.y;\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    isMoving = _useState2[0],\n    setMoving = _useState2[1];\n  var startPositionInfo = useRef({\n    diffX: 0,\n    diffY: 0,\n    transformX: 0,\n    transformY: 0\n  });\n  var onMouseDown = function onMouseDown(event) {\n    // Only allow main button\n    if (!movable || event.button !== 0) return;\n    event.preventDefault();\n    event.stopPropagation();\n    startPositionInfo.current = {\n      diffX: event.pageX - x,\n      diffY: event.pageY - y,\n      transformX: x,\n      transformY: y\n    };\n    setMoving(true);\n  };\n  var onMouseMove = function onMouseMove(event) {\n    if (visible && isMoving) {\n      updateTransform({\n        x: event.pageX - startPositionInfo.current.diffX,\n        y: event.pageY - startPositionInfo.current.diffY\n      }, 'move');\n    }\n  };\n  var onMouseUp = function onMouseUp() {\n    if (visible && isMoving) {\n      setMoving(false);\n\n      /** No need to restore the position when the picture is not moved, So as not to interfere with the click */\n      var _startPositionInfo$cu = startPositionInfo.current,\n        transformX = _startPositionInfo$cu.transformX,\n        transformY = _startPositionInfo$cu.transformY;\n      var hasChangedPosition = x !== transformX && y !== transformY;\n      if (!hasChangedPosition) return;\n      var width = imgRef.current.offsetWidth * scale;\n      var height = imgRef.current.offsetHeight * scale;\n      // eslint-disable-next-line @typescript-eslint/no-shadow\n      var _imgRef$current$getBo = imgRef.current.getBoundingClientRect(),\n        left = _imgRef$current$getBo.left,\n        top = _imgRef$current$getBo.top;\n      var isRotate = rotate % 180 !== 0;\n      var fixState = getFixScaleEleTransPosition(isRotate ? height : width, isRotate ? width : height, left, top);\n      if (fixState) {\n        updateTransform(_objectSpread({}, fixState), 'dragRebound');\n      }\n    }\n  };\n  var onWheel = function onWheel(event) {\n    if (!visible || event.deltaY == 0) return;\n    // Scale ratio depends on the deltaY size\n    var scaleRatio = Math.abs(event.deltaY / 100);\n    // Limit the maximum scale ratio\n    var mergedScaleRatio = Math.min(scaleRatio, WHEEL_MAX_SCALE_RATIO);\n    // Scale the ratio each time\n    var ratio = BASE_SCALE_RATIO + mergedScaleRatio * scaleStep;\n    if (event.deltaY > 0) {\n      ratio = BASE_SCALE_RATIO / ratio;\n    }\n    dispatchZoomChange(ratio, 'wheel', event.clientX, event.clientY);\n  };\n  useEffect(function () {\n    var onTopMouseUpListener;\n    var onTopMouseMoveListener;\n    var onMouseUpListener;\n    var onMouseMoveListener;\n    if (movable) {\n      onMouseUpListener = addEventListener(window, 'mouseup', onMouseUp, false);\n      onMouseMoveListener = addEventListener(window, 'mousemove', onMouseMove, false);\n      try {\n        // Resolve if in iframe lost event\n        /* istanbul ignore next */\n        if (window.top !== window.self) {\n          onTopMouseUpListener = addEventListener(window.top, 'mouseup', onMouseUp, false);\n          onTopMouseMoveListener = addEventListener(window.top, 'mousemove', onMouseMove, false);\n        }\n      } catch (error) {\n        /* istanbul ignore next */\n        warning(false, \"[rc-image] \".concat(error));\n      }\n    }\n    return function () {\n      var _onMouseUpListener, _onMouseMoveListener, _onTopMouseUpListener, _onTopMouseMoveListen;\n      (_onMouseUpListener = onMouseUpListener) === null || _onMouseUpListener === void 0 || _onMouseUpListener.remove();\n      (_onMouseMoveListener = onMouseMoveListener) === null || _onMouseMoveListener === void 0 || _onMouseMoveListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseUpListener = onTopMouseUpListener) === null || _onTopMouseUpListener === void 0 || _onTopMouseUpListener.remove();\n      /* istanbul ignore next */\n      (_onTopMouseMoveListen = onTopMouseMoveListener) === null || _onTopMouseMoveListen === void 0 || _onTopMouseMoveListen.remove();\n    };\n  }, [visible, isMoving, x, y, rotate, movable]);\n  return {\n    isMoving: isMoving,\n    onMouseDown: onMouseDown,\n    onMouseMove: onMouseMove,\n    onMouseUp: onMouseUp,\n    onWheel: onWheel\n  };\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,gBAAgB,MAAM,iCAAiC;AAC9D,SAASC,OAAO,QAAQ,oBAAoB;AAC5C,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,2BAA2B,MAAM,gCAAgC;AACxE,SAASC,gBAAgB,EAAEC,qBAAqB,QAAQ,kBAAkB;AAC1E,eAAe,SAASC,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAEC,eAAe,EAAEC,kBAAkB,EAAE;EACzH,IAAIC,MAAM,GAAGH,SAAS,CAACG,MAAM;IAC3BC,KAAK,GAAGJ,SAAS,CAACI,KAAK;IACvBC,CAAC,GAAGL,SAAS,CAACK,CAAC;IACfC,CAAC,GAAGN,SAAS,CAACM,CAAC;EACjB,IAAIC,SAAS,GAAGhB,QAAQ,CAAC,KAAK,CAAC;IAC7BiB,UAAU,GAAGtB,cAAc,CAACqB,SAAS,EAAE,CAAC,CAAC;IACzCE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,SAAS,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC3B,IAAIG,iBAAiB,GAAGrB,MAAM,CAAC;IAC7BsB,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;IAC5C;IACA,IAAI,CAACpB,OAAO,IAAIoB,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;IACpCD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtBF,KAAK,CAACG,eAAe,CAAC,CAAC;IACvBT,iBAAiB,CAACU,OAAO,GAAG;MAC1BT,KAAK,EAAEK,KAAK,CAACK,KAAK,GAAGjB,CAAC;MACtBQ,KAAK,EAAEI,KAAK,CAACM,KAAK,GAAGjB,CAAC;MACtBQ,UAAU,EAAET,CAAC;MACbU,UAAU,EAAET;IACd,CAAC;IACDI,SAAS,CAAC,IAAI,CAAC;EACjB,CAAC;EACD,IAAIc,WAAW,GAAG,SAASA,WAAWA,CAACP,KAAK,EAAE;IAC5C,IAAInB,OAAO,IAAIW,QAAQ,EAAE;MACvBR,eAAe,CAAC;QACdI,CAAC,EAAEY,KAAK,CAACK,KAAK,GAAGX,iBAAiB,CAACU,OAAO,CAACT,KAAK;QAChDN,CAAC,EAAEW,KAAK,CAACM,KAAK,GAAGZ,iBAAiB,CAACU,OAAO,CAACR;MAC7C,CAAC,EAAE,MAAM,CAAC;IACZ;EACF,CAAC;EACD,IAAIY,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAI3B,OAAO,IAAIW,QAAQ,EAAE;MACvBC,SAAS,CAAC,KAAK,CAAC;;MAEhB;MACA,IAAIgB,qBAAqB,GAAGf,iBAAiB,CAACU,OAAO;QACnDP,UAAU,GAAGY,qBAAqB,CAACZ,UAAU;QAC7CC,UAAU,GAAGW,qBAAqB,CAACX,UAAU;MAC/C,IAAIY,kBAAkB,GAAGtB,CAAC,KAAKS,UAAU,IAAIR,CAAC,KAAKS,UAAU;MAC7D,IAAI,CAACY,kBAAkB,EAAE;MACzB,IAAIC,KAAK,GAAGhC,MAAM,CAACyB,OAAO,CAACQ,WAAW,GAAGzB,KAAK;MAC9C,IAAI0B,MAAM,GAAGlC,MAAM,CAACyB,OAAO,CAACU,YAAY,GAAG3B,KAAK;MAChD;MACA,IAAI4B,qBAAqB,GAAGpC,MAAM,CAACyB,OAAO,CAACY,qBAAqB,CAAC,CAAC;QAChEC,IAAI,GAAGF,qBAAqB,CAACE,IAAI;QACjCC,GAAG,GAAGH,qBAAqB,CAACG,GAAG;MACjC,IAAIC,QAAQ,GAAGjC,MAAM,GAAG,GAAG,KAAK,CAAC;MACjC,IAAIkC,QAAQ,GAAG7C,2BAA2B,CAAC4C,QAAQ,GAAGN,MAAM,GAAGF,KAAK,EAAEQ,QAAQ,GAAGR,KAAK,GAAGE,MAAM,EAAEI,IAAI,EAAEC,GAAG,CAAC;MAC3G,IAAIE,QAAQ,EAAE;QACZpC,eAAe,CAAChB,aAAa,CAAC,CAAC,CAAC,EAAEoD,QAAQ,CAAC,EAAE,aAAa,CAAC;MAC7D;IACF;EACF,CAAC;EACD,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACrB,KAAK,EAAE;IACpC,IAAI,CAACnB,OAAO,IAAImB,KAAK,CAACsB,MAAM,IAAI,CAAC,EAAE;IACnC;IACA,IAAIC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACzB,KAAK,CAACsB,MAAM,GAAG,GAAG,CAAC;IAC7C;IACA,IAAII,gBAAgB,GAAGF,IAAI,CAACG,GAAG,CAACJ,UAAU,EAAE9C,qBAAqB,CAAC;IAClE;IACA,IAAImD,KAAK,GAAGpD,gBAAgB,GAAGkD,gBAAgB,GAAG5C,SAAS;IAC3D,IAAIkB,KAAK,CAACsB,MAAM,GAAG,CAAC,EAAE;MACpBM,KAAK,GAAGpD,gBAAgB,GAAGoD,KAAK;IAClC;IACA3C,kBAAkB,CAAC2C,KAAK,EAAE,OAAO,EAAE5B,KAAK,CAAC6B,OAAO,EAAE7B,KAAK,CAAC8B,OAAO,CAAC;EAClE,CAAC;EACD1D,SAAS,CAAC,YAAY;IACpB,IAAI2D,oBAAoB;IACxB,IAAIC,sBAAsB;IAC1B,IAAIC,iBAAiB;IACrB,IAAIC,mBAAmB;IACvB,IAAItD,OAAO,EAAE;MACXqD,iBAAiB,GAAG/D,gBAAgB,CAACiE,MAAM,EAAE,SAAS,EAAE3B,SAAS,EAAE,KAAK,CAAC;MACzE0B,mBAAmB,GAAGhE,gBAAgB,CAACiE,MAAM,EAAE,WAAW,EAAE5B,WAAW,EAAE,KAAK,CAAC;MAC/E,IAAI;QACF;QACA;QACA,IAAI4B,MAAM,CAACjB,GAAG,KAAKiB,MAAM,CAACC,IAAI,EAAE;UAC9BL,oBAAoB,GAAG7D,gBAAgB,CAACiE,MAAM,CAACjB,GAAG,EAAE,SAAS,EAAEV,SAAS,EAAE,KAAK,CAAC;UAChFwB,sBAAsB,GAAG9D,gBAAgB,CAACiE,MAAM,CAACjB,GAAG,EAAE,WAAW,EAAEX,WAAW,EAAE,KAAK,CAAC;QACxF;MACF,CAAC,CAAC,OAAO8B,KAAK,EAAE;QACd;QACAlE,OAAO,CAAC,KAAK,EAAE,aAAa,CAACmE,MAAM,CAACD,KAAK,CAAC,CAAC;MAC7C;IACF;IACA,OAAO,YAAY;MACjB,IAAIE,kBAAkB,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,qBAAqB;MAC1F,CAACH,kBAAkB,GAAGN,iBAAiB,MAAM,IAAI,IAAIM,kBAAkB,KAAK,KAAK,CAAC,IAAIA,kBAAkB,CAACI,MAAM,CAAC,CAAC;MACjH,CAACH,oBAAoB,GAAGN,mBAAmB,MAAM,IAAI,IAAIM,oBAAoB,KAAK,KAAK,CAAC,IAAIA,oBAAoB,CAACG,MAAM,CAAC,CAAC;MACzH;MACA,CAACF,qBAAqB,GAAGV,oBAAoB,MAAM,IAAI,IAAIU,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACE,MAAM,CAAC,CAAC;MAC7H;MACA,CAACD,qBAAqB,GAAGV,sBAAsB,MAAM,IAAI,IAAIU,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACC,MAAM,CAAC,CAAC;IACjI,CAAC;EACH,CAAC,EAAE,CAAC9D,OAAO,EAAEW,QAAQ,EAAEJ,CAAC,EAAEC,CAAC,EAAEH,MAAM,EAAEN,OAAO,CAAC,CAAC;EAC9C,OAAO;IACLY,QAAQ,EAAEA,QAAQ;IAClBO,WAAW,EAAEA,WAAW;IACxBQ,WAAW,EAAEA,WAAW;IACxBC,SAAS,EAAEA,SAAS;IACpBa,OAAO,EAAEA;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}