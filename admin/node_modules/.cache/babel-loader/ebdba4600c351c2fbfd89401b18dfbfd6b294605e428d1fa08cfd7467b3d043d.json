{"ast": null, "code": "import React, { useMemo } from 'react';\nimport { Color } from \"../color\";\nimport { generateColor } from \"../util\";\nvar Gradient = function Gradient(_ref) {\n  var colors = _ref.colors,\n    children = _ref.children,\n    _ref$direction = _ref.direction,\n    direction = _ref$direction === void 0 ? 'to right' : _ref$direction,\n    type = _ref.type,\n    prefixCls = _ref.prefixCls;\n  var gradientColors = useMemo(function () {\n    return colors.map(function (color, idx) {\n      var result = generateColor(color);\n      if (type === 'alpha' && idx === colors.length - 1) {\n        result = new Color(result.setA(1));\n      }\n      return result.toRgbString();\n    }).join(',');\n  }, [colors, type]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-gradient\"),\n    style: {\n      position: 'absolute',\n      inset: 0,\n      background: \"linear-gradient(\".concat(direction, \", \").concat(gradientColors, \")\")\n    }\n  }, children);\n};\nexport default Gradient;", "map": {"version": 3, "names": ["React", "useMemo", "Color", "generateColor", "Gradient", "_ref", "colors", "children", "_ref$direction", "direction", "type", "prefixCls", "gradientColors", "map", "color", "idx", "result", "length", "setA", "toRgbString", "join", "createElement", "className", "concat", "style", "position", "inset", "background"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@rc-component/color-picker/es/components/Gradient.js"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport { Color } from \"../color\";\nimport { generateColor } from \"../util\";\nvar Gradient = function Gradient(_ref) {\n  var colors = _ref.colors,\n    children = _ref.children,\n    _ref$direction = _ref.direction,\n    direction = _ref$direction === void 0 ? 'to right' : _ref$direction,\n    type = _ref.type,\n    prefixCls = _ref.prefixCls;\n  var gradientColors = useMemo(function () {\n    return colors.map(function (color, idx) {\n      var result = generateColor(color);\n      if (type === 'alpha' && idx === colors.length - 1) {\n        result = new Color(result.setA(1));\n      }\n      return result.toRgbString();\n    }).join(',');\n  }, [colors, type]);\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: \"\".concat(prefixCls, \"-gradient\"),\n    style: {\n      position: 'absolute',\n      inset: 0,\n      background: \"linear-gradient(\".concat(direction, \", \").concat(gradientColors, \")\")\n    }\n  }, children);\n};\nexport default Gradient;"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,OAAO,QAAQ,OAAO;AACtC,SAASC,KAAK,QAAQ,UAAU;AAChC,SAASC,aAAa,QAAQ,SAAS;AACvC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;EACrC,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;IACtBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,cAAc,GAAGH,IAAI,CAACI,SAAS;IAC/BA,SAAS,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,cAAc;IACnEE,IAAI,GAAGL,IAAI,CAACK,IAAI;IAChBC,SAAS,GAAGN,IAAI,CAACM,SAAS;EAC5B,IAAIC,cAAc,GAAGX,OAAO,CAAC,YAAY;IACvC,OAAOK,MAAM,CAACO,GAAG,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;MACtC,IAAIC,MAAM,GAAGb,aAAa,CAACW,KAAK,CAAC;MACjC,IAAIJ,IAAI,KAAK,OAAO,IAAIK,GAAG,KAAKT,MAAM,CAACW,MAAM,GAAG,CAAC,EAAE;QACjDD,MAAM,GAAG,IAAId,KAAK,CAACc,MAAM,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;MACpC;MACA,OAAOF,MAAM,CAACG,WAAW,CAAC,CAAC;IAC7B,CAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;EACd,CAAC,EAAE,CAACd,MAAM,EAAEI,IAAI,CAAC,CAAC;EAClB,OAAO,aAAaV,KAAK,CAACqB,aAAa,CAAC,KAAK,EAAE;IAC7CC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACZ,SAAS,EAAE,WAAW,CAAC;IAC5Ca,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,CAAC;MACRC,UAAU,EAAE,kBAAkB,CAACJ,MAAM,CAACd,SAAS,EAAE,IAAI,CAAC,CAACc,MAAM,CAACX,cAAc,EAAE,GAAG;IACnF;EACF,CAAC,EAAEL,QAAQ,CAAC;AACd,CAAC;AACD,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}