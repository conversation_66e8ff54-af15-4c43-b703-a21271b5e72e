{"ast": null, "code": "import devWarning from \"rc-util/es/warning\";\nexport function lintWarning(message, info) {\n  var path = info.path,\n    parentSelectors = info.parentSelectors;\n  devWarning(false, \"[Ant Design CSS-in-JS] \".concat(path ? \"Error in \".concat(path, \": \") : '').concat(message).concat(parentSelectors.length ? \" Selector: \".concat(parentSelectors.join(' | ')) : ''));\n}", "map": {"version": 3, "names": ["dev<PERSON><PERSON><PERSON>", "lintWarning", "message", "info", "path", "parentSelectors", "concat", "length", "join"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@ant-design/cssinjs/es/linters/utils.js"], "sourcesContent": ["import devWarning from \"rc-util/es/warning\";\nexport function lintWarning(message, info) {\n  var path = info.path,\n    parentSelectors = info.parentSelectors;\n  devWarning(false, \"[Ant Design CSS-in-JS] \".concat(path ? \"Error in \".concat(path, \": \") : '').concat(message).concat(parentSelectors.length ? \" Selector: \".concat(parentSelectors.join(' | ')) : ''));\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,oBAAoB;AAC3C,OAAO,SAASC,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACzC,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,eAAe,GAAGF,IAAI,CAACE,eAAe;EACxCL,UAAU,CAAC,KAAK,EAAE,yBAAyB,CAACM,MAAM,CAACF,IAAI,GAAG,WAAW,CAACE,MAAM,CAACF,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,CAACE,MAAM,CAACJ,OAAO,CAAC,CAACI,MAAM,CAACD,eAAe,CAACE,MAAM,GAAG,aAAa,CAACD,MAAM,CAACD,eAAe,CAACG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACzM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}