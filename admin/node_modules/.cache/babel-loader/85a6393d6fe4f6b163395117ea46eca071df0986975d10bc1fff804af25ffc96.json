{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Row, Col, Card, Statistic, Spin, message } from \"antd\";\nimport { QrcodeOutlined, CheckCircleOutlined, StopOutlined, ClockCircleOutlined, ScanOutlined } from \"@ant-design/icons\";\nimport { qrCodeApi } from \"../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchStats();\n  }, []);\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      const data = await qrCodeApi.getStats();\n      setStats(data);\n    } catch (error) {\n      message.error(\"获取统计信息失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        padding: \"50px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\u4EEA\\u8868\\u76D8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4E8C\\u7EF4\\u7801\\u6570\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.total) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#1890ff\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D3B\\u8DC3\\u4E8C\\u7EF4\\u7801\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.active) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#52c41a\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u505C\\u7528\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.inactive) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#faad14\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u8FC7\\u671F\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.expired) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#ff4d4f\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u626B\\u63CF\\u6B21\\u6570\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalScans) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#722ed1\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"zr3d2rzWUuuAzE7RwCrYeGz8vE0=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Row", "Col", "Card", "Statistic", "Spin", "message", "QrcodeOutlined", "CheckCircleOutlined", "StopOutlined", "ClockCircleOutlined", "ScanOutlined", "qrCodeApi", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "stats", "setStats", "loading", "setLoading", "fetchStats", "data", "getStats", "error", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "sm", "md", "lg", "className", "title", "value", "total", "prefix", "valueStyle", "color", "active", "inactive", "expired", "marginTop", "totalScans", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { Row, Col, Card, Statistic, Spin, message } from \"antd\";\nimport {\n  QrcodeOutlined,\n  CheckCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  ScanOutlined,\n  ShoppingOutlined,\n  TagsOutlined,\n} from \"@ant-design/icons\";\nimport {\n  qrCodeApi,\n  productApi,\n  StatsResponse,\n  ProductStatsResponse,\n} from \"../services/api\";\n\nconst Dashboard: React.FC = () => {\n  const [stats, setStats] = useState<StatsResponse | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchStats();\n  }, []);\n\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      const data = await qrCodeApi.getStats();\n      setStats(data);\n    } catch (error) {\n      message.error(\"获取统计信息失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: \"center\", padding: \"50px\" }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <h1>仪表盘</h1>\n      <Row gutter={[16, 16]}>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"总二维码数\"\n              value={stats?.total || 0}\n              prefix={<QrcodeOutlined />}\n              valueStyle={{ color: \"#1890ff\" }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"活跃二维码\"\n              value={stats?.active || 0}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: \"#52c41a\" }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"已停用\"\n              value={stats?.inactive || 0}\n              prefix={<StopOutlined />}\n              valueStyle={{ color: \"#faad14\" }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"已过期\"\n              value={stats?.expired || 0}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: \"#ff4d4f\" }}\n            />\n          </Card>\n        </Col>\n      </Row>\n      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>\n        <Col xs={24} sm={12}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"总扫描次数\"\n              value={stats?.totalScans || 0}\n              prefix={<ScanOutlined />}\n              valueStyle={{ color: \"#722ed1\" }}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AAC/D,SACEC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,QAGP,mBAAmB;AAC1B,SACEC,SAAS,QAIJ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAuB,IAAI,CAAC;EAC9D,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdsB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,IAAI,GAAG,MAAMV,SAAS,CAACW,QAAQ,CAAC,CAAC;MACvCL,QAAQ,CAACI,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdlB,OAAO,CAACkB,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKW,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,eACnDd,OAAA,CAACT,IAAI;QAACwB,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACEnB,OAAA;IAAAc,QAAA,gBACEd,OAAA;MAAAc,QAAA,EAAI;IAAG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACZnB,OAAA,CAACb,GAAG;MAACiC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAN,QAAA,gBACpBd,OAAA,CAACZ,GAAG;QAACiC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChCd,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1Bd,OAAA,CAACV,SAAS;YACRoC,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,KAAK,KAAI,CAAE;YACzBC,MAAM,eAAE7B,OAAA,CAACP,cAAc;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnB,OAAA,CAACZ,GAAG;QAACiC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChCd,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1Bd,OAAA,CAACV,SAAS;YACRoC,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE6B,MAAM,KAAI,CAAE;YAC1BH,MAAM,eAAE7B,OAAA,CAACN,mBAAmB;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnB,OAAA,CAACZ,GAAG;QAACiC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChCd,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1Bd,OAAA,CAACV,SAAS;YACRoC,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE,CAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8B,QAAQ,KAAI,CAAE;YAC5BJ,MAAM,eAAE7B,OAAA,CAACL,YAAY;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNnB,OAAA,CAACZ,GAAG;QAACiC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChCd,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1Bd,OAAA,CAACV,SAAS;YACRoC,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE,CAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+B,OAAO,KAAI,CAAE;YAC3BL,MAAM,eAAE7B,OAAA,CAACJ,mBAAmB;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNnB,OAAA,CAACb,GAAG;MAACiC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACT,KAAK,EAAE;QAAEwB,SAAS,EAAE;MAAG,CAAE;MAAArB,QAAA,eAC9Cd,OAAA,CAACZ,GAAG;QAACiC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAR,QAAA,eAClBd,OAAA,CAACX,IAAI;UAACoC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1Bd,OAAA,CAACV,SAAS;YACRoC,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAAxB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiC,UAAU,KAAI,CAAE;YAC9BP,MAAM,eAAE7B,OAAA,CAACH,YAAY;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CAvFID,SAAmB;AAAAoC,EAAA,GAAnBpC,SAAmB;AAyFzB,eAAeA,SAAS;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}