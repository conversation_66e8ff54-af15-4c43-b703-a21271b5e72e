{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/components/Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Layout, Menu } from \"antd\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { DashboardOutlined, ShoppingOutlined, AppstoreAddOutlined } from \"@ant-design/icons\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    logout,\n    user\n  } = useAuth();\n  const menuItems = [{\n    key: \"/\",\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this),\n    label: \"仪表盘\"\n  }, {\n    key: \"/qr-codes\",\n    icon: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    label: \"二维码管理\"\n  }, {\n    key: \"/qr-codes/create\",\n    icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this),\n    label: \"创建二维码\"\n  }, {\n    key: \"/products\",\n    icon: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    label: \"商品管理\"\n  }, {\n    key: \"/products/create\",\n    icon: /*#__PURE__*/_jsxDEV(AppstoreAddOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 13\n    }, this),\n    label: \"创建商品\"\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  return /*#__PURE__*/_jsxDEV(Sider, {\n    width: 200,\n    theme: \"dark\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: 64,\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        color: \"white\",\n        fontSize: \"18px\",\n        fontWeight: \"bold\"\n      },\n      children: \"\\u4E8C\\u7EF4\\u7801\\u7BA1\\u7406\\u5E73\\u53F0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      mode: \"inline\",\n      selectedKeys: [location.pathname],\n      items: menuItems,\n      onClick: handleMenuClick,\n      theme: \"dark\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"ne7uFX/HClqKJDmhTyddXd59eRE=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Layout", "<PERSON><PERSON>", "useNavigate", "useLocation", "useAuth", "DashboardOutlined", "ShoppingOutlined", "AppstoreAddOutlined", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "Sidebar", "_s", "navigate", "location", "logout", "user", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "QrcodeOutlined", "PlusOutlined", "handleMenuClick", "width", "theme", "children", "style", "height", "display", "alignItems", "justifyContent", "color", "fontSize", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/components/Sidebar.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Layout, Menu, Modal } from \"antd\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport {\n  DashboardOutlined,\n  ShoppingOutlined,\n  AppstoreAddOutlined,\n  LockOutlined,\n  LogoutOutlined,\n  UserOutlined,\n} from \"@ant-design/icons\";\n\nconst { Sider } = Layout;\n\nconst Sidebar: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { logout, user } = useAuth();\n\n  const menuItems = [\n    {\n      key: \"/\",\n      icon: <DashboardOutlined />,\n      label: \"仪表盘\",\n    },\n    {\n      key: \"/qr-codes\",\n      icon: <QrcodeOutlined />,\n      label: \"二维码管理\",\n    },\n    {\n      key: \"/qr-codes/create\",\n      icon: <PlusOutlined />,\n      label: \"创建二维码\",\n    },\n    {\n      key: \"/products\",\n      icon: <ShoppingOutlined />,\n      label: \"商品管理\",\n    },\n    {\n      key: \"/products/create\",\n      icon: <AppstoreAddOutlined />,\n      label: \"创建商品\",\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  return (\n    <Sider width={200} theme=\"dark\">\n      <div\n        style={{\n          height: 64,\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          color: \"white\",\n          fontSize: \"18px\",\n          fontWeight: \"bold\",\n        }}\n      >\n        二维码管理平台\n      </div>\n      <Menu\n        mode=\"inline\"\n        selectedKeys={[location.pathname]}\n        items={menuItems}\n        onClick={handleMenuClick}\n        theme=\"dark\"\n      />\n    </Sider>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,IAAI,QAAe,MAAM;AAC1C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,iBAAiB,EACjBC,gBAAgB,EAChBC,mBAAmB,QAId,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC;AAAM,CAAC,GAAGV,MAAM;AAExB,MAAMW,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY,MAAM;IAAEC;EAAK,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAElC,MAAMa,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAEV,OAAA,CAACJ,iBAAiB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEV,OAAA,CAACgB,cAAc;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAEV,OAAA,CAACiB,YAAY;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEV,OAAA,CAACH,gBAAgB;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAEV,OAAA,CAACF,mBAAmB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMG,eAAe,GAAGA,CAAC;IAAET;EAAqB,CAAC,KAAK;IACpDL,QAAQ,CAACK,GAAG,CAAC;EACf,CAAC;EAED,oBACET,OAAA,CAACC,KAAK;IAACkB,KAAK,EAAE,GAAI;IAACC,KAAK,EAAC,MAAM;IAAAC,QAAA,gBAC7BrB,OAAA;MACEsB,KAAK,EAAE;QACLC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,EACH;IAED;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNd,OAAA,CAACR,IAAI;MACHsC,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAE,CAAC1B,QAAQ,CAAC2B,QAAQ,CAAE;MAClCC,KAAK,EAAEzB,SAAU;MACjB0B,OAAO,EAAEhB,eAAgB;MACzBE,KAAK,EAAC;IAAM;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEZ,CAAC;AAACX,EAAA,CA7DID,OAAiB;EAAA,QACJT,WAAW,EACXC,WAAW,EACHC,OAAO;AAAA;AAAAwC,EAAA,GAH5BjC,OAAiB;AA+DvB,eAAeA,OAAO;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}