{"ast": null, "code": "import * as React from 'react';\nvar PopupContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (_, next) {\n  return next.cache;\n});\nif (process.env.NODE_ENV !== 'production') {\n  PopupContent.displayName = 'PopupContent';\n}\nexport default PopupContent;", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memo", "_ref", "children", "_", "next", "cache", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@rc-component/trigger/es/Popup/PopupContent.js"], "sourcesContent": ["import * as React from 'react';\nvar PopupContent = /*#__PURE__*/React.memo(function (_ref) {\n  var children = _ref.children;\n  return children;\n}, function (_, next) {\n  return next.cache;\n});\nif (process.env.NODE_ENV !== 'production') {\n  PopupContent.displayName = 'PopupContent';\n}\nexport default PopupContent;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,YAAY,GAAG,aAAaD,KAAK,CAACE,IAAI,CAAC,UAAUC,IAAI,EAAE;EACzD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAOA,QAAQ;AACjB,CAAC,EAAE,UAAUC,CAAC,EAAEC,IAAI,EAAE;EACpB,OAAOA,IAAI,CAACC,KAAK;AACnB,CAAC,CAAC;AACF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCT,YAAY,CAACU,WAAW,GAAG,cAAc;AAC3C;AACA,eAAeV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}