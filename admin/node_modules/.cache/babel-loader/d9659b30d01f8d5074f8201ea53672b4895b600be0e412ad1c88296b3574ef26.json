{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/pages/Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst Login = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const {\n    login,\n    isAuthenticated\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 如果已经登录，重定向到主页\n  useEffect(() => {\n    if (isAuthenticated) {\n      var _location$state, _location$state$from;\n      const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/';\n      navigate(from, {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, navigate, location]);\n  const handleSubmit = async values => {\n    setLoading(true);\n    const success = await login(values);\n    if (success) {\n      var _location$state2, _location$state2$from;\n      const from = ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : (_location$state2$from = _location$state2.from) === null || _location$state2$from === void 0 ? void 0 : _location$state2$from.pathname) || '/';\n      navigate(from, {\n        replace: true\n      });\n    }\n    setLoading(false);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      padding: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        width: '100%',\n        maxWidth: 400,\n        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: 32\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 2,\n          style: {\n            marginBottom: 8\n          },\n          children: \"\\u5546\\u54C1\\u7BA1\\u7406\\u5E73\\u53F0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"\\u8BF7\\u767B\\u5F55\\u60A8\\u7684\\u8D26\\u6237\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u9ED8\\u8BA4\\u8D26\\u6237\\u4FE1\\u606F\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u7528\\u6237\\u540D: admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u5BC6\\u7801: admin\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 13\n        }, this),\n        type: \"info\",\n        showIcon: true,\n        style: {\n          marginBottom: 24\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        name: \"login\",\n        onFinish: handleSubmit,\n        autoComplete: \"off\",\n        size: \"large\",\n        initialValues: {\n          username: 'admin',\n          password: 'admin'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          rules: [{\n            required: true,\n            message: '请输入用户名'\n          }, {\n            min: 3,\n            message: '用户名至少3位'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\",\n            autoComplete: \"username\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          rules: [{\n            required: true,\n            message: '请输入密码'\n          }, {\n            min: 4,\n            message: '密码至少4位'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\",\n            autoComplete: \"current-password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            block: true,\n            style: {\n              height: 48\n            },\n            children: \"\\u767B\\u5F55\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 110,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginTop: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          style: {\n            fontSize: 12\n          },\n          children: \"\\xA9 2024 \\u5546\\u54C1\\u7BA1\\u7406\\u5E73\\u53F0. All rights reserved.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 123,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 36,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"E61rET3+jKhLVM1WwQD003BT7XU=\", false, function () {\n  return [Form.useForm, useAuth, useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Form", "Input", "<PERSON><PERSON>", "Card", "Typography", "<PERSON><PERSON>", "UserOutlined", "LockOutlined", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Title", "Text", "<PERSON><PERSON>", "_s", "form", "useForm", "loading", "setLoading", "login", "isAuthenticated", "navigate", "location", "_location$state", "_location$state$from", "from", "state", "pathname", "replace", "handleSubmit", "values", "success", "_location$state2", "_location$state2$from", "style", "minHeight", "display", "alignItems", "justifyContent", "background", "padding", "children", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "textAlign", "marginBottom", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "message", "description", "showIcon", "name", "onFinish", "autoComplete", "size", "initialValues", "username", "password", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "htmlType", "block", "height", "marginTop", "fontSize", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Form, Input, Button, Card, Typography, Space, Alert } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { LoginData } from '../services/api';\n\nconst { Title, Text } = Typography;\n\nconst Login: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const { login, isAuthenticated } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 如果已经登录，重定向到主页\n  useEffect(() => {\n    if (isAuthenticated) {\n      const from = (location.state as any)?.from?.pathname || '/';\n      navigate(from, { replace: true });\n    }\n  }, [isAuthenticated, navigate, location]);\n\n  const handleSubmit = async (values: LoginData) => {\n    setLoading(true);\n    const success = await login(values);\n    if (success) {\n      const from = (location.state as any)?.from?.pathname || '/';\n      navigate(from, { replace: true });\n    }\n    setLoading(false);\n  };\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n      padding: '20px'\n    }}>\n      <Card\n        style={{\n          width: '100%',\n          maxWidth: 400,\n          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',\n        }}\n      >\n        <div style={{ textAlign: 'center', marginBottom: 32 }}>\n          <Title level={2} style={{ marginBottom: 8 }}>\n            商品管理平台\n          </Title>\n          <Text type=\"secondary\">请登录您的账户</Text>\n        </div>\n\n        <Alert\n          message=\"默认账户信息\"\n          description={\n            <div>\n              <div>用户名: admin</div>\n              <div>密码: admin</div>\n            </div>\n          }\n          type=\"info\"\n          showIcon\n          style={{ marginBottom: 24 }}\n        />\n\n        <Form\n          form={form}\n          name=\"login\"\n          onFinish={handleSubmit}\n          autoComplete=\"off\"\n          size=\"large\"\n          initialValues={{\n            username: 'admin',\n            password: 'admin',\n          }}\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[\n              { required: true, message: '请输入用户名' },\n              { min: 3, message: '用户名至少3位' },\n            ]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"用户名\"\n              autoComplete=\"username\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[\n              { required: true, message: '请输入密码' },\n              { min: 4, message: '密码至少4位' },\n            ]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"密码\"\n              autoComplete=\"current-password\"\n            />\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0 }}>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              block\n              style={{ height: 48 }}\n            >\n              登录\n            </Button>\n          </Form.Item>\n        </Form>\n\n        <div style={{ textAlign: 'center', marginTop: 24 }}>\n          <Text type=\"secondary\" style={{ fontSize: 12 }}>\n            © 2024 商品管理平台. All rights reserved.\n          </Text>\n        </div>\n      </Card>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAASC,KAAK,QAAQ,MAAM;AAC1E,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlD,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGV,UAAU;AAElC,MAAMW,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,IAAI,CAAC,GAAGjB,IAAI,CAACkB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEuB,KAAK;IAAEC;EAAgB,CAAC,GAAGZ,OAAO,CAAC,CAAC;EAC5C,MAAMa,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAMgB,QAAQ,GAAGf,WAAW,CAAC,CAAC;;EAE9B;EACAV,SAAS,CAAC,MAAM;IACd,IAAIuB,eAAe,EAAE;MAAA,IAAAG,eAAA,EAAAC,oBAAA;MACnB,MAAMC,IAAI,GAAG,EAAAF,eAAA,GAACD,QAAQ,CAACI,KAAK,cAAAH,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBE,IAAI,cAAAD,oBAAA,uBAA7BA,oBAAA,CAA+BG,QAAQ,KAAI,GAAG;MAC3DN,QAAQ,CAACI,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;EACF,CAAC,EAAE,CAACR,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,CAAC,CAAC;EAEzC,MAAMO,YAAY,GAAG,MAAOC,MAAiB,IAAK;IAChDZ,UAAU,CAAC,IAAI,CAAC;IAChB,MAAMa,OAAO,GAAG,MAAMZ,KAAK,CAACW,MAAM,CAAC;IACnC,IAAIC,OAAO,EAAE;MAAA,IAAAC,gBAAA,EAAAC,qBAAA;MACX,MAAMR,IAAI,GAAG,EAAAO,gBAAA,GAACV,QAAQ,CAACI,KAAK,cAAAM,gBAAA,wBAAAC,qBAAA,GAAfD,gBAAA,CAAyBP,IAAI,cAAAQ,qBAAA,uBAA7BA,qBAAA,CAA+BN,QAAQ,KAAI,GAAG;MAC3DN,QAAQ,CAACI,IAAI,EAAE;QAAEG,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC;IACAV,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC;EAED,oBACER,OAAA;IAAKwB,KAAK,EAAE;MACVC,SAAS,EAAE,OAAO;MAClBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,mDAAmD;MAC/DC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eACA/B,OAAA,CAACT,IAAI;MACHiC,KAAK,EAAE;QACLQ,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,GAAG;QACbC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,gBAEF/B,OAAA;QAAKwB,KAAK,EAAE;UAAEW,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAL,QAAA,gBACpD/B,OAAA,CAACC,KAAK;UAACoC,KAAK,EAAE,CAAE;UAACb,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAE,CAAE;UAAAL,QAAA,EAAC;QAE7C;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACRzC,OAAA,CAACE,IAAI;UAACwC,IAAI,EAAC,WAAW;UAAAX,QAAA,EAAC;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClC,CAAC,eAENzC,OAAA,CAACP,KAAK;QACJkD,OAAO,EAAC,sCAAQ;QAChBC,WAAW,eACT5C,OAAA;UAAA+B,QAAA,gBACE/B,OAAA;YAAA+B,QAAA,EAAK;UAAU;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBzC,OAAA;YAAA+B,QAAA,EAAK;UAAS;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CACN;QACDC,IAAI,EAAC,MAAM;QACXG,QAAQ;QACRrB,KAAK,EAAE;UAAEY,YAAY,EAAE;QAAG;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC,eAEFzC,OAAA,CAACZ,IAAI;QACHiB,IAAI,EAAEA,IAAK;QACXyC,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAE5B,YAAa;QACvB6B,YAAY,EAAC,KAAK;QAClBC,IAAI,EAAC,OAAO;QACZC,aAAa,EAAE;UACbC,QAAQ,EAAE,OAAO;UACjBC,QAAQ,EAAE;QACZ,CAAE;QAAArB,QAAA,gBAEF/B,OAAA,CAACZ,IAAI,CAACiE,IAAI;UACRP,IAAI,EAAC,UAAU;UACfQ,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEZ,OAAO,EAAE;UAAS,CAAC,EACrC;YAAEa,GAAG,EAAE,CAAC;YAAEb,OAAO,EAAE;UAAU,CAAC,CAC9B;UAAAZ,QAAA,eAEF/B,OAAA,CAACX,KAAK;YACJoE,MAAM,eAAEzD,OAAA,CAACN,YAAY;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBiB,WAAW,EAAC,oBAAK;YACjBV,YAAY,EAAC;UAAU;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZzC,OAAA,CAACZ,IAAI,CAACiE,IAAI;UACRP,IAAI,EAAC,UAAU;UACfQ,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEZ,OAAO,EAAE;UAAQ,CAAC,EACpC;YAAEa,GAAG,EAAE,CAAC;YAAEb,OAAO,EAAE;UAAS,CAAC,CAC7B;UAAAZ,QAAA,eAEF/B,OAAA,CAACX,KAAK,CAACsE,QAAQ;YACbF,MAAM,eAAEzD,OAAA,CAACL,YAAY;cAAA2C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBiB,WAAW,EAAC,cAAI;YAChBV,YAAY,EAAC;UAAkB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZzC,OAAA,CAACZ,IAAI,CAACiE,IAAI;UAAC7B,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAE,CAAE;UAAAL,QAAA,eACpC/B,OAAA,CAACV,MAAM;YACLoD,IAAI,EAAC,SAAS;YACdkB,QAAQ,EAAC,QAAQ;YACjBrD,OAAO,EAAEA,OAAQ;YACjBsD,KAAK;YACLrC,KAAK,EAAE;cAAEsC,MAAM,EAAE;YAAG,CAAE;YAAA/B,QAAA,EACvB;UAED;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAEPzC,OAAA;QAAKwB,KAAK,EAAE;UAAEW,SAAS,EAAE,QAAQ;UAAE4B,SAAS,EAAE;QAAG,CAAE;QAAAhC,QAAA,eACjD/B,OAAA,CAACE,IAAI;UAACwC,IAAI,EAAC,WAAW;UAAClB,KAAK,EAAE;YAAEwC,QAAQ,EAAE;UAAG,CAAE;UAAAjC,QAAA,EAAC;QAEhD;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrC,EAAA,CAzHID,KAAe;EAAA,QACJf,IAAI,CAACkB,OAAO,EAEQR,OAAO,EACzBF,WAAW,EACXC,WAAW;AAAA;AAAAoE,EAAA,GALxB9D,KAAe;AA2HrB,eAAeA,KAAK;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}