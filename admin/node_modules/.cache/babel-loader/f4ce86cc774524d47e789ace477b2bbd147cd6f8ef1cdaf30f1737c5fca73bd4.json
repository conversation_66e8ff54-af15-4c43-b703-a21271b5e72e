{"ast": null, "code": "const initMotionCommon = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\n// FIXME: origin less code seems same as initMotionCommon. Maybe we can safe remove\nconst initMotionCommonLeave = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\nexport const initMotion = (motionCls, inKeyframes, outKeyframes, duration, sameLevel = false) => {\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return {\n    [`\n      ${sameLevelPrefix}${motionCls}-enter,\n      ${sameLevelPrefix}${motionCls}-appear\n    `]: Object.assign(Object.assign({}, initMotionCommon(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`${sameLevelPrefix}${motionCls}-leave`]: Object.assign(Object.assign({}, initMotionCommonLeave(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`\n      ${sameLevelPrefix}${motionCls}-enter${motionCls}-enter-active,\n      ${sameLevelPrefix}${motionCls}-appear${motionCls}-appear-active\n    `]: {\n      animationName: inKeyframes,\n      animationPlayState: 'running'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave${motionCls}-leave-active`]: {\n      animationName: outKeyframes,\n      animationPlayState: 'running',\n      pointerEvents: 'none'\n    }\n  };\n};", "map": {"version": 3, "names": ["initMotionCommon", "duration", "animationDuration", "animationFillMode", "initMotionCommonLeave", "initMotion", "motionCls", "inKeyframes", "outKeyframes", "sameLevel", "sameLevelPrefix", "Object", "assign", "animationPlayState", "animationName", "pointerEvents"], "sources": ["/Users/<USER>/github/7/admin/node_modules/antd/es/style/motion/motion.js"], "sourcesContent": ["const initMotionCommon = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\n// FIXME: origin less code seems same as initMotionCommon. Maybe we can safe remove\nconst initMotionCommonLeave = duration => ({\n  animationDuration: duration,\n  animationFillMode: 'both'\n});\nexport const initMotion = (motionCls, inKeyframes, outKeyframes, duration, sameLevel = false) => {\n  const sameLevelPrefix = sameLevel ? '&' : '';\n  return {\n    [`\n      ${sameLevelPrefix}${motionCls}-enter,\n      ${sameLevelPrefix}${motionCls}-appear\n    `]: Object.assign(Object.assign({}, initMotionCommon(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`${sameLevelPrefix}${motionCls}-leave`]: Object.assign(Object.assign({}, initMotionCommonLeave(duration)), {\n      animationPlayState: 'paused'\n    }),\n    [`\n      ${sameLevelPrefix}${motionCls}-enter${motionCls}-enter-active,\n      ${sameLevelPrefix}${motionCls}-appear${motionCls}-appear-active\n    `]: {\n      animationName: inKeyframes,\n      animationPlayState: 'running'\n    },\n    [`${sameLevelPrefix}${motionCls}-leave${motionCls}-leave-active`]: {\n      animationName: outKeyframes,\n      animationPlayState: 'running',\n      pointerEvents: 'none'\n    }\n  };\n};"], "mappings": "AAAA,MAAMA,gBAAgB,GAAGC,QAAQ,KAAK;EACpCC,iBAAiB,EAAED,QAAQ;EAC3BE,iBAAiB,EAAE;AACrB,CAAC,CAAC;AACF;AACA,MAAMC,qBAAqB,GAAGH,QAAQ,KAAK;EACzCC,iBAAiB,EAAED,QAAQ;EAC3BE,iBAAiB,EAAE;AACrB,CAAC,CAAC;AACF,OAAO,MAAME,UAAU,GAAGA,CAACC,SAAS,EAAEC,WAAW,EAAEC,YAAY,EAAEP,QAAQ,EAAEQ,SAAS,GAAG,KAAK,KAAK;EAC/F,MAAMC,eAAe,GAAGD,SAAS,GAAG,GAAG,GAAG,EAAE;EAC5C,OAAO;IACL,CAAC;AACL,QAAQC,eAAe,GAAGJ,SAAS;AACnC,QAAQI,eAAe,GAAGJ,SAAS;AACnC,KAAK,GAAGK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,gBAAgB,CAACC,QAAQ,CAAC,CAAC,EAAE;MAC/DY,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,CAAC,GAAGH,eAAe,GAAGJ,SAAS,QAAQ,GAAGK,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAER,qBAAqB,CAACH,QAAQ,CAAC,CAAC,EAAE;MAC1GY,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,CAAC;AACL,QAAQH,eAAe,GAAGJ,SAAS,SAASA,SAAS;AACrD,QAAQI,eAAe,GAAGJ,SAAS,UAAUA,SAAS;AACtD,KAAK,GAAG;MACFQ,aAAa,EAAEP,WAAW;MAC1BM,kBAAkB,EAAE;IACtB,CAAC;IACD,CAAC,GAAGH,eAAe,GAAGJ,SAAS,SAASA,SAAS,eAAe,GAAG;MACjEQ,aAAa,EAAEN,YAAY;MAC3BK,kBAAkB,EAAE,SAAS;MAC7BE,aAAa,EAAE;IACjB;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}