{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/components/Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Layout, Menu, Modal } from \"antd\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport { DashboardOutlined, ShoppingOutlined, AppstoreAddOutlined, LockOutlined, LogoutOutlined, UserOutlined } from \"@ant-design/icons\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    logout,\n    user\n  } = useAuth();\n  const menuItems = [{\n    key: \"/\",\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 13\n    }, this),\n    label: \"仪表盘\"\n  }, {\n    key: \"/products\",\n    icon: /*#__PURE__*/_jsxDEV(ShoppingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 13\n    }, this),\n    label: \"商品管理\"\n  }, {\n    key: \"/products/create\",\n    icon: /*#__PURE__*/_jsxDEV(AppstoreAddOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 13\n    }, this),\n    label: \"创建商品\"\n  }, {\n    key: \"/change-password\",\n    icon: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 13\n    }, this),\n    label: \"更改密码\"\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    if (key === \"logout\") {\n      Modal.confirm({\n        title: \"确认退出\",\n        content: \"您确定要退出登录吗？\",\n        onOk: () => {\n          logout();\n          navigate(\"/login\");\n        }\n      });\n    } else {\n      navigate(key);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Sider, {\n    width: 200,\n    theme: \"dark\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: 64,\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        color: \"white\",\n        fontSize: \"18px\",\n        fontWeight: \"bold\"\n      },\n      children: \"\\u5546\\u54C1\\u7BA1\\u7406\\u5E73\\u53F0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      mode: \"inline\",\n      selectedKeys: [location.pathname],\n      items: menuItems,\n      onClick: handleMenuClick,\n      theme: \"dark\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: \"absolute\",\n        bottom: 0,\n        width: \"100%\",\n        borderTop: \"1px solid #434343\",\n        padding: \"12px 16px\",\n        background: \"#001529\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: \"rgba(255, 255, 255, 0.65)\",\n          fontSize: \"12px\",\n          marginBottom: \"8px\",\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"8px\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), user === null || user === void 0 ? void 0 : user.username]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: \"#ff4d4f\",\n          cursor: \"pointer\",\n          fontSize: \"12px\",\n          display: \"flex\",\n          alignItems: \"center\",\n          gap: \"8px\"\n        },\n        onClick: () => handleMenuClick({\n          key: \"logout\"\n        }),\n        children: [/*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), \"\\u9000\\u51FA\\u767B\\u5F55\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"ne7uFX/HClqKJDmhTyddXd59eRE=\", false, function () {\n  return [useNavigate, useLocation, useAuth];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Layout", "<PERSON><PERSON>", "Modal", "useNavigate", "useLocation", "useAuth", "DashboardOutlined", "ShoppingOutlined", "AppstoreAddOutlined", "LockOutlined", "LogoutOutlined", "UserOutlined", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "Sidebar", "_s", "navigate", "location", "logout", "user", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "confirm", "title", "content", "onOk", "width", "theme", "children", "style", "height", "display", "alignItems", "justifyContent", "color", "fontSize", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "onClick", "position", "bottom", "borderTop", "padding", "background", "marginBottom", "gap", "username", "cursor", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/components/Sidebar.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Layout, <PERSON>u, Modal } from \"antd\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { useAuth } from \"../contexts/AuthContext\";\nimport {\n  DashboardOutlined,\n  ShoppingOutlined,\n  AppstoreAddOutlined,\n  LockOutlined,\n  LogoutOutlined,\n  UserOutlined,\n} from \"@ant-design/icons\";\n\nconst { Sider } = Layout;\n\nconst Sidebar: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { logout, user } = useAuth();\n\n  const menuItems = [\n    {\n      key: \"/\",\n      icon: <DashboardOutlined />,\n      label: \"仪表盘\",\n    },\n    {\n      key: \"/products\",\n      icon: <ShoppingOutlined />,\n      label: \"商品管理\",\n    },\n    {\n      key: \"/products/create\",\n      icon: <AppstoreAddOutlined />,\n      label: \"创建商品\",\n    },\n    {\n      key: \"/change-password\",\n      icon: <LockOutlined />,\n      label: \"更改密码\",\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    if (key === \"logout\") {\n      Modal.confirm({\n        title: \"确认退出\",\n        content: \"您确定要退出登录吗？\",\n        onOk: () => {\n          logout();\n          navigate(\"/login\");\n        },\n      });\n    } else {\n      navigate(key);\n    }\n  };\n\n  return (\n    <Sider width={200} theme=\"dark\">\n      <div\n        style={{\n          height: 64,\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          color: \"white\",\n          fontSize: \"18px\",\n          fontWeight: \"bold\",\n        }}\n      >\n        商品管理平台\n      </div>\n      <Menu\n        mode=\"inline\"\n        selectedKeys={[location.pathname]}\n        items={menuItems}\n        onClick={handleMenuClick}\n        theme=\"dark\"\n      />\n\n      {/* 用户信息和退出按钮 */}\n      <div\n        style={{\n          position: \"absolute\",\n          bottom: 0,\n          width: \"100%\",\n          borderTop: \"1px solid #434343\",\n          padding: \"12px 16px\",\n          background: \"#001529\",\n        }}\n      >\n        <div\n          style={{\n            color: \"rgba(255, 255, 255, 0.65)\",\n            fontSize: \"12px\",\n            marginBottom: \"8px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n          }}\n        >\n          <UserOutlined />\n          {user?.username}\n        </div>\n        <div\n          style={{\n            color: \"#ff4d4f\",\n            cursor: \"pointer\",\n            fontSize: \"12px\",\n            display: \"flex\",\n            alignItems: \"center\",\n            gap: \"8px\",\n          }}\n          onClick={() => handleMenuClick({ key: \"logout\" })}\n        >\n          <LogoutOutlined />\n          退出登录\n        </div>\n      </div>\n    </Sider>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC1C,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,yBAAyB;AACjD,SACEC,iBAAiB,EACjBC,gBAAgB,EAChBC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC;AAAM,CAAC,GAAGd,MAAM;AAExB,MAAMe,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEe,MAAM;IAAEC;EAAK,CAAC,GAAGf,OAAO,CAAC,CAAC;EAElC,MAAMgB,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAEV,OAAA,CAACP,iBAAiB;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAEV,OAAA,CAACN,gBAAgB;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC1BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAEV,OAAA,CAACL,mBAAmB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAEV,OAAA,CAACJ,YAAY;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAAC;IAAEP;EAAqB,CAAC,KAAK;IACpD,IAAIA,GAAG,KAAK,QAAQ,EAAE;MACpBpB,KAAK,CAAC4B,OAAO,CAAC;QACZC,KAAK,EAAE,MAAM;QACbC,OAAO,EAAE,YAAY;QACrBC,IAAI,EAAEA,CAAA,KAAM;UACVd,MAAM,CAAC,CAAC;UACRF,QAAQ,CAAC,QAAQ,CAAC;QACpB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLA,QAAQ,CAACK,GAAG,CAAC;IACf;EACF,CAAC;EAED,oBACET,OAAA,CAACC,KAAK;IAACoB,KAAK,EAAE,GAAI;IAACC,KAAK,EAAC,MAAM;IAAAC,QAAA,gBAC7BvB,OAAA;MACEwB,KAAK,EAAE;QACLC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,EACH;IAED;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNd,OAAA,CAACZ,IAAI;MACH4C,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAE,CAAC5B,QAAQ,CAAC6B,QAAQ,CAAE;MAClCC,KAAK,EAAE3B,SAAU;MACjB4B,OAAO,EAAEpB,eAAgB;MACzBM,KAAK,EAAC;IAAM;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eAGFd,OAAA;MACEwB,KAAK,EAAE;QACLa,QAAQ,EAAE,UAAU;QACpBC,MAAM,EAAE,CAAC;QACTjB,KAAK,EAAE,MAAM;QACbkB,SAAS,EAAE,mBAAmB;QAC9BC,OAAO,EAAE,WAAW;QACpBC,UAAU,EAAE;MACd,CAAE;MAAAlB,QAAA,gBAEFvB,OAAA;QACEwB,KAAK,EAAE;UACLK,KAAK,EAAE,2BAA2B;UAClCC,QAAQ,EAAE,MAAM;UAChBY,YAAY,EAAE,KAAK;UACnBhB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBgB,GAAG,EAAE;QACP,CAAE;QAAApB,QAAA,gBAEFvB,OAAA,CAACF,YAAY;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACfP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqC,QAAQ;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eACNd,OAAA;QACEwB,KAAK,EAAE;UACLK,KAAK,EAAE,SAAS;UAChBgB,MAAM,EAAE,SAAS;UACjBf,QAAQ,EAAE,MAAM;UAChBJ,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBgB,GAAG,EAAE;QACP,CAAE;QACFP,OAAO,EAAEA,CAAA,KAAMpB,eAAe,CAAC;UAAEP,GAAG,EAAE;QAAS,CAAC,CAAE;QAAAc,QAAA,gBAElDvB,OAAA,CAACH,cAAc;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,4BAEpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AAACX,EAAA,CA3GID,OAAiB;EAAA,QACJZ,WAAW,EACXC,WAAW,EACHC,OAAO;AAAA;AAAAsD,EAAA,GAH5B5C,OAAiB;AA6GvB,eAAeA,OAAO;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}