{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/components/Header.tsx\";\nimport React from \"react\";\nimport { Layout, Typography } from \"antd\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header: AntHeader\n} = Layout;\nconst {\n  Title\n} = Typography;\nconst Header = () => {\n  return /*#__PURE__*/_jsxDEV(AntHeader, {\n    style: {\n      background: \"#fff\",\n      padding: \"0 24px\",\n      display: \"flex\",\n      alignItems: \"center\",\n      borderBottom: \"1px solid #f0f0f0\"\n    },\n    children: /*#__PURE__*/_jsxDEV(Title, {\n      level: 4,\n      style: {\n        margin: 0\n      },\n      children: \"\\u5546\\u54C1\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this);\n};\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "Layout", "Typography", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Title", "style", "background", "padding", "display", "alignItems", "borderBottom", "children", "level", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/components/Header.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Layout, Typography } from \"antd\";\n\nconst { Header: AntHeader } = Layout;\nconst { Title } = Typography;\n\nconst Header: React.FC = () => {\n  return (\n    <AntHeader\n      style={{\n        background: \"#fff\",\n        padding: \"0 24px\",\n        display: \"flex\",\n        alignItems: \"center\",\n        borderBottom: \"1px solid #f0f0f0\",\n      }}\n    >\n      <Title level={4} style={{ margin: 0 }}>\n        商品管理系统\n      </Title>\n    </AntHeader>\n  );\n};\n\nexport default Header;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,UAAU,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1C,MAAM;EAAEC,MAAM,EAAEC;AAAU,CAAC,GAAGL,MAAM;AACpC,MAAM;EAAEM;AAAM,CAAC,GAAGL,UAAU;AAE5B,MAAMG,MAAgB,GAAGA,CAAA,KAAM;EAC7B,oBACED,OAAA,CAACE,SAAS;IACRE,KAAK,EAAE;MACLC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,QAAQ;MACjBC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,YAAY,EAAE;IAChB,CAAE;IAAAC,QAAA,eAEFV,OAAA,CAACG,KAAK;MAACQ,KAAK,EAAE,CAAE;MAACP,KAAK,EAAE;QAAEQ,MAAM,EAAE;MAAE,CAAE;MAAAF,QAAA,EAAC;IAEvC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEhB,CAAC;AAACC,EAAA,GAhBIhB,MAAgB;AAkBtB,eAAeA,MAAM;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}