{"ast": null, "code": "import axios from \"axios\";\n\n// 环境配置\nconst getApiBaseUrl = () => {\n  const env = process.env.REACT_APP_ENV || \"development\";\n  switch (env) {\n    case \"production\":\n      return process.env.REACT_APP_API_URL || \"https://api.your-domain.com\";\n    case \"staging\":\n      return process.env.REACT_APP_API_URL || \"https://api-staging.your-domain.com\";\n    case \"development\":\n    default:\n      return process.env.REACT_APP_API_URL || \"http://localhost:3000\";\n  }\n};\nconst API_BASE_URL = getApiBaseUrl();\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\"\n  }\n});\n\n// 请求拦截器\napi.interceptors.request.use(config => {\n  // 可以在这里添加认证 token\n  // const token = localStorage.getItem('token');\n  // if (token) {\n  //   config.headers.Authorization = `Bearer ${token}`;\n  // }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器\napi.interceptors.response.use(response => {\n  return response.data;\n}, error => {\n  // 根据环境决定是否打印详细错误信息\n  if (process.env.REACT_APP_DEBUG === \"true\") {\n    console.error(\"API Error:\", error);\n  }\n\n  // 生产环境下可以集成错误监控服务\n  if (process.env.REACT_APP_ENV === \"production\" && process.env.REACT_APP_SENTRY_DSN) {\n    // 这里可以集成 Sentry 等错误监控服务\n    // Sentry.captureException(error);\n  }\n  return Promise.reject(error);\n});\n\n// 商品相关接口\n\n// 二维码 API\nexport const qrCodeApi = {\n  // 获取二维码列表\n  getList: params => {\n    return api.get(\"/qr-codes\", {\n      params\n    });\n  },\n  // 获取二维码详情\n  getById: id => {\n    return api.get(`/qr-codes/${id}`);\n  },\n  // 创建二维码\n  create: data => {\n    return api.post(\"/qr-codes\", data);\n  },\n  // 更新二维码\n  update: (id, data) => {\n    return api.patch(`/qr-codes/${id}`, data);\n  },\n  // 删除二维码\n  delete: id => {\n    return api.delete(`/qr-codes/${id}`);\n  },\n  // 获取统计信息\n  getStats: () => {\n    return api.get(\"/qr-codes/stats\");\n  },\n  // 验证二维码\n  verify: (code, wechatUserId) => {\n    return api.post(\"/qr-codes/verify\", {\n      code,\n      wechatUserId\n    });\n  }\n};\n\n// 商品 API\nexport const productApi = {\n  // 获取商品列表\n  getList: params => {\n    return api.get(\"/products\", {\n      params\n    });\n  },\n  // 获取商品详情\n  getById: id => {\n    return api.get(`/products/${id}`);\n  },\n  // 创建商品\n  create: data => {\n    return api.post(\"/products\", data);\n  },\n  // 更新商品\n  update: (id, data) => {\n    return api.patch(`/products/${id}`, data);\n  },\n  // 删除商品\n  delete: id => {\n    return api.delete(`/products/${id}`);\n  },\n  // 获取统计信息\n  getStats: () => {\n    return api.get(\"/products/stats\");\n  },\n  // 获取所有品牌\n  getBrands: () => {\n    return api.get(\"/products/brands\");\n  },\n  // 获取所有分类\n  getCategories: () => {\n    return api.get(\"/products/categories\");\n  },\n  // 根据商品编号获取商品\n  getByProductNumber: productNumber => {\n    return api.get(`/products/number/${productNumber}`);\n  }\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "getApiBaseUrl", "env", "process", "REACT_APP_ENV", "REACT_APP_API_URL", "API_BASE_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "error", "Promise", "reject", "response", "data", "REACT_APP_DEBUG", "console", "REACT_APP_SENTRY_DSN", "qrCodeApi", "getList", "params", "get", "getById", "id", "post", "update", "patch", "delete", "getStats", "verify", "code", "wechatUserId", "productApi", "getBrands", "getCategories", "getByProductNumber", "productNumber"], "sources": ["/Users/<USER>/github/7/admin/src/services/api.ts"], "sourcesContent": ["import axios from \"axios\";\n\n// 环境配置\nconst getApiBaseUrl = () => {\n  const env = process.env.REACT_APP_ENV || \"development\";\n\n  switch (env) {\n    case \"production\":\n      return process.env.REACT_APP_API_URL || \"https://api.your-domain.com\";\n    case \"staging\":\n      return (\n        process.env.REACT_APP_API_URL || \"https://api-staging.your-domain.com\"\n      );\n    case \"development\":\n    default:\n      return process.env.REACT_APP_API_URL || \"http://localhost:3000\";\n  }\n};\n\nconst API_BASE_URL = getApiBaseUrl();\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 10000,\n  headers: {\n    \"Content-Type\": \"application/json\",\n  },\n});\n\n// 请求拦截器\napi.interceptors.request.use(\n  (config) => {\n    // 可以在这里添加认证 token\n    // const token = localStorage.getItem('token');\n    // if (token) {\n    //   config.headers.Authorization = `Bearer ${token}`;\n    // }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// 响应拦截器\napi.interceptors.response.use(\n  (response) => {\n    return response.data;\n  },\n  (error) => {\n    // 根据环境决定是否打印详细错误信息\n    if (process.env.REACT_APP_DEBUG === \"true\") {\n      console.error(\"API Error:\", error);\n    }\n\n    // 生产环境下可以集成错误监控服务\n    if (\n      process.env.REACT_APP_ENV === \"production\" &&\n      process.env.REACT_APP_SENTRY_DSN\n    ) {\n      // 这里可以集成 Sentry 等错误监控服务\n      // Sentry.captureException(error);\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport interface QrCode {\n  _id: string;\n  code: string;\n  content: string;\n  name: string;\n  description?: string;\n  qrImageUrl: string;\n  status: \"active\" | \"inactive\" | \"expired\";\n  expiresAt?: string;\n  scanCount: number;\n  lastScannedAt?: string;\n  wechatUserId?: string;\n  wechatUserInfo?: {\n    nickname?: string;\n    avatar?: string;\n    openid?: string;\n  };\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface CreateQrCodeData {\n  name: string;\n  content: string;\n  description?: string;\n  status?: string;\n  expiresAt?: string;\n}\n\nexport interface QueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  status?: string;\n  sortBy?: string;\n  sortOrder?: string;\n}\n\nexport interface QrCodeListResponse {\n  data: QrCode[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface StatsResponse {\n  total: number;\n  active: number;\n  inactive: number;\n  expired: number;\n  totalScans: number;\n}\n\n// 商品相关接口\nexport interface Product {\n  _id: string;\n  name: string;\n  productNumber: string;\n  alcoholContent: number;\n  packagingDate: string;\n  description?: string;\n  status: \"active\" | \"inactive\" | \"discontinued\";\n  brand?: string;\n  category?: string;\n  volume?: number;\n  batchNumber?: string;\n  productionLocation?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface CreateProductData {\n  name: string;\n  alcoholContent: number;\n  packagingDate: string;\n  description?: string;\n  status?: string;\n  brand?: string;\n  category?: string;\n  volume?: number;\n  batchNumber?: string;\n  productionLocation?: string;\n}\n\nexport interface ProductQueryParams {\n  page?: number;\n  limit?: number;\n  search?: string;\n  status?: string;\n  brand?: string;\n  category?: string;\n  sortBy?: string;\n  sortOrder?: string;\n}\n\nexport interface ProductListResponse {\n  data: Product[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface ProductStatsResponse {\n  total: number;\n  active: number;\n  inactive: number;\n  discontinued: number;\n  totalBrands: number;\n  totalCategories: number;\n}\n\n// 二维码 API\nexport const qrCodeApi = {\n  // 获取二维码列表\n  getList: (params?: QueryParams): Promise<QrCodeListResponse> => {\n    return api.get(\"/qr-codes\", { params });\n  },\n\n  // 获取二维码详情\n  getById: (id: string): Promise<QrCode> => {\n    return api.get(`/qr-codes/${id}`);\n  },\n\n  // 创建二维码\n  create: (data: CreateQrCodeData): Promise<QrCode> => {\n    return api.post(\"/qr-codes\", data);\n  },\n\n  // 更新二维码\n  update: (id: string, data: Partial<CreateQrCodeData>): Promise<QrCode> => {\n    return api.patch(`/qr-codes/${id}`, data);\n  },\n\n  // 删除二维码\n  delete: (id: string): Promise<void> => {\n    return api.delete(`/qr-codes/${id}`);\n  },\n\n  // 获取统计信息\n  getStats: (): Promise<StatsResponse> => {\n    return api.get(\"/qr-codes/stats\");\n  },\n\n  // 验证二维码\n  verify: (code: string, wechatUserId?: string): Promise<any> => {\n    return api.post(\"/qr-codes/verify\", { code, wechatUserId });\n  },\n};\n\n// 商品 API\nexport const productApi = {\n  // 获取商品列表\n  getList: (params?: ProductQueryParams): Promise<ProductListResponse> => {\n    return api.get(\"/products\", { params });\n  },\n\n  // 获取商品详情\n  getById: (id: string): Promise<Product> => {\n    return api.get(`/products/${id}`);\n  },\n\n  // 创建商品\n  create: (data: CreateProductData): Promise<Product> => {\n    return api.post(\"/products\", data);\n  },\n\n  // 更新商品\n  update: (id: string, data: Partial<CreateProductData>): Promise<Product> => {\n    return api.patch(`/products/${id}`, data);\n  },\n\n  // 删除商品\n  delete: (id: string): Promise<void> => {\n    return api.delete(`/products/${id}`);\n  },\n\n  // 获取统计信息\n  getStats: (): Promise<ProductStatsResponse> => {\n    return api.get(\"/products/stats\");\n  },\n\n  // 获取所有品牌\n  getBrands: (): Promise<string[]> => {\n    return api.get(\"/products/brands\");\n  },\n\n  // 获取所有分类\n  getCategories: (): Promise<string[]> => {\n    return api.get(\"/products/categories\");\n  },\n\n  // 根据商品编号获取商品\n  getByProductNumber: (productNumber: string): Promise<Product> => {\n    return api.get(`/products/number/${productNumber}`);\n  },\n};\n\nexport default api;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,MAAMC,GAAG,GAAGC,OAAO,CAACD,GAAG,CAACE,aAAa,IAAI,aAAa;EAEtD,QAAQF,GAAG;IACT,KAAK,YAAY;MACf,OAAOC,OAAO,CAACD,GAAG,CAACG,iBAAiB,IAAI,6BAA6B;IACvE,KAAK,SAAS;MACZ,OACEF,OAAO,CAACD,GAAG,CAACG,iBAAiB,IAAI,qCAAqC;IAE1E,KAAK,aAAa;IAClB;MACE,OAAOF,OAAO,CAACD,GAAG,CAACG,iBAAiB,IAAI,uBAAuB;EACnE;AACF,CAAC;AAED,MAAMC,YAAY,GAAGL,aAAa,CAAC,CAAC;AAEpC,MAAMM,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACA;EACA;EACA;EACA;EACA,OAAOA,MAAM;AACf,CAAC,EACAC,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAT,GAAG,CAACK,YAAY,CAACO,QAAQ,CAACL,GAAG,CAC1BK,QAAQ,IAAK;EACZ,OAAOA,QAAQ,CAACC,IAAI;AACtB,CAAC,EACAJ,KAAK,IAAK;EACT;EACA,IAAIb,OAAO,CAACD,GAAG,CAACmB,eAAe,KAAK,MAAM,EAAE;IAC1CC,OAAO,CAACN,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;;EAEA;EACA,IACEb,OAAO,CAACD,GAAG,CAACE,aAAa,KAAK,YAAY,IAC1CD,OAAO,CAACD,GAAG,CAACqB,oBAAoB,EAChC;IACA;IACA;EAAA;EAGF,OAAON,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAwDD;;AA2DA;AACA,OAAO,MAAMQ,SAAS,GAAG;EACvB;EACAC,OAAO,EAAGC,MAAoB,IAAkC;IAC9D,OAAOnB,GAAG,CAACoB,GAAG,CAAC,WAAW,EAAE;MAAED;IAAO,CAAC,CAAC;EACzC,CAAC;EAED;EACAE,OAAO,EAAGC,EAAU,IAAsB;IACxC,OAAOtB,GAAG,CAACoB,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EACnC,CAAC;EAED;EACArB,MAAM,EAAGY,IAAsB,IAAsB;IACnD,OAAOb,GAAG,CAACuB,IAAI,CAAC,WAAW,EAAEV,IAAI,CAAC;EACpC,CAAC;EAED;EACAW,MAAM,EAAEA,CAACF,EAAU,EAAET,IAA+B,KAAsB;IACxE,OAAOb,GAAG,CAACyB,KAAK,CAAC,aAAaH,EAAE,EAAE,EAAET,IAAI,CAAC;EAC3C,CAAC;EAED;EACAa,MAAM,EAAGJ,EAAU,IAAoB;IACrC,OAAOtB,GAAG,CAAC0B,MAAM,CAAC,aAAaJ,EAAE,EAAE,CAAC;EACtC,CAAC;EAED;EACAK,QAAQ,EAAEA,CAAA,KAA8B;IACtC,OAAO3B,GAAG,CAACoB,GAAG,CAAC,iBAAiB,CAAC;EACnC,CAAC;EAED;EACAQ,MAAM,EAAEA,CAACC,IAAY,EAAEC,YAAqB,KAAmB;IAC7D,OAAO9B,GAAG,CAACuB,IAAI,CAAC,kBAAkB,EAAE;MAAEM,IAAI;MAAEC;IAAa,CAAC,CAAC;EAC7D;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxB;EACAb,OAAO,EAAGC,MAA2B,IAAmC;IACtE,OAAOnB,GAAG,CAACoB,GAAG,CAAC,WAAW,EAAE;MAAED;IAAO,CAAC,CAAC;EACzC,CAAC;EAED;EACAE,OAAO,EAAGC,EAAU,IAAuB;IACzC,OAAOtB,GAAG,CAACoB,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EACnC,CAAC;EAED;EACArB,MAAM,EAAGY,IAAuB,IAAuB;IACrD,OAAOb,GAAG,CAACuB,IAAI,CAAC,WAAW,EAAEV,IAAI,CAAC;EACpC,CAAC;EAED;EACAW,MAAM,EAAEA,CAACF,EAAU,EAAET,IAAgC,KAAuB;IAC1E,OAAOb,GAAG,CAACyB,KAAK,CAAC,aAAaH,EAAE,EAAE,EAAET,IAAI,CAAC;EAC3C,CAAC;EAED;EACAa,MAAM,EAAGJ,EAAU,IAAoB;IACrC,OAAOtB,GAAG,CAAC0B,MAAM,CAAC,aAAaJ,EAAE,EAAE,CAAC;EACtC,CAAC;EAED;EACAK,QAAQ,EAAEA,CAAA,KAAqC;IAC7C,OAAO3B,GAAG,CAACoB,GAAG,CAAC,iBAAiB,CAAC;EACnC,CAAC;EAED;EACAY,SAAS,EAAEA,CAAA,KAAyB;IAClC,OAAOhC,GAAG,CAACoB,GAAG,CAAC,kBAAkB,CAAC;EACpC,CAAC;EAED;EACAa,aAAa,EAAEA,CAAA,KAAyB;IACtC,OAAOjC,GAAG,CAACoB,GAAG,CAAC,sBAAsB,CAAC;EACxC,CAAC;EAED;EACAc,kBAAkB,EAAGC,aAAqB,IAAuB;IAC/D,OAAOnC,GAAG,CAACoB,GAAG,CAAC,oBAAoBe,aAAa,EAAE,CAAC;EACrD;AACF,CAAC;AAED,eAAenC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}