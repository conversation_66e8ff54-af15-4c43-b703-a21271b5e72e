{"ast": null, "code": "function useProdHMR() {\n  return false;\n}\nvar webpackHMR = false;\nfunction useDevHMR() {\n  return webpackHMR;\n}\nexport default process.env.NODE_ENV === 'production' ? useProdHMR : useDevHMR;\n\n// Webpack `module.hot.accept` do not support any deps update trigger\n// We have to hack handler to force mark as HRM\nif (process.env.NODE_ENV !== 'production' && typeof module !== 'undefined' && module && module.hot && typeof window !== 'undefined') {\n  // Use `globalThis` first, and `window` for older browsers\n  // const win = globalThis as any;\n  var win = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : null;\n  if (win && typeof win.webpackHotUpdate === 'function') {\n    var originWebpackHotUpdate = win.webpackHotUpdate;\n    win.webpackHotUpdate = function () {\n      webpackHMR = true;\n      setTimeout(function () {\n        webpackHMR = false;\n      }, 0);\n      return originWebpackHotUpdate.apply(void 0, arguments);\n    };\n  }\n}", "map": {"version": 3, "names": ["useProdHMR", "webpackHMR", "useDevHMR", "process", "env", "NODE_ENV", "module", "hot", "window", "win", "globalThis", "webpackHotUpdate", "originWebpackHotUpdate", "setTimeout", "apply", "arguments"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@ant-design/cssinjs/es/hooks/useHMR.js"], "sourcesContent": ["function useProdHMR() {\n  return false;\n}\nvar webpackHMR = false;\nfunction useDevHMR() {\n  return webpackHMR;\n}\nexport default process.env.NODE_ENV === 'production' ? useProdHMR : useDevHMR;\n\n// Webpack `module.hot.accept` do not support any deps update trigger\n// We have to hack handler to force mark as HRM\nif (process.env.NODE_ENV !== 'production' && typeof module !== 'undefined' && module && module.hot && typeof window !== 'undefined') {\n  // Use `globalThis` first, and `window` for older browsers\n  // const win = globalThis as any;\n  var win = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : null;\n  if (win && typeof win.webpackHotUpdate === 'function') {\n    var originWebpackHotUpdate = win.webpackHotUpdate;\n    win.webpackHotUpdate = function () {\n      webpackHMR = true;\n      setTimeout(function () {\n        webpackHMR = false;\n      }, 0);\n      return originWebpackHotUpdate.apply(void 0, arguments);\n    };\n  }\n}"], "mappings": "AAAA,SAASA,UAAUA,CAAA,EAAG;EACpB,OAAO,KAAK;AACd;AACA,IAAIC,UAAU,GAAG,KAAK;AACtB,SAASC,SAASA,CAAA,EAAG;EACnB,OAAOD,UAAU;AACnB;AACA,eAAeE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGL,UAAU,GAAGE,SAAS;;AAE7E;AACA;AACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,IAAIA,MAAM,CAACC,GAAG,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACnI;EACA;EACA,IAAIC,GAAG,GAAG,OAAOC,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAG,OAAOF,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;EACxG,IAAIC,GAAG,IAAI,OAAOA,GAAG,CAACE,gBAAgB,KAAK,UAAU,EAAE;IACrD,IAAIC,sBAAsB,GAAGH,GAAG,CAACE,gBAAgB;IACjDF,GAAG,CAACE,gBAAgB,GAAG,YAAY;MACjCV,UAAU,GAAG,IAAI;MACjBY,UAAU,CAAC,YAAY;QACrBZ,UAAU,GAAG,KAAK;MACpB,CAAC,EAAE,CAAC,CAAC;MACL,OAAOW,sBAAsB,CAACE,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC;IACxD,CAAC;EACH;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}