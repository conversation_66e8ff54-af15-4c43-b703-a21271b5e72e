{"ast": null, "code": "export const getColumnKey = (column, defaultKey) => {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n};\nexport function getColumnPos(index, pos) {\n  return pos ? `${pos}-${index}` : `${index}`;\n}\nexport const renderColumnTitle = (title, props) => {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  return title;\n};\n/**\n * Safe get column title\n *\n * Should filter [object Object]\n *\n * @param title\n */\nexport const safeColumnTitle = (title, props) => {\n  const res = renderColumnTitle(title, props);\n  if (Object.prototype.toString.call(res) === '[object Object]') {\n    return '';\n  }\n  return res;\n};", "map": {"version": 3, "names": ["getColumnKey", "column", "defaultKey", "key", "undefined", "dataIndex", "Array", "isArray", "join", "getColumnPos", "index", "pos", "renderColumnTitle", "title", "props", "safeColumnTitle", "res", "Object", "prototype", "toString", "call"], "sources": ["/Users/<USER>/github/7/admin/node_modules/antd/es/table/util.js"], "sourcesContent": ["export const getColumnKey = (column, defaultKey) => {\n  if ('key' in column && column.key !== undefined && column.key !== null) {\n    return column.key;\n  }\n  if (column.dataIndex) {\n    return Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex;\n  }\n  return defaultKey;\n};\nexport function getColumnPos(index, pos) {\n  return pos ? `${pos}-${index}` : `${index}`;\n}\nexport const renderColumnTitle = (title, props) => {\n  if (typeof title === 'function') {\n    return title(props);\n  }\n  return title;\n};\n/**\n * Safe get column title\n *\n * Should filter [object Object]\n *\n * @param title\n */\nexport const safeColumnTitle = (title, props) => {\n  const res = renderColumnTitle(title, props);\n  if (Object.prototype.toString.call(res) === '[object Object]') {\n    return '';\n  }\n  return res;\n};"], "mappings": "AAAA,OAAO,MAAMA,YAAY,GAAGA,CAACC,MAAM,EAAEC,UAAU,KAAK;EAClD,IAAI,KAAK,IAAID,MAAM,IAAIA,MAAM,CAACE,GAAG,KAAKC,SAAS,IAAIH,MAAM,CAACE,GAAG,KAAK,IAAI,EAAE;IACtE,OAAOF,MAAM,CAACE,GAAG;EACnB;EACA,IAAIF,MAAM,CAACI,SAAS,EAAE;IACpB,OAAOC,KAAK,CAACC,OAAO,CAACN,MAAM,CAACI,SAAS,CAAC,GAAGJ,MAAM,CAACI,SAAS,CAACG,IAAI,CAAC,GAAG,CAAC,GAAGP,MAAM,CAACI,SAAS;EACxF;EACA,OAAOH,UAAU;AACnB,CAAC;AACD,OAAO,SAASO,YAAYA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACvC,OAAOA,GAAG,GAAG,GAAGA,GAAG,IAAID,KAAK,EAAE,GAAG,GAAGA,KAAK,EAAE;AAC7C;AACA,OAAO,MAAME,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;EACjD,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IAC/B,OAAOA,KAAK,CAACC,KAAK,CAAC;EACrB;EACA,OAAOD,KAAK;AACd,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,eAAe,GAAGA,CAACF,KAAK,EAAEC,KAAK,KAAK;EAC/C,MAAME,GAAG,GAAGJ,iBAAiB,CAACC,KAAK,EAAEC,KAAK,CAAC;EAC3C,IAAIG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC,KAAK,iBAAiB,EAAE;IAC7D,OAAO,EAAE;EACX;EACA,OAAOA,GAAG;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}