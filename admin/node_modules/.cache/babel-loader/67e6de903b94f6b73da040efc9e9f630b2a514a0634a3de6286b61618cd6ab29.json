{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"containerRef\"];\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    containerRef = props.containerRef,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(RefContext),\n    panelRef = _React$useContext.panel;\n  var mergedRef = useComposeRef(panelRef, containerRef);\n\n  // =============================== Render ===============================\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    role: \"dialog\",\n    ref: mergedRef\n  }, pickAttrs(props, {\n    aria: true\n  }), {\n    \"aria-modal\": \"true\"\n  }, restProps));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "classNames", "React", "RefContext", "pickAttrs", "useComposeRef", "<PERSON>er<PERSON><PERSON><PERSON>", "props", "prefixCls", "className", "containerRef", "restProps", "_React$useContext", "useContext", "panelRef", "panel", "mergedRef", "createElement", "concat", "role", "ref", "aria", "process", "env", "NODE_ENV", "displayName"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-drawer/es/DrawerPanel.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"prefixCls\", \"className\", \"containerRef\"];\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { RefContext } from \"./context\";\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport { useComposeRef } from \"rc-util/es/ref\";\nvar DrawerPanel = function DrawerPanel(props) {\n  var prefixCls = props.prefixCls,\n    className = props.className,\n    containerRef = props.containerRef,\n    restProps = _objectWithoutProperties(props, _excluded);\n  var _React$useContext = React.useContext(RefContext),\n    panelRef = _React$useContext.panel;\n  var mergedRef = useComposeRef(panelRef, containerRef);\n\n  // =============================== Render ===============================\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({\n    className: classNames(\"\".concat(prefixCls, \"-content\"), className),\n    role: \"dialog\",\n    ref: mergedRef\n  }, pickAttrs(props, {\n    aria: true\n  }), {\n    \"aria-modal\": \"true\"\n  }, restProps));\n};\nif (process.env.NODE_ENV !== 'production') {\n  DrawerPanel.displayName = 'DrawerPanel';\n}\nexport default DrawerPanel;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,cAAc,CAAC;AAC1D,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,QAAQ,WAAW;AACtC,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAE;EAC5C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC3BC,YAAY,GAAGH,KAAK,CAACG,YAAY;IACjCC,SAAS,GAAGZ,wBAAwB,CAACQ,KAAK,EAAEP,SAAS,CAAC;EACxD,IAAIY,iBAAiB,GAAGV,KAAK,CAACW,UAAU,CAACV,UAAU,CAAC;IAClDW,QAAQ,GAAGF,iBAAiB,CAACG,KAAK;EACpC,IAAIC,SAAS,GAAGX,aAAa,CAACS,QAAQ,EAAEJ,YAAY,CAAC;;EAErD;;EAEA,OAAO,aAAaR,KAAK,CAACe,aAAa,CAAC,KAAK,EAAEnB,QAAQ,CAAC;IACtDW,SAAS,EAAER,UAAU,CAAC,EAAE,CAACiB,MAAM,CAACV,SAAS,EAAE,UAAU,CAAC,EAAEC,SAAS,CAAC;IAClEU,IAAI,EAAE,QAAQ;IACdC,GAAG,EAAEJ;EACP,CAAC,EAAEZ,SAAS,CAACG,KAAK,EAAE;IAClBc,IAAI,EAAE;EACR,CAAC,CAAC,EAAE;IACF,YAAY,EAAE;EAChB,CAAC,EAAEV,SAAS,CAAC,CAAC;AAChB,CAAC;AACD,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzClB,WAAW,CAACmB,WAAW,GAAG,aAAa;AACzC;AACA,eAAenB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}