{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/pages/ProductList.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Table, Button, Space, Tag, Input, Select, message, Popconfirm, Card, Tooltip } from \"antd\";\nimport { useNavigate } from \"react-router-dom\";\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined, ReloadOutlined } from \"@ant-design/icons\";\nimport dayjs from \"dayjs\";\nimport { productApi } from \"../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst ProductList = () => {\n  _s();\n  const navigate = useNavigate();\n  const [data, setData] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [filters, setFilters] = useState({});\n  const [brands, setBrands] = useState([]);\n  const [categories, setCategories] = useState([]);\n  useEffect(() => {\n    fetchData();\n    fetchBrands();\n    fetchCategories();\n  }, [pagination.current, pagination.pageSize, filters]);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: pagination.current,\n        limit: pagination.pageSize,\n        ...filters\n      };\n      const response = await productApi.getList(params);\n      setData(response.data);\n      setPagination(prev => ({\n        ...prev,\n        total: response.total\n      }));\n    } catch (error) {\n      message.error(\"获取数据失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchBrands = async () => {\n    try {\n      const brandList = await productApi.getBrands();\n      setBrands(brandList);\n    } catch (error) {\n      console.error(\"获取品牌列表失败\");\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const categoryList = await productApi.getCategories();\n      setCategories(categoryList);\n    } catch (error) {\n      console.error(\"获取分类列表失败\");\n    }\n  };\n  const handleDelete = async id => {\n    try {\n      await productApi.delete(id);\n      message.success(\"删除成功\");\n      fetchData();\n    } catch (error) {\n      message.error(\"删除失败\");\n    }\n  };\n  const handleSearch = value => {\n    setFilters(prev => ({\n      ...prev,\n      search: value\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleStatusFilter = value => {\n    setFilters(prev => ({\n      ...prev,\n      status: value || undefined\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleBrandFilter = value => {\n    setFilters(prev => ({\n      ...prev,\n      brand: value || undefined\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const handleCategoryFilter = value => {\n    setFilters(prev => ({\n      ...prev,\n      category: value || undefined\n    }));\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n  };\n  const getStatusTag = status => {\n    const statusMap = {\n      active: {\n        color: \"green\",\n        text: \"活跃\"\n      },\n      inactive: {\n        color: \"orange\",\n        text: \"停用\"\n      },\n      discontinued: {\n        color: \"red\",\n        text: \"停产\"\n      }\n    };\n    const config = statusMap[status];\n    return /*#__PURE__*/_jsxDEV(Tag, {\n      color: config.color,\n      children: config.text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 124,\n      columnNumber: 12\n    }, this);\n  };\n  const columns = [{\n    title: \"商品编号\",\n    dataIndex: \"productNumber\",\n    key: \"productNumber\",\n    width: 120,\n    fixed: \"left\",\n    render: productNumber => /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontFamily: \"monospace\",\n        fontWeight: \"bold\"\n      },\n      children: productNumber\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: \"商品名称\",\n    dataIndex: \"name\",\n    key: \"name\",\n    width: 150,\n    ellipsis: {\n      showTitle: false\n    },\n    render: name => /*#__PURE__*/_jsxDEV(Tooltip, {\n      placement: \"topLeft\",\n      title: name,\n      children: name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: \"酒精度\",\n    dataIndex: \"alcoholContent\",\n    key: \"alcoholContent\",\n    width: 100,\n    render: alcoholContent => `${alcoholContent}%`,\n    sorter: true\n  }, {\n    title: \"包装日期\",\n    dataIndex: \"packagingDate\",\n    key: \"packagingDate\",\n    width: 120,\n    render: date => dayjs(date).format(\"YYYY-MM-DD\"),\n    sorter: true\n  }, {\n    title: \"品牌\",\n    dataIndex: \"brand\",\n    key: \"brand\",\n    width: 100,\n    render: brand => brand || \"-\"\n  }, {\n    title: \"分类\",\n    dataIndex: \"category\",\n    key: \"category\",\n    width: 100,\n    render: category => category || \"-\"\n  }, {\n    title: \"容量\",\n    dataIndex: \"volume\",\n    key: \"volume\",\n    width: 80,\n    render: volume => volume ? `${volume}ml` : \"-\"\n  }, {\n    title: \"状态\",\n    dataIndex: \"status\",\n    key: \"status\",\n    width: 80,\n    render: status => getStatusTag(status)\n  }, {\n    title: \"创建时间\",\n    dataIndex: \"createdAt\",\n    key: \"createdAt\",\n    width: 120,\n    render: date => dayjs(date).format(\"MM-DD HH:mm\"),\n    sorter: true\n  }, {\n    title: \"操作\",\n    key: \"action\",\n    width: 180,\n    fixed: \"right\",\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 19\n        }, this),\n        onClick: () => navigate(`/products/${record._id}`),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 19\n        }, this),\n        onClick: () => navigate(`/products/${record._id}?edit=true`),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u5546\\u54C1\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record._id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 46\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 213,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          style: {\n            margin: 0\n          },\n          children: \"\\u5546\\u54C1\\u7BA1\\u7406\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 19\n          }, this),\n          onClick: () => navigate(\"/products/create\"),\n          children: \"\\u521B\\u5EFA\\u5546\\u54C1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16,\n          display: \"flex\",\n          gap: 16,\n          flexWrap: \"wrap\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(Search, {\n          placeholder: \"\\u641C\\u7D22\\u5546\\u54C1\\u540D\\u79F0\\u3001\\u7F16\\u53F7\\u3001\\u54C1\\u724C\\u7B49\",\n          allowClear: true,\n          style: {\n            width: 300\n          },\n          onSearch: handleSearch,\n          enterButton: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 26\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u7B5B\\u9009\\u72B6\\u6001\",\n          allowClear: true,\n          style: {\n            width: 120\n          },\n          onChange: handleStatusFilter,\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"active\",\n            children: \"\\u6D3B\\u8DC3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"inactive\",\n            children: \"\\u505C\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"discontinued\",\n            children: \"\\u505C\\u4EA7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u7B5B\\u9009\\u54C1\\u724C\",\n          allowClear: true,\n          style: {\n            width: 120\n          },\n          onChange: handleBrandFilter,\n          children: brands.map(brand => /*#__PURE__*/_jsxDEV(Option, {\n            value: brand,\n            children: brand\n          }, brand, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u7B5B\\u9009\\u5206\\u7C7B\",\n          allowClear: true,\n          style: {\n            width: 120\n          },\n          onChange: handleCategoryFilter,\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n            value: category,\n            children: category\n          }, category, false, {\n            fileName: _jsxFileName,\n            lineNumber: 308,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 313,\n            columnNumber: 25\n          }, this),\n          onClick: fetchData,\n          children: \"\\u5237\\u65B0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: data,\n        rowKey: \"_id\",\n        loading: loading,\n        pagination: {\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setPagination(prev => ({\n              ...prev,\n              current: page,\n              pageSize: pageSize || 10\n            }));\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 318,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 245,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 244,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductList, \"uBIKcIQzlGsjdtGbnvanOgtmvog=\", false, function () {\n  return [useNavigate];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Table", "<PERSON><PERSON>", "Space", "Tag", "Input", "Select", "message", "Popconfirm", "Card", "<PERSON><PERSON><PERSON>", "useNavigate", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "SearchOutlined", "ReloadOutlined", "dayjs", "productApi", "jsxDEV", "_jsxDEV", "Search", "Option", "ProductList", "_s", "navigate", "data", "setData", "loading", "setLoading", "pagination", "setPagination", "current", "pageSize", "total", "filters", "setFilters", "brands", "setBrands", "categories", "setCategories", "fetchData", "fetchBrands", "fetchCategories", "params", "page", "limit", "response", "getList", "prev", "error", "brandList", "getBrands", "console", "categoryList", "getCategories", "handleDelete", "id", "delete", "success", "handleSearch", "value", "search", "handleStatusFilter", "status", "undefined", "handleBrandFilter", "brand", "handleCategoryFilter", "category", "getStatusTag", "statusMap", "active", "color", "text", "inactive", "discontinued", "config", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "columns", "title", "dataIndex", "key", "width", "fixed", "render", "productNumber", "style", "fontFamily", "fontWeight", "ellipsis", "showTitle", "name", "placement", "alcoholContent", "sorter", "date", "format", "volume", "_", "record", "size", "type", "icon", "onClick", "_id", "onConfirm", "okText", "cancelText", "danger", "marginBottom", "display", "justifyContent", "alignItems", "margin", "gap", "flexWrap", "placeholder", "allowClear", "onSearch", "enterButton", "onChange", "map", "dataSource", "<PERSON><PERSON><PERSON>", "showSizeChanger", "showQuickJumper", "showTotal", "range", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/pages/ProductList.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport {\n  Table,\n  Button,\n  Space,\n  Tag,\n  Input,\n  Select,\n  message,\n  Popconfirm,\n  Card,\n  Tooltip,\n} from \"antd\";\nimport { useNavigate } from \"react-router-dom\";\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  SearchOutlined,\n  ReloadOutlined,\n} from \"@ant-design/icons\";\nimport dayjs from \"dayjs\";\nimport { productApi, Product, ProductQueryParams } from \"../services/api\";\n\nconst { Search } = Input;\nconst { Option } = Select;\n\nconst ProductList: React.FC = () => {\n  const navigate = useNavigate();\n  const [data, setData] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n  const [filters, setFilters] = useState<ProductQueryParams>({});\n  const [brands, setBrands] = useState<string[]>([]);\n  const [categories, setCategories] = useState<string[]>([]);\n\n  useEffect(() => {\n    fetchData();\n    fetchBrands();\n    fetchCategories();\n  }, [pagination.current, pagination.pageSize, filters]);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      const params = {\n        page: pagination.current,\n        limit: pagination.pageSize,\n        ...filters,\n      };\n      const response = await productApi.getList(params);\n      setData(response.data);\n      setPagination((prev) => ({\n        ...prev,\n        total: response.total,\n      }));\n    } catch (error) {\n      message.error(\"获取数据失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchBrands = async () => {\n    try {\n      const brandList = await productApi.getBrands();\n      setBrands(brandList);\n    } catch (error) {\n      console.error(\"获取品牌列表失败\");\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const categoryList = await productApi.getCategories();\n      setCategories(categoryList);\n    } catch (error) {\n      console.error(\"获取分类列表失败\");\n    }\n  };\n\n  const handleDelete = async (id: string) => {\n    try {\n      await productApi.delete(id);\n      message.success(\"删除成功\");\n      fetchData();\n    } catch (error) {\n      message.error(\"删除失败\");\n    }\n  };\n\n  const handleSearch = (value: string) => {\n    setFilters((prev) => ({ ...prev, search: value }));\n    setPagination((prev) => ({ ...prev, current: 1 }));\n  };\n\n  const handleStatusFilter = (value: string) => {\n    setFilters((prev) => ({ ...prev, status: value || undefined }));\n    setPagination((prev) => ({ ...prev, current: 1 }));\n  };\n\n  const handleBrandFilter = (value: string) => {\n    setFilters((prev) => ({ ...prev, brand: value || undefined }));\n    setPagination((prev) => ({ ...prev, current: 1 }));\n  };\n\n  const handleCategoryFilter = (value: string) => {\n    setFilters((prev) => ({ ...prev, category: value || undefined }));\n    setPagination((prev) => ({ ...prev, current: 1 }));\n  };\n\n  const getStatusTag = (status: string) => {\n    const statusMap = {\n      active: { color: \"green\", text: \"活跃\" },\n      inactive: { color: \"orange\", text: \"停用\" },\n      discontinued: { color: \"red\", text: \"停产\" },\n    };\n    const config = statusMap[status as keyof typeof statusMap];\n    return <Tag color={config.color}>{config.text}</Tag>;\n  };\n\n  const columns = [\n    {\n      title: \"商品编号\",\n      dataIndex: \"productNumber\",\n      key: \"productNumber\",\n      width: 120,\n\n      fixed: \"left\",\n      render: (productNumber: string) => (\n        <span style={{ fontFamily: \"monospace\", fontWeight: \"bold\" }}>\n          {productNumber}\n        </span>\n      ),\n    },\n    {\n      title: \"商品名称\",\n      dataIndex: \"name\",\n      key: \"name\",\n      width: 150,\n      ellipsis: {\n        showTitle: false,\n      },\n      render: (name: string) => (\n        <Tooltip placement=\"topLeft\" title={name}>\n          {name}\n        </Tooltip>\n      ),\n    },\n    {\n      title: \"酒精度\",\n      dataIndex: \"alcoholContent\",\n      key: \"alcoholContent\",\n      width: 100,\n      render: (alcoholContent: number) => `${alcoholContent}%`,\n      sorter: true,\n    },\n    {\n      title: \"包装日期\",\n      dataIndex: \"packagingDate\",\n      key: \"packagingDate\",\n      width: 120,\n      render: (date: string) => dayjs(date).format(\"YYYY-MM-DD\"),\n      sorter: true,\n    },\n    {\n      title: \"品牌\",\n      dataIndex: \"brand\",\n      key: \"brand\",\n      width: 100,\n      render: (brand: string) => brand || \"-\",\n    },\n    {\n      title: \"分类\",\n      dataIndex: \"category\",\n      key: \"category\",\n      width: 100,\n      render: (category: string) => category || \"-\",\n    },\n    {\n      title: \"容量\",\n      dataIndex: \"volume\",\n      key: \"volume\",\n      width: 80,\n      render: (volume: number) => (volume ? `${volume}ml` : \"-\"),\n    },\n    {\n      title: \"状态\",\n      dataIndex: \"status\",\n      key: \"status\",\n      width: 80,\n      render: (status: string) => getStatusTag(status),\n    },\n    {\n      title: \"创建时间\",\n      dataIndex: \"createdAt\",\n      key: \"createdAt\",\n      width: 120,\n      render: (date: string) => dayjs(date).format(\"MM-DD HH:mm\"),\n      sorter: true,\n    },\n    {\n      title: \"操作\",\n      key: \"action\",\n      width: 180,\n      fixed: \"right\" as const,\n      render: (_: any, record: Product) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            icon={<EyeOutlined />}\n            onClick={() => navigate(`/products/${record._id}`)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            icon={<EditOutlined />}\n            onClick={() => navigate(`/products/${record._id}?edit=true`)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个商品吗？\"\n            onConfirm={() => handleDelete(record._id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button type=\"link\" danger icon={<DeleteOutlined />}>\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div>\n      <Card>\n        <div\n          style={{\n            marginBottom: 16,\n            display: \"flex\",\n            justifyContent: \"space-between\",\n            alignItems: \"center\",\n          }}\n        >\n          <h1 style={{ margin: 0 }}>商品管理</h1>\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => navigate(\"/products/create\")}\n          >\n            创建商品\n          </Button>\n        </div>\n\n        <div\n          style={{\n            marginBottom: 16,\n            display: \"flex\",\n            gap: 16,\n            flexWrap: \"wrap\",\n          }}\n        >\n          <Search\n            placeholder=\"搜索商品名称、编号、品牌等\"\n            allowClear\n            style={{ width: 300 }}\n            onSearch={handleSearch}\n            enterButton={<SearchOutlined />}\n          />\n          <Select\n            placeholder=\"筛选状态\"\n            allowClear\n            style={{ width: 120 }}\n            onChange={handleStatusFilter}\n          >\n            <Option value=\"active\">活跃</Option>\n            <Option value=\"inactive\">停用</Option>\n            <Option value=\"discontinued\">停产</Option>\n          </Select>\n          <Select\n            placeholder=\"筛选品牌\"\n            allowClear\n            style={{ width: 120 }}\n            onChange={handleBrandFilter}\n          >\n            {brands.map((brand) => (\n              <Option key={brand} value={brand}>\n                {brand}\n              </Option>\n            ))}\n          </Select>\n          <Select\n            placeholder=\"筛选分类\"\n            allowClear\n            style={{ width: 120 }}\n            onChange={handleCategoryFilter}\n          >\n            {categories.map((category) => (\n              <Option key={category} value={category}>\n                {category}\n              </Option>\n            ))}\n          </Select>\n          <Button icon={<ReloadOutlined />} onClick={fetchData}>\n            刷新\n          </Button>\n        </div>\n\n        <Table\n          columns={columns}\n          dataSource={data}\n          rowKey=\"_id\"\n          loading={loading}\n          pagination={{\n            ...pagination,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setPagination((prev) => ({\n                ...prev,\n                current: page,\n                pageSize: pageSize || 10,\n              }));\n            },\n          }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default ProductList;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,GAAG,EACHC,KAAK,EACLC,MAAM,EACNC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,OAAO,QACF,MAAM;AACb,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAqC,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAM;EAAEC;AAAO,CAAC,GAAGjB,KAAK;AACxB,MAAM;EAAEkB;AAAO,CAAC,GAAGjB,MAAM;AAEzB,MAAMkB,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAG5B,QAAQ,CAAY,EAAE,CAAC;EAC/C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC;IAC3CiC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAqB,CAAC,CAAC,CAAC;EAC9D,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAW,EAAE,CAAC;EAClD,MAAM,CAACwC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAW,EAAE,CAAC;EAE1DD,SAAS,CAAC,MAAM;IACd2C,SAAS,CAAC,CAAC;IACXC,WAAW,CAAC,CAAC;IACbC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACb,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,EAAEE,OAAO,CAAC,CAAC;EAEtD,MAAMM,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMe,MAAM,GAAG;QACbC,IAAI,EAAEf,UAAU,CAACE,OAAO;QACxBc,KAAK,EAAEhB,UAAU,CAACG,QAAQ;QAC1B,GAAGE;MACL,CAAC;MACD,MAAMY,QAAQ,GAAG,MAAM7B,UAAU,CAAC8B,OAAO,CAACJ,MAAM,CAAC;MACjDjB,OAAO,CAACoB,QAAQ,CAACrB,IAAI,CAAC;MACtBK,aAAa,CAAEkB,IAAI,KAAM;QACvB,GAAGA,IAAI;QACPf,KAAK,EAAEa,QAAQ,CAACb;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRrB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B,IAAI;MACF,MAAMS,SAAS,GAAG,MAAMjC,UAAU,CAACkC,SAAS,CAAC,CAAC;MAC9Cd,SAAS,CAACa,SAAS,CAAC;IACtB,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMP,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMW,YAAY,GAAG,MAAMpC,UAAU,CAACqC,aAAa,CAAC,CAAC;MACrDf,aAAa,CAACc,YAAY,CAAC;IAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdG,OAAO,CAACH,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAMM,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMvC,UAAU,CAACwC,MAAM,CAACD,EAAE,CAAC;MAC3BnD,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;MACvBlB,SAAS,CAAC,CAAC;IACb,CAAC,CAAC,OAAOS,KAAK,EAAE;MACd5C,OAAO,CAAC4C,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMU,YAAY,GAAIC,KAAa,IAAK;IACtCzB,UAAU,CAAEa,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEa,MAAM,EAAED;IAAM,CAAC,CAAC,CAAC;IAClD9B,aAAa,CAAEkB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEjB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAM+B,kBAAkB,GAAIF,KAAa,IAAK;IAC5CzB,UAAU,CAAEa,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEe,MAAM,EAAEH,KAAK,IAAII;IAAU,CAAC,CAAC,CAAC;IAC/DlC,aAAa,CAAEkB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEjB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMkC,iBAAiB,GAAIL,KAAa,IAAK;IAC3CzB,UAAU,CAAEa,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEkB,KAAK,EAAEN,KAAK,IAAII;IAAU,CAAC,CAAC,CAAC;IAC9DlC,aAAa,CAAEkB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEjB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMoC,oBAAoB,GAAIP,KAAa,IAAK;IAC9CzB,UAAU,CAAEa,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEoB,QAAQ,EAAER,KAAK,IAAII;IAAU,CAAC,CAAC,CAAC;IACjElC,aAAa,CAAEkB,IAAI,KAAM;MAAE,GAAGA,IAAI;MAAEjB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;EACpD,CAAC;EAED,MAAMsC,YAAY,GAAIN,MAAc,IAAK;IACvC,MAAMO,SAAS,GAAG;MAChBC,MAAM,EAAE;QAAEC,KAAK,EAAE,OAAO;QAAEC,IAAI,EAAE;MAAK,CAAC;MACtCC,QAAQ,EAAE;QAAEF,KAAK,EAAE,QAAQ;QAAEC,IAAI,EAAE;MAAK,CAAC;MACzCE,YAAY,EAAE;QAAEH,KAAK,EAAE,KAAK;QAAEC,IAAI,EAAE;MAAK;IAC3C,CAAC;IACD,MAAMG,MAAM,GAAGN,SAAS,CAACP,MAAM,CAA2B;IAC1D,oBAAO5C,OAAA,CAACjB,GAAG;MAACsE,KAAK,EAAEI,MAAM,CAACJ,KAAM;MAAAK,QAAA,EAAED,MAAM,CAACH;IAAI;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EACtD,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IAEVC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAGC,aAAqB,iBAC5BtE,OAAA;MAAMuE,KAAK,EAAE;QAAEC,UAAU,EAAE,WAAW;QAAEC,UAAU,EAAE;MAAO,CAAE;MAAAf,QAAA,EAC1DY;IAAa;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAEV,CAAC,EACD;IACEE,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVO,QAAQ,EAAE;MACRC,SAAS,EAAE;IACb,CAAC;IACDN,MAAM,EAAGO,IAAY,iBACnB5E,OAAA,CAACX,OAAO;MAACwF,SAAS,EAAC,SAAS;MAACb,KAAK,EAAEY,IAAK;MAAAlB,QAAA,EACtCkB;IAAI;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAEb,CAAC,EACD;IACEE,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,gBAAgB;IAC3BC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGS,cAAsB,IAAK,GAAGA,cAAc,GAAG;IACxDC,MAAM,EAAE;EACV,CAAC,EACD;IACEf,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,eAAe;IAC1BC,GAAG,EAAE,eAAe;IACpBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGW,IAAY,IAAKnF,KAAK,CAACmF,IAAI,CAAC,CAACC,MAAM,CAAC,YAAY,CAAC;IAC1DF,MAAM,EAAE;EACV,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGtB,KAAa,IAAKA,KAAK,IAAI;EACtC,CAAC,EACD;IACEiB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGpB,QAAgB,IAAKA,QAAQ,IAAI;EAC5C,CAAC,EACD;IACEe,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGa,MAAc,IAAMA,MAAM,GAAG,GAAGA,MAAM,IAAI,GAAG;EACxD,CAAC,EACD;IACElB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,EAAE;IACTE,MAAM,EAAGzB,MAAc,IAAKM,YAAY,CAACN,MAAM;EACjD,CAAC,EACD;IACEoB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVE,MAAM,EAAGW,IAAY,IAAKnF,KAAK,CAACmF,IAAI,CAAC,CAACC,MAAM,CAAC,aAAa,CAAC;IAC3DF,MAAM,EAAE;EACV,CAAC,EACD;IACEf,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,KAAK,EAAE,OAAgB;IACvBC,MAAM,EAAEA,CAACc,CAAM,EAAEC,MAAe,kBAC9BpF,OAAA,CAAClB,KAAK;MAACuG,IAAI,EAAC,OAAO;MAAA3B,QAAA,gBACjB1D,OAAA,CAACnB,MAAM;QACLyG,IAAI,EAAC,MAAM;QACXC,IAAI,eAAEvF,OAAA,CAACN,WAAW;UAAAiE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtB0B,OAAO,EAAEA,CAAA,KAAMnF,QAAQ,CAAC,aAAa+E,MAAM,CAACK,GAAG,EAAE,CAAE;QAAA/B,QAAA,EACpD;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA,CAACnB,MAAM;QACLyG,IAAI,EAAC,MAAM;QACXC,IAAI,eAAEvF,OAAA,CAACR,YAAY;UAAAmE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvB0B,OAAO,EAAEA,CAAA,KAAMnF,QAAQ,CAAC,aAAa+E,MAAM,CAACK,GAAG,YAAY,CAAE;QAAA/B,QAAA,EAC9D;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT9D,OAAA,CAACb,UAAU;QACT6E,KAAK,EAAC,oEAAa;QACnB0B,SAAS,EAAEA,CAAA,KAAMtD,YAAY,CAACgD,MAAM,CAACK,GAAG,CAAE;QAC1CE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAlC,QAAA,eAEf1D,OAAA,CAACnB,MAAM;UAACyG,IAAI,EAAC,MAAM;UAACO,MAAM;UAACN,IAAI,eAAEvF,OAAA,CAACP,cAAc;YAAAkE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAAC;QAErD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE9D,OAAA;IAAA0D,QAAA,eACE1D,OAAA,CAACZ,IAAI;MAAAsE,QAAA,gBACH1D,OAAA;QACEuE,KAAK,EAAE;UACLuB,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,MAAM;UACfC,cAAc,EAAE,eAAe;UAC/BC,UAAU,EAAE;QACd,CAAE;QAAAvC,QAAA,gBAEF1D,OAAA;UAAIuE,KAAK,EAAE;YAAE2B,MAAM,EAAE;UAAE,CAAE;UAAAxC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnC9D,OAAA,CAACnB,MAAM;UACLyG,IAAI,EAAC,SAAS;UACdC,IAAI,eAAEvF,OAAA,CAACT,YAAY;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB0B,OAAO,EAAEA,CAAA,KAAMnF,QAAQ,CAAC,kBAAkB,CAAE;UAAAqD,QAAA,EAC7C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9D,OAAA;QACEuE,KAAK,EAAE;UACLuB,YAAY,EAAE,EAAE;UAChBC,OAAO,EAAE,MAAM;UACfI,GAAG,EAAE,EAAE;UACPC,QAAQ,EAAE;QACZ,CAAE;QAAA1C,QAAA,gBAEF1D,OAAA,CAACC,MAAM;UACLoG,WAAW,EAAC,gFAAe;UAC3BC,UAAU;UACV/B,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAI,CAAE;UACtBoC,QAAQ,EAAE/D,YAAa;UACvBgE,WAAW,eAAExG,OAAA,CAACL,cAAc;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eACF9D,OAAA,CAACf,MAAM;UACLoH,WAAW,EAAC,0BAAM;UAClBC,UAAU;UACV/B,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAI,CAAE;UACtBsC,QAAQ,EAAE9D,kBAAmB;UAAAe,QAAA,gBAE7B1D,OAAA,CAACE,MAAM;YAACuC,KAAK,EAAC,QAAQ;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAClC9D,OAAA,CAACE,MAAM;YAACuC,KAAK,EAAC,UAAU;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACpC9D,OAAA,CAACE,MAAM;YAACuC,KAAK,EAAC,cAAc;YAAAiB,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eACT9D,OAAA,CAACf,MAAM;UACLoH,WAAW,EAAC,0BAAM;UAClBC,UAAU;UACV/B,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAI,CAAE;UACtBsC,QAAQ,EAAE3D,iBAAkB;UAAAY,QAAA,EAE3BzC,MAAM,CAACyF,GAAG,CAAE3D,KAAK,iBAChB/C,OAAA,CAACE,MAAM;YAAauC,KAAK,EAAEM,KAAM;YAAAW,QAAA,EAC9BX;UAAK,GADKA,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT9D,OAAA,CAACf,MAAM;UACLoH,WAAW,EAAC,0BAAM;UAClBC,UAAU;UACV/B,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAI,CAAE;UACtBsC,QAAQ,EAAEzD,oBAAqB;UAAAU,QAAA,EAE9BvC,UAAU,CAACuF,GAAG,CAAEzD,QAAQ,iBACvBjD,OAAA,CAACE,MAAM;YAAgBuC,KAAK,EAAEQ,QAAS;YAAAS,QAAA,EACpCT;UAAQ,GADEA,QAAQ;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEb,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT9D,OAAA,CAACnB,MAAM;UAAC0G,IAAI,eAAEvF,OAAA,CAACJ,cAAc;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC0B,OAAO,EAAEnE,SAAU;UAAAqC,QAAA,EAAC;QAEtD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAEN9D,OAAA,CAACpB,KAAK;QACJmF,OAAO,EAAEA,OAAQ;QACjB4C,UAAU,EAAErG,IAAK;QACjBsG,MAAM,EAAC,KAAK;QACZpG,OAAO,EAAEA,OAAQ;QACjBE,UAAU,EAAE;UACV,GAAGA,UAAU;UACbmG,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACjG,KAAK,EAAEkG,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQlG,KAAK,IAAI;UAC5C2F,QAAQ,EAAEA,CAAChF,IAAI,EAAEZ,QAAQ,KAAK;YAC5BF,aAAa,CAAEkB,IAAI,KAAM;cACvB,GAAGA,IAAI;cACPjB,OAAO,EAAEa,IAAI;cACbZ,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC,CAAC;UACL;QACF;MAAE;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAxTID,WAAqB;EAAA,QACRb,WAAW;AAAA;AAAA2H,EAAA,GADxB9G,WAAqB;AA0T3B,eAAeA,WAAW;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}