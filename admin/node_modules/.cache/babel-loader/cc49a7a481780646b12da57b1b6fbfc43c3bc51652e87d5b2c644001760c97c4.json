{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/components/Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from \"react\";\nimport { Layout, Menu } from \"antd\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport { DashboardOutlined, QrcodeOutlined, PlusOutlined } from \"@ant-design/icons\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Sider\n} = Layout;\nconst Sidebar = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const menuItems = [{\n    key: \"/\",\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 13\n    }, this),\n    label: \"仪表盘\"\n  }, {\n    key: \"/qr-codes\",\n    icon: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 13\n    }, this),\n    label: \"二维码管理\"\n  }, {\n    key: \"/qr-codes/create\",\n    icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 13\n    }, this),\n    label: \"创建二维码\"\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    navigate(key);\n  };\n  return /*#__PURE__*/_jsxDEV(Sider, {\n    width: 200,\n    theme: \"dark\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: 64,\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        color: \"white\",\n        fontSize: \"18px\",\n        fontWeight: \"bold\"\n      },\n      children: \"\\u4E8C\\u7EF4\\u7801\\u7BA1\\u7406\\u5E73\\u53F0\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 42,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Menu, {\n      mode: \"inline\",\n      selectedKeys: [location.pathname],\n      items: menuItems,\n      onClick: handleMenuClick,\n      theme: \"dark\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 41,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"VDZHUspDq9N5O9RWjniBrjgIdAA=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Layout", "<PERSON><PERSON>", "useNavigate", "useLocation", "DashboardOutlined", "QrcodeOutlined", "PlusOutlined", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "Sidebar", "_s", "navigate", "location", "menuItems", "key", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "handleMenuClick", "width", "theme", "children", "style", "height", "display", "alignItems", "justifyContent", "color", "fontSize", "fontWeight", "mode", "<PERSON><PERSON><PERSON><PERSON>", "pathname", "items", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/components/Sidebar.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Layout, Menu } from \"antd\";\nimport { useNavigate, useLocation } from \"react-router-dom\";\nimport {\n  DashboardOutlined,\n  QrcodeOutlined,\n  PlusOutlined,\n  ShoppingOutlined,\n  AppstoreAddOutlined,\n} from \"@ant-design/icons\";\n\nconst { Sider } = Layout;\n\nconst Sidebar: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const menuItems = [\n    {\n      key: \"/\",\n      icon: <DashboardOutlined />,\n      label: \"仪表盘\",\n    },\n    {\n      key: \"/qr-codes\",\n      icon: <QrcodeOutlined />,\n      label: \"二维码管理\",\n    },\n    {\n      key: \"/qr-codes/create\",\n      icon: <PlusOutlined />,\n      label: \"创建二维码\",\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    navigate(key);\n  };\n\n  return (\n    <Sider width={200} theme=\"dark\">\n      <div\n        style={{\n          height: 64,\n          display: \"flex\",\n          alignItems: \"center\",\n          justifyContent: \"center\",\n          color: \"white\",\n          fontSize: \"18px\",\n          fontWeight: \"bold\",\n        }}\n      >\n        二维码管理平台\n      </div>\n      <Menu\n        mode=\"inline\"\n        selectedKeys={[location.pathname]}\n        items={menuItems}\n        onClick={handleMenuClick}\n        theme=\"dark\"\n      />\n    </Sider>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,IAAI,QAAQ,MAAM;AACnC,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SACEC,iBAAiB,EACjBC,cAAc,EACdC,YAAY,QAGP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC;AAAM,CAAC,GAAGT,MAAM;AAExB,MAAMU,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAMC,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC9B,MAAMW,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAE9B,MAAMW,SAAS,GAAG,CAChB;IACEC,GAAG,EAAE,GAAG;IACRC,IAAI,eAAER,OAAA,CAACJ,iBAAiB;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,WAAW;IAChBC,IAAI,eAAER,OAAA,CAACH,cAAc;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,KAAK,EAAE;EACT,CAAC,EACD;IACEN,GAAG,EAAE,kBAAkB;IACvBC,IAAI,eAAER,OAAA,CAACF,YAAY;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,KAAK,EAAE;EACT,CAAC,CACF;EAED,MAAMC,eAAe,GAAGA,CAAC;IAAEP;EAAqB,CAAC,KAAK;IACpDH,QAAQ,CAACG,GAAG,CAAC;EACf,CAAC;EAED,oBACEP,OAAA,CAACC,KAAK;IAACc,KAAK,EAAE,GAAI;IAACC,KAAK,EAAC,MAAM;IAAAC,QAAA,gBAC7BjB,OAAA;MACEkB,KAAK,EAAE;QACLC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE,OAAO;QACdC,QAAQ,EAAE,MAAM;QAChBC,UAAU,EAAE;MACd,CAAE;MAAAR,QAAA,EACH;IAED;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACNZ,OAAA,CAACP,IAAI;MACHiC,IAAI,EAAC,QAAQ;MACbC,YAAY,EAAE,CAACtB,QAAQ,CAACuB,QAAQ,CAAE;MAClCC,KAAK,EAAEvB,SAAU;MACjBwB,OAAO,EAAEhB,eAAgB;MACzBE,KAAK,EAAC;IAAM;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEZ,CAAC;AAACT,EAAA,CAlDID,OAAiB;EAAA,QACJR,WAAW,EACXC,WAAW;AAAA;AAAAoC,EAAA,GAFxB7B,OAAiB;AAoDvB,eAAeA,OAAO;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}