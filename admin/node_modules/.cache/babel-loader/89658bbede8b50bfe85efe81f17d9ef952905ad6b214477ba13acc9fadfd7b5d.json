{"ast": null, "code": "import { unit } from '@ant-design/cssinjs';\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    tableExpandColumnWidth,\n    calc\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-cell,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${unit(paddingVertical)} ${unit(paddingHorizontal)}`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: unit(calc(paddingHorizontal).div(2).mul(-1).equal())\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `${unit(calc(paddingVertical).mul(-1).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: unit(calc(paddingVertical).mul(-1).equal()),\n          marginInline: `${unit(calc(tableExpandColumnWidth).sub(paddingHorizontal).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-extra`]: {\n        paddingInlineStart: unit(calc(paddingHorizontal).div(4).equal())\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;", "map": {"version": 3, "names": ["unit", "genSizeStyle", "token", "componentCls", "tableExpandColumnWidth", "calc", "getSizeStyle", "size", "paddingVertical", "paddingHorizontal", "fontSize", "padding", "marginInlineEnd", "div", "mul", "equal", "margin", "marginBlock", "marginInline", "sub", "paddingInlineStart", "Object", "assign", "tablePaddingVerticalMiddle", "tablePaddingHorizontalMiddle", "tableFontSizeMiddle", "tablePaddingVerticalSmall", "tablePaddingHorizontalSmall", "tableFontSizeSmall"], "sources": ["/Users/<USER>/github/7/admin/node_modules/antd/es/table/style/size.js"], "sourcesContent": ["import { unit } from '@ant-design/cssinjs';\nconst genSizeStyle = token => {\n  const {\n    componentCls,\n    tableExpandColumnWidth,\n    calc\n  } = token;\n  const getSizeStyle = (size, paddingVertical, paddingHorizontal, fontSize) => ({\n    [`${componentCls}${componentCls}-${size}`]: {\n      fontSize,\n      [`\n        ${componentCls}-title,\n        ${componentCls}-footer,\n        ${componentCls}-cell,\n        ${componentCls}-thead > tr > th,\n        ${componentCls}-tbody > tr > th,\n        ${componentCls}-tbody > tr > td,\n        tfoot > tr > th,\n        tfoot > tr > td\n      `]: {\n        padding: `${unit(paddingVertical)} ${unit(paddingHorizontal)}`\n      },\n      [`${componentCls}-filter-trigger`]: {\n        marginInlineEnd: unit(calc(paddingHorizontal).div(2).mul(-1).equal())\n      },\n      [`${componentCls}-expanded-row-fixed`]: {\n        margin: `${unit(calc(paddingVertical).mul(-1).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n      },\n      [`${componentCls}-tbody`]: {\n        // ========================= Nest Table ===========================\n        [`${componentCls}-wrapper:only-child ${componentCls}`]: {\n          marginBlock: unit(calc(paddingVertical).mul(-1).equal()),\n          marginInline: `${unit(calc(tableExpandColumnWidth).sub(paddingHorizontal).equal())} ${unit(calc(paddingHorizontal).mul(-1).equal())}`\n        }\n      },\n      // https://github.com/ant-design/ant-design/issues/35167\n      [`${componentCls}-selection-extra`]: {\n        paddingInlineStart: unit(calc(paddingHorizontal).div(4).equal())\n      }\n    }\n  });\n  return {\n    [`${componentCls}-wrapper`]: Object.assign(Object.assign({}, getSizeStyle('middle', token.tablePaddingVerticalMiddle, token.tablePaddingHorizontalMiddle, token.tableFontSizeMiddle)), getSizeStyle('small', token.tablePaddingVerticalSmall, token.tablePaddingHorizontalSmall, token.tableFontSizeSmall))\n  };\n};\nexport default genSizeStyle;"], "mappings": "AAAA,SAASA,IAAI,QAAQ,qBAAqB;AAC1C,MAAMC,YAAY,GAAGC,KAAK,IAAI;EAC5B,MAAM;IACJC,YAAY;IACZC,sBAAsB;IACtBC;EACF,CAAC,GAAGH,KAAK;EACT,MAAMI,YAAY,GAAGA,CAACC,IAAI,EAAEC,eAAe,EAAEC,iBAAiB,EAAEC,QAAQ,MAAM;IAC5E,CAAC,GAAGP,YAAY,GAAGA,YAAY,IAAII,IAAI,EAAE,GAAG;MAC1CG,QAAQ;MACR,CAAC;AACP,UAAUP,YAAY;AACtB,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB,UAAUA,YAAY;AACtB;AACA;AACA,OAAO,GAAG;QACFQ,OAAO,EAAE,GAAGX,IAAI,CAACQ,eAAe,CAAC,IAAIR,IAAI,CAACS,iBAAiB,CAAC;MAC9D,CAAC;MACD,CAAC,GAAGN,YAAY,iBAAiB,GAAG;QAClCS,eAAe,EAAEZ,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;MACtE,CAAC;MACD,CAAC,GAAGZ,YAAY,qBAAqB,GAAG;QACtCa,MAAM,EAAE,GAAGhB,IAAI,CAACK,IAAI,CAACG,eAAe,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC,IAAIf,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;MACzG,CAAC;MACD,CAAC,GAAGZ,YAAY,QAAQ,GAAG;QACzB;QACA,CAAC,GAAGA,YAAY,uBAAuBA,YAAY,EAAE,GAAG;UACtDc,WAAW,EAAEjB,IAAI,CAACK,IAAI,CAACG,eAAe,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;UACxDG,YAAY,EAAE,GAAGlB,IAAI,CAACK,IAAI,CAACD,sBAAsB,CAAC,CAACe,GAAG,CAACV,iBAAiB,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,IAAIf,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACK,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC,CAAC;QACrI;MACF,CAAC;MACD;MACA,CAAC,GAAGZ,YAAY,kBAAkB,GAAG;QACnCiB,kBAAkB,EAAEpB,IAAI,CAACK,IAAI,CAACI,iBAAiB,CAAC,CAACI,GAAG,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,CAAC;MACjE;IACF;EACF,CAAC,CAAC;EACF,OAAO;IACL,CAAC,GAAGZ,YAAY,UAAU,GAAGkB,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhB,YAAY,CAAC,QAAQ,EAAEJ,KAAK,CAACqB,0BAA0B,EAAErB,KAAK,CAACsB,4BAA4B,EAAEtB,KAAK,CAACuB,mBAAmB,CAAC,CAAC,EAAEnB,YAAY,CAAC,OAAO,EAAEJ,KAAK,CAACwB,yBAAyB,EAAExB,KAAK,CAACyB,2BAA2B,EAAEzB,KAAK,CAAC0B,kBAAkB,CAAC;EAC5S,CAAC;AACH,CAAC;AACD,eAAe3B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}