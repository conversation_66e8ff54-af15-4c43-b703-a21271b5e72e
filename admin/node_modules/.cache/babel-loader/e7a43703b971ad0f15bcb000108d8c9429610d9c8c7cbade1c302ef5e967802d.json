{"ast": null, "code": "\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport Input from '../../input/Input';\nimport { toHexFormat } from '../color';\nimport { generateColor } from '../util';\nconst hexReg = /(^#[\\da-f]{6}$)|(^#[\\da-f]{8}$)/i;\nconst isHexString = hex => hexReg.test(`#${hex}`);\nconst ColorHexInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHexInputPrefixCls = `${prefixCls}-hex-input`;\n  const [hexValue, setHexValue] = useState(() => value ? toHexFormat(value.toHexString()) : undefined);\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setHexValue(toHexFormat(value.toHexString()));\n    }\n  }, [value]);\n  const handleHexChange = e => {\n    const originValue = e.target.value;\n    setHexValue(toHexFormat(originValue));\n    if (isHexString(toHexFormat(originValue, true))) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(generateColor(originValue));\n    }\n  };\n  return /*#__PURE__*/React.createElement(Input, {\n    className: colorHexInputPrefixCls,\n    value: hexValue,\n    prefix: \"#\",\n    onChange: handleHexChange,\n    size: \"small\"\n  });\n};\nexport default ColorHexInput;", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Input", "toHexFormat", "generateColor", "hexReg", "isHexString", "hex", "test", "ColorHexInput", "prefixCls", "value", "onChange", "colorHexInputPrefixCls", "hexValue", "setHexValue", "toHexString", "undefined", "handleHexChange", "e", "originValue", "target", "createElement", "className", "prefix", "size"], "sources": ["/Users/<USER>/github/7/admin/node_modules/antd/es/color-picker/components/ColorHexInput.js"], "sourcesContent": ["\"use client\";\n\nimport React, { useEffect, useState } from 'react';\nimport Input from '../../input/Input';\nimport { toHexFormat } from '../color';\nimport { generateColor } from '../util';\nconst hexReg = /(^#[\\da-f]{6}$)|(^#[\\da-f]{8}$)/i;\nconst isHexString = hex => hexReg.test(`#${hex}`);\nconst ColorHexInput = ({\n  prefixCls,\n  value,\n  onChange\n}) => {\n  const colorHexInputPrefixCls = `${prefixCls}-hex-input`;\n  const [hexValue, setHexValue] = useState(() => value ? toHexFormat(value.toHexString()) : undefined);\n  // Update step value\n  useEffect(() => {\n    if (value) {\n      setHexValue(toHexFormat(value.toHexString()));\n    }\n  }, [value]);\n  const handleHexChange = e => {\n    const originValue = e.target.value;\n    setHexValue(toHexFormat(originValue));\n    if (isHexString(toHexFormat(originValue, true))) {\n      onChange === null || onChange === void 0 ? void 0 : onChange(generateColor(originValue));\n    }\n  };\n  return /*#__PURE__*/React.createElement(Input, {\n    className: colorHexInputPrefixCls,\n    value: hexValue,\n    prefix: \"#\",\n    onChange: handleHexChange,\n    size: \"small\"\n  });\n};\nexport default ColorHexInput;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,mBAAmB;AACrC,SAASC,WAAW,QAAQ,UAAU;AACtC,SAASC,aAAa,QAAQ,SAAS;AACvC,MAAMC,MAAM,GAAG,kCAAkC;AACjD,MAAMC,WAAW,GAAGC,GAAG,IAAIF,MAAM,CAACG,IAAI,CAAC,IAAID,GAAG,EAAE,CAAC;AACjD,MAAME,aAAa,GAAGA,CAAC;EACrBC,SAAS;EACTC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,MAAMC,sBAAsB,GAAG,GAAGH,SAAS,YAAY;EACvD,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,MAAMU,KAAK,GAAGR,WAAW,CAACQ,KAAK,CAACK,WAAW,CAAC,CAAC,CAAC,GAAGC,SAAS,CAAC;EACpG;EACAjB,SAAS,CAAC,MAAM;IACd,IAAIW,KAAK,EAAE;MACTI,WAAW,CAACZ,WAAW,CAACQ,KAAK,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACL,KAAK,CAAC,CAAC;EACX,MAAMO,eAAe,GAAGC,CAAC,IAAI;IAC3B,MAAMC,WAAW,GAAGD,CAAC,CAACE,MAAM,CAACV,KAAK;IAClCI,WAAW,CAACZ,WAAW,CAACiB,WAAW,CAAC,CAAC;IACrC,IAAId,WAAW,CAACH,WAAW,CAACiB,WAAW,EAAE,IAAI,CAAC,CAAC,EAAE;MAC/CR,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACR,aAAa,CAACgB,WAAW,CAAC,CAAC;IAC1F;EACF,CAAC;EACD,OAAO,aAAarB,KAAK,CAACuB,aAAa,CAACpB,KAAK,EAAE;IAC7CqB,SAAS,EAAEV,sBAAsB;IACjCF,KAAK,EAAEG,QAAQ;IACfU,MAAM,EAAE,GAAG;IACXZ,QAAQ,EAAEM,eAAe;IACzBO,IAAI,EAAE;EACR,CAAC,CAAC;AACJ,CAAC;AACD,eAAehB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}