{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authApi } from '../services/api';\nimport { message } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // 初始化时检查本地存储的认证信息\n    const savedToken = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n    if (savedToken && savedUser) {\n      setToken(savedToken);\n      try {\n        setUser(JSON.parse(savedUser));\n      } catch (error) {\n        console.error('解析用户信息失败:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n  const login = async data => {\n    try {\n      setLoading(true);\n      const response = await authApi.login(data);\n\n      // 保存认证信息\n      setToken(response.access_token);\n      setUser(response.user);\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      message.success('登录成功');\n      return true;\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || '登录失败';\n      message.error(errorMessage);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    message.success('已退出登录');\n  };\n  const isAuthenticated = !!token && !!user;\n  const value = {\n    user,\n    token,\n    login,\n    logout,\n    loading,\n    isAuthenticated\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 92,\n    columnNumber: 10\n  }, this);\n};\n_s2(AuthProvider, \"uAkFQMmIUxfxJcQTEb8tCM/KFt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authApi", "message", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "savedToken", "localStorage", "getItem", "savedUser", "JSON", "parse", "error", "console", "removeItem", "login", "data", "response", "access_token", "setItem", "stringify", "success", "_error$response", "_error$response$data", "errorMessage", "logout", "isAuthenticated", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { authApi, LoginData, UserProfile } from '../services/api';\nimport { message } from 'antd';\n\ninterface AuthContextType {\n  user: UserProfile | null;\n  token: string | null;\n  login: (data: LoginData) => Promise<boolean>;\n  logout: () => void;\n  loading: boolean;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<UserProfile | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // 初始化时检查本地存储的认证信息\n    const savedToken = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n\n    if (savedToken && savedUser) {\n      setToken(savedToken);\n      try {\n        setUser(JSON.parse(savedUser));\n      } catch (error) {\n        console.error('解析用户信息失败:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (data: LoginData): Promise<boolean> => {\n    try {\n      setLoading(true);\n      const response = await authApi.login(data);\n      \n      // 保存认证信息\n      setToken(response.access_token);\n      setUser(response.user as UserProfile);\n      localStorage.setItem('token', response.access_token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      message.success('登录成功');\n      return true;\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || '登录失败';\n      message.error(errorMessage);\n      return false;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    message.success('已退出登录');\n  };\n\n  const isAuthenticated = !!token && !!user;\n\n  const value: AuthContextType = {\n    user,\n    token,\n    login,\n    logout,\n    loading,\n    isAuthenticated,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AACxF,SAASC,OAAO,QAAgC,iBAAiB;AACjE,SAASC,OAAO,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW/B,MAAMC,WAAW,gBAAGR,aAAa,CAA8BS,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGX,UAAU,CAACO,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhB,QAAQ,CAAqB,IAAI,CAAC;EAC1D,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMoB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE9C,IAAIF,UAAU,IAAIG,SAAS,EAAE;MAC3BN,QAAQ,CAACG,UAAU,CAAC;MACpB,IAAI;QACFL,OAAO,CAACS,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC,CAAC;MAChC,CAAC,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjCL,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;MACjC;IACF;IACAT,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMU,KAAK,GAAG,MAAOC,IAAe,IAAuB;IACzD,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,QAAQ,GAAG,MAAM9B,OAAO,CAAC4B,KAAK,CAACC,IAAI,CAAC;;MAE1C;MACAb,QAAQ,CAACc,QAAQ,CAACC,YAAY,CAAC;MAC/BjB,OAAO,CAACgB,QAAQ,CAACjB,IAAmB,CAAC;MACrCO,YAAY,CAACY,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAACC,YAAY,CAAC;MACpDX,YAAY,CAACY,OAAO,CAAC,MAAM,EAAET,IAAI,CAACU,SAAS,CAACH,QAAQ,CAACjB,IAAI,CAAC,CAAC;MAE3DZ,OAAO,CAACiC,OAAO,CAAC,MAAM,CAAC;MACvB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOT,KAAU,EAAE;MAAA,IAAAU,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAV,KAAK,CAACK,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBN,IAAI,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsBnC,OAAO,KAAI,MAAM;MAC5DA,OAAO,CAACwB,KAAK,CAACY,YAAY,CAAC;MAC3B,OAAO,KAAK;IACd,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMoB,MAAM,GAAGA,CAAA,KAAM;IACnBxB,OAAO,CAAC,IAAI,CAAC;IACbE,QAAQ,CAAC,IAAI,CAAC;IACdI,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;IAChCP,YAAY,CAACO,UAAU,CAAC,MAAM,CAAC;IAC/B1B,OAAO,CAACiC,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;EAED,MAAMK,eAAe,GAAG,CAAC,CAACxB,KAAK,IAAI,CAAC,CAACF,IAAI;EAEzC,MAAM2B,KAAsB,GAAG;IAC7B3B,IAAI;IACJE,KAAK;IACLa,KAAK;IACLU,MAAM;IACNrB,OAAO;IACPsB;EACF,CAAC;EAED,oBAAOpC,OAAA,CAACC,WAAW,CAACqC,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAA7B,QAAA,EAAEA;EAAQ;IAAA+B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAuB,CAAC;AAC9E,CAAC;AAACjC,GAAA,CAjEWF,YAAyC;AAAAoC,EAAA,GAAzCpC,YAAyC;AAAA,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}