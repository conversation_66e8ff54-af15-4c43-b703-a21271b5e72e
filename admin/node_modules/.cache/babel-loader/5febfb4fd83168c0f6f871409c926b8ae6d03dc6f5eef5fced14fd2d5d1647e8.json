{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar ENUM = 'enum';\nvar enumerable = function enumerable(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default enumerable;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "ENUM", "enumerable", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field", "undefined"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@rc-component/async-validator/es/validator/enum.js"], "sourcesContent": ["import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar ENUM = 'enum';\nvar enumerable = function enumerable(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n    if (value !== undefined) {\n      rules[ENUM](rule, value, source, errors, options);\n    }\n  }\n  callback(errors);\n};\nexport default enumerable;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,SAAS;AACtC,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC3E,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIT,YAAY,CAACI,KAAK,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACzC,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAN,KAAK,CAACW,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACpD,IAAIH,KAAK,KAAKS,SAAS,EAAE;MACvBd,KAAK,CAACE,IAAI,CAAC,CAACE,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;IACnD;EACF;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}