{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/App.tsx\";\nimport React from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\nimport { Layout } from \"antd\";\nimport Sidebar from \"./components/Sidebar\";\nimport Header from \"./components/Header\";\nimport Dashboard from \"./pages/Dashboard\";\nimport ProductList from \"./pages/ProductList\";\nimport ProductDetail from \"./pages/ProductDetail\";\nimport CreateProduct from \"./pages/CreateProduct\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Content\n} = Layout;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: \"24px 16px\",\n          padding: 24,\n          background: \"#fff\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/qr-codes\",\n            element: /*#__PURE__*/_jsxDEV(QrCodeList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/qr-codes/create\",\n            element: /*#__PURE__*/_jsxDEV(CreateQrCode, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/qr-codes/:id\",\n            element: /*#__PURE__*/_jsxDEV(QrCodeDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/products\",\n            element: /*#__PURE__*/_jsxDEV(ProductList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/products/create\",\n            element: /*#__PURE__*/_jsxDEV(CreateProduct, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 32,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/products/:id\",\n            element: /*#__PURE__*/_jsxDEV(ProductDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Layout", "Sidebar", "Header", "Dashboard", "ProductList", "ProductDetail", "CreateProduct", "jsxDEV", "_jsxDEV", "Content", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "margin", "padding", "background", "path", "element", "QrCodeList", "CreateQrCode", "QrCodeDetail", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/App.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\nimport { Layout } from \"antd\";\nimport { AuthProvider } from \"./contexts/AuthContext\";\nimport ProtectedRoute from \"./components/ProtectedRoute\";\nimport Sidebar from \"./components/Sidebar\";\nimport Header from \"./components/Header\";\nimport Login from \"./pages/Login\";\nimport Dashboard from \"./pages/Dashboard\";\nimport ProductList from \"./pages/ProductList\";\nimport ProductDetail from \"./pages/ProductDetail\";\nimport CreateProduct from \"./pages/CreateProduct\";\nimport ChangePassword from \"./pages/ChangePassword\";\n\nconst { Content } = Layout;\n\nfunction App() {\n  return (\n    <Layout>\n      <Sidebar />\n      <Layout>\n        <Header />\n        <Content\n          style={{ margin: \"24px 16px\", padding: 24, background: \"#fff\" }}\n        >\n          <Routes>\n            <Route path=\"/\" element={<Dashboard />} />\n            <Route path=\"/qr-codes\" element={<QrCodeList />} />\n            <Route path=\"/qr-codes/create\" element={<CreateQrCode />} />\n            <Route path=\"/qr-codes/:id\" element={<QrCodeDetail />} />\n            <Route path=\"/products\" element={<ProductList />} />\n            <Route path=\"/products/create\" element={<CreateProduct />} />\n            <Route path=\"/products/:id\" element={<ProductDetail />} />\n          </Routes>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,MAAM;AAG7B,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AAExC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,aAAa,MAAM,uBAAuB;AACjD,OAAOC,aAAa,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAGlD,MAAM;EAAEC;AAAQ,CAAC,GAAGT,MAAM;AAE1B,SAASU,GAAGA,CAAA,EAAG;EACb,oBACEF,OAAA,CAACR,MAAM;IAAAW,QAAA,gBACLH,OAAA,CAACP,OAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXP,OAAA,CAACR,MAAM;MAAAW,QAAA,gBACLH,OAAA,CAACN,MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA,CAACC,OAAO;QACNO,KAAK,EAAE;UAAEC,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAR,QAAA,eAEhEH,OAAA,CAACV,MAAM;UAAAa,QAAA,gBACLH,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEb,OAAA,CAACL,SAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEb,OAAA,CAACc,UAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEb,OAAA,CAACe,YAAY;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEb,OAAA,CAACgB,YAAY;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACzDP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEb,OAAA,CAACJ,WAAW;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACpDP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEb,OAAA,CAACF,aAAa;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7DP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEb,OAAA,CAACH,aAAa;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACU,EAAA,GAtBQf,GAAG;AAwBZ,eAAeA,GAAG;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}