{"ast": null, "code": "var getCompVarPrefix = function getCompVarPrefix(component, prefix) {\n  return \"\".concat([prefix, component.replace(/([A-Z]+)([A-Z][a-z]+)/g, '$1-$2').replace(/([a-z])([A-Z])/g, '$1-$2')].filter(Boolean).join('-'));\n};\nexport default getCompVarPrefix;", "map": {"version": 3, "names": ["getCompVarPrefix", "component", "prefix", "concat", "replace", "filter", "Boolean", "join"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@ant-design/cssinjs-utils/es/util/getCompVarPrefix.js"], "sourcesContent": ["var getCompVarPrefix = function getCompVarPrefix(component, prefix) {\n  return \"\".concat([prefix, component.replace(/([A-Z]+)([A-Z][a-z]+)/g, '$1-$2').replace(/([a-z])([A-Z])/g, '$1-$2')].filter(Boolean).join('-'));\n};\nexport default getCompVarPrefix;"], "mappings": "AAAA,IAAIA,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,SAAS,EAAEC,MAAM,EAAE;EAClE,OAAO,EAAE,CAACC,MAAM,CAAC,CAACD,MAAM,EAAED,SAAS,CAACG,OAAO,CAAC,wBAAwB,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChJ,CAAC;AACD,eAAeP,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}