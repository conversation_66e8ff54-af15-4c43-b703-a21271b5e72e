{"ast": null, "code": "import * as React from 'react';\nimport Slider from \"../components/Slider\";\nexport default function useComponent(components) {\n  return React.useMemo(function () {\n    var _ref = components || {},\n      slider = _ref.slider;\n    return [slider || Slider];\n  }, [components]);\n}", "map": {"version": 3, "names": ["React", "Slide<PERSON>", "useComponent", "components", "useMemo", "_ref", "slider"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@rc-component/color-picker/es/hooks/useComponent.js"], "sourcesContent": ["import * as React from 'react';\nimport Slider from \"../components/Slider\";\nexport default function useComponent(components) {\n  return React.useMemo(function () {\n    var _ref = components || {},\n      slider = _ref.slider;\n    return [slider || Slider];\n  }, [components]);\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;AACzC,eAAe,SAASC,YAAYA,CAACC,UAAU,EAAE;EAC/C,OAAOH,KAAK,CAACI,OAAO,CAAC,YAAY;IAC/B,IAAIC,IAAI,GAAGF,UAAU,IAAI,CAAC,CAAC;MACzBG,MAAM,GAAGD,IAAI,CAACC,MAAM;IACtB,OAAO,CAACA,MAAM,IAAIL,MAAM,CAAC;EAC3B,CAAC,EAAE,CAACE,UAAU,CAAC,CAAC;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}