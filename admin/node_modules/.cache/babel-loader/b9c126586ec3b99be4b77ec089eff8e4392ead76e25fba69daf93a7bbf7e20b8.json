{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Form, Input, Button, Card, Select, DatePicker, message, Space, Divider, InputNumber, Row, Col } from \"antd\";\nimport { useNavigate } from \"react-router-dom\";\nimport { ArrowLeftOutlined, SaveOutlined } from \"@ant-design/icons\";\nimport dayjs from \"dayjs\";\nimport { productApi } from \"../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\nconst {\n  Option\n} = Select;\nconst CreateProduct = () => {\n  _s();\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const handleSubmit = async values => {\n    try {\n      setLoading(true);\n      const data = {\n        name: values.name,\n        alcoholContent: values.alcoholContent,\n        packagingDate: values.packagingDate.toISOString(),\n        description: values.description,\n        status: values.status || \"active\",\n        brand: values.brand,\n        category: values.category,\n        volume: values.volume,\n        batchNumber: values.batchNumber,\n        productionLocation: values.productionLocation\n      };\n      await productApi.create(data);\n      message.success(\"商品创建成功\");\n      navigate(\"/products\");\n    } catch (error) {\n      message.error(\"创建失败，请重试\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleBack = () => {\n    navigate(\"/products\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 24\n        },\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ArrowLeftOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 27\n            }, this),\n            onClick: handleBack,\n            children: \"\\u8FD4\\u56DE\\u5217\\u8868\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Divider, {\n            type: \"vertical\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            style: {\n              margin: 0\n            },\n            children: \"\\u521B\\u5EFA\\u5546\\u54C1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        initialValues: {\n          status: \"active\",\n          alcoholContent: 0\n        },\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              name: \"name\",\n              rules: [{\n                required: true,\n                message: \"请输入商品名称\"\n              }, {\n                max: 100,\n                message: \"名称不能超过100个字符\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\\uFF0C\\u5982\\uFF1A\\u8305\\u53F0\\u9152\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u9152\\u7CBE\\u5EA6 (%)\",\n              name: \"alcoholContent\",\n              rules: [{\n                required: true,\n                message: \"请输入酒精度\"\n              }, {\n                type: \"number\",\n                min: 0,\n                max: 100,\n                message: \"酒精度应在0-100之间\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: \"100%\"\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u9152\\u7CBE\\u5EA6\",\n                min: 0,\n                max: 100,\n                precision: 1,\n                addonAfter: \"%\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5305\\u88C5\\u65E5\\u671F\",\n              name: \"packagingDate\",\n              rules: [{\n                required: true,\n                message: \"请选择包装日期\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(DatePicker, {\n                style: {\n                  width: \"100%\"\n                },\n                placeholder: \"\\u9009\\u62E9\\u5305\\u88C5\\u65E5\\u671F\",\n                disabledDate: current => current && current > dayjs().endOf(\"day\")\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 123,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u72B6\\u6001\",\n              name: \"status\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: \"active\",\n                  children: \"\\u6D3B\\u8DC3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 136,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"inactive\",\n                  children: \"\\u505C\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 137,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: \"discontinued\",\n                  children: \"\\u505C\\u4EA7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 133,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 116,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u54C1\\u724C\",\n              name: \"brand\",\n              rules: [{\n                max: 50,\n                message: \"品牌名称不能超过50个字符\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u54C1\\u724C\\u540D\\u79F0\\uFF0C\\u5982\\uFF1A\\u8305\\u53F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5206\\u7C7B\",\n              name: \"category\",\n              rules: [{\n                max: 50,\n                message: \"分类名称不能超过50个字符\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5206\\u7C7B\\uFF0C\\u5982\\uFF1A\\u767D\\u9152\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 24,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5BB9\\u91CF (ml)\",\n              name: \"volume\",\n              rules: [{\n                type: \"number\",\n                min: 1,\n                message: \"容量必须大于0\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: \"100%\"\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BB9\\u91CF\",\n                min: 1,\n                addonAfter: \"ml\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            md: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u6279\\u6B21\\u53F7\",\n              name: \"batchNumber\",\n              rules: [{\n                max: 50,\n                message: \"批次号不能超过50个字符\"\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u6279\\u6B21\\u53F7\\uFF0C\\u5982\\uFF1AB20240115001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 190,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u751F\\u4EA7\\u5730\",\n          name: \"productionLocation\",\n          rules: [{\n            max: 100,\n            message: \"生产地不能超过100个字符\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u751F\\u4EA7\\u5730\\uFF0C\\u5982\\uFF1A\\u8D35\\u5DDE\\u8305\\u53F0\\u9547\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          name: \"description\",\n          rules: [{\n            max: 500,\n            message: \"描述不能超过500个字符\"\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            rows: 4,\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u63CF\\u8FF0\\uFF08\\u53EF\\u9009\\uFF09\",\n            showCount: true,\n            maxLength: 500\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 23\n              }, this),\n              children: \"\\u521B\\u5EFA\\u5546\\u54C1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: handleBack,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 60,\n    columnNumber: 5\n  }, this);\n};\n_s(CreateProduct, \"5WySIf5f8mC8ILhE7he/R1PA7bA=\", false, function () {\n  return [useNavigate, Form.useForm];\n});\n_c = CreateProduct;\nexport default CreateProduct;\nvar _c;\n$RefreshReg$(_c, \"CreateProduct\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Select", "DatePicker", "message", "Space", "Divider", "InputNumber", "Row", "Col", "useNavigate", "ArrowLeftOutlined", "SaveOutlined", "dayjs", "productApi", "jsxDEV", "_jsxDEV", "TextArea", "Option", "CreateProduct", "_s", "navigate", "form", "useForm", "loading", "setLoading", "handleSubmit", "values", "data", "name", "alcoholContent", "packagingDate", "toISOString", "description", "status", "brand", "category", "volume", "batchNumber", "productionLocation", "create", "success", "error", "handleBack", "children", "style", "marginBottom", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "type", "margin", "layout", "onFinish", "initialValues", "gutter", "xs", "md", "<PERSON><PERSON>", "label", "rules", "required", "max", "placeholder", "min", "width", "precision", "addonAfter", "disabledDate", "current", "endOf", "value", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/pages/CreateProduct.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport {\n  Form,\n  Input,\n  Button,\n  Card,\n  Select,\n  DatePicker,\n  message,\n  Space,\n  Divider,\n  InputNumber,\n  Row,\n  Col,\n} from \"antd\";\nimport { useNavigate } from \"react-router-dom\";\nimport { ArrowLeftOutlined, SaveOutlined } from \"@ant-design/icons\";\nimport dayjs from \"dayjs\";\nimport { productApi, CreateProductData } from \"../services/api\";\n\nconst { TextArea } = Input;\nconst { Option } = Select;\n\nconst CreateProduct: React.FC = () => {\n  const navigate = useNavigate();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n\n  const handleSubmit = async (values: any) => {\n    try {\n      setLoading(true);\n      const data: CreateProductData = {\n        name: values.name,\n        alcoholContent: values.alcoholContent,\n        packagingDate: values.packagingDate.toISOString(),\n        description: values.description,\n        status: values.status || \"active\",\n        brand: values.brand,\n        category: values.category,\n        volume: values.volume,\n        batchNumber: values.batchNumber,\n        productionLocation: values.productionLocation,\n      };\n\n      await productApi.create(data);\n      message.success(\"商品创建成功\");\n      navigate(\"/products\");\n    } catch (error) {\n      message.error(\"创建失败，请重试\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    navigate(\"/products\");\n  };\n\n  return (\n    <div>\n      <Card>\n        <div style={{ marginBottom: 24 }}>\n          <Space>\n            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>\n              返回列表\n            </Button>\n            <Divider type=\"vertical\" />\n            <h1 style={{ margin: 0 }}>创建商品</h1>\n          </Space>\n        </div>\n\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n          initialValues={{\n            status: \"active\",\n            alcoholContent: 0,\n          }}\n        >\n          <Row gutter={24}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"商品名称\"\n                name=\"name\"\n                rules={[\n                  { required: true, message: \"请输入商品名称\" },\n                  { max: 100, message: \"名称不能超过100个字符\" },\n                ]}\n              >\n                <Input placeholder=\"请输入商品名称，如：茅台酒\" />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"酒精度 (%)\"\n                name=\"alcoholContent\"\n                rules={[\n                  { required: true, message: \"请输入酒精度\" },\n                  { type: \"number\", min: 0, max: 100, message: \"酒精度应在0-100之间\" },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: \"100%\" }}\n                  placeholder=\"请输入酒精度\"\n                  min={0}\n                  max={100}\n                  precision={1}\n                  addonAfter=\"%\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={24}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"包装日期\"\n                name=\"packagingDate\"\n                rules={[{ required: true, message: \"请选择包装日期\" }]}\n              >\n                <DatePicker\n                  style={{ width: \"100%\" }}\n                  placeholder=\"选择包装日期\"\n                  disabledDate={(current) =>\n                    current && current > dayjs().endOf(\"day\")\n                  }\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item label=\"状态\" name=\"status\">\n                <Select>\n                  <Option value=\"active\">活跃</Option>\n                  <Option value=\"inactive\">停用</Option>\n                  <Option value=\"discontinued\">停产</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={24}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"品牌\"\n                name=\"brand\"\n                rules={[{ max: 50, message: \"品牌名称不能超过50个字符\" }]}\n              >\n                <Input placeholder=\"请输入品牌名称，如：茅台\" />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"分类\"\n                name=\"category\"\n                rules={[{ max: 50, message: \"分类名称不能超过50个字符\" }]}\n              >\n                <Input placeholder=\"请输入分类，如：白酒\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={24}>\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"容量 (ml)\"\n                name=\"volume\"\n                rules={[\n                  { type: \"number\", min: 1, message: \"容量必须大于0\" },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: \"100%\" }}\n                  placeholder=\"请输入容量\"\n                  min={1}\n                  addonAfter=\"ml\"\n                />\n              </Form.Item>\n            </Col>\n\n            <Col xs={24} md={12}>\n              <Form.Item\n                label=\"批次号\"\n                name=\"batchNumber\"\n                rules={[{ max: 50, message: \"批次号不能超过50个字符\" }]}\n              >\n                <Input placeholder=\"请输入批次号，如：B20240115001\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            label=\"生产地\"\n            name=\"productionLocation\"\n            rules={[{ max: 100, message: \"生产地不能超过100个字符\" }]}\n          >\n            <Input placeholder=\"请输入生产地，如：贵州茅台镇\" />\n          </Form.Item>\n\n          <Form.Item\n            label=\"商品描述\"\n            name=\"description\"\n            rules={[{ max: 500, message: \"描述不能超过500个字符\" }]}\n          >\n            <TextArea\n              rows={4}\n              placeholder=\"请输入商品描述（可选）\"\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                icon={<SaveOutlined />}\n              >\n                创建商品\n              </Button>\n              <Button onClick={handleBack}>取消</Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default CreateProduct;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,IAAI,EACJC,MAAM,EACNC,UAAU,EACVC,OAAO,EACPC,KAAK,EACLC,OAAO,EACPC,WAAW,EACXC,GAAG,EACHC,GAAG,QACE,MAAM;AACb,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,iBAAiB,EAAEC,YAAY,QAAQ,mBAAmB;AACnE,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAA2B,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAM;EAAEC;AAAS,CAAC,GAAGlB,KAAK;AAC1B,MAAM;EAAEmB;AAAO,CAAC,GAAGhB,MAAM;AAEzB,MAAMiB,aAAuB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,IAAI,CAAC,GAAGxB,IAAI,CAACyB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAM6B,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACFF,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMG,IAAuB,GAAG;QAC9BC,IAAI,EAAEF,MAAM,CAACE,IAAI;QACjBC,cAAc,EAAEH,MAAM,CAACG,cAAc;QACrCC,aAAa,EAAEJ,MAAM,CAACI,aAAa,CAACC,WAAW,CAAC,CAAC;QACjDC,WAAW,EAAEN,MAAM,CAACM,WAAW;QAC/BC,MAAM,EAAEP,MAAM,CAACO,MAAM,IAAI,QAAQ;QACjCC,KAAK,EAAER,MAAM,CAACQ,KAAK;QACnBC,QAAQ,EAAET,MAAM,CAACS,QAAQ;QACzBC,MAAM,EAAEV,MAAM,CAACU,MAAM;QACrBC,WAAW,EAAEX,MAAM,CAACW,WAAW;QAC/BC,kBAAkB,EAAEZ,MAAM,CAACY;MAC7B,CAAC;MAED,MAAMzB,UAAU,CAAC0B,MAAM,CAACZ,IAAI,CAAC;MAC7BxB,OAAO,CAACqC,OAAO,CAAC,QAAQ,CAAC;MACzBpB,QAAQ,CAAC,WAAW,CAAC;IACvB,CAAC,CAAC,OAAOqB,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRjB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvBtB,QAAQ,CAAC,WAAW,CAAC;EACvB,CAAC;EAED,oBACEL,OAAA;IAAA4B,QAAA,eACE5B,OAAA,CAACf,IAAI;MAAA2C,QAAA,gBACH5B,OAAA;QAAK6B,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAF,QAAA,eAC/B5B,OAAA,CAACX,KAAK;UAAAuC,QAAA,gBACJ5B,OAAA,CAAChB,MAAM;YAAC+C,IAAI,eAAE/B,OAAA,CAACL,iBAAiB;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAACC,OAAO,EAAET,UAAW;YAAAC,QAAA,EAAC;UAE1D;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTnC,OAAA,CAACV,OAAO;YAAC+C,IAAI,EAAC;UAAU;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3BnC,OAAA;YAAI6B,KAAK,EAAE;cAAES,MAAM,EAAE;YAAE,CAAE;YAAAV,QAAA,EAAC;UAAI;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAENnC,OAAA,CAAClB,IAAI;QACHwB,IAAI,EAAEA,IAAK;QACXiC,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE9B,YAAa;QACvB+B,aAAa,EAAE;UACbvB,MAAM,EAAE,QAAQ;UAChBJ,cAAc,EAAE;QAClB,CAAE;QAAAc,QAAA,gBAEF5B,OAAA,CAACR,GAAG;UAACkD,MAAM,EAAE,EAAG;UAAAd,QAAA,gBACd5B,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZjC,IAAI,EAAC,MAAM;cACXkC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5D,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAE6D,GAAG,EAAE,GAAG;gBAAE7D,OAAO,EAAE;cAAe,CAAC,CACrC;cAAAwC,QAAA,eAEF5B,OAAA,CAACjB,KAAK;gBAACmE,WAAW,EAAC;cAAe;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENnC,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,wBAAS;cACfjC,IAAI,EAAC,gBAAgB;cACrBkC,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5D,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEiD,IAAI,EAAE,QAAQ;gBAAEc,GAAG,EAAE,CAAC;gBAAEF,GAAG,EAAE,GAAG;gBAAE7D,OAAO,EAAE;cAAe,CAAC,CAC7D;cAAAwC,QAAA,eAEF5B,OAAA,CAACT,WAAW;gBACVsC,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO,CAAE;gBACzBF,WAAW,EAAC,sCAAQ;gBACpBC,GAAG,EAAE,CAAE;gBACPF,GAAG,EAAE,GAAI;gBACTI,SAAS,EAAE,CAAE;gBACbC,UAAU,EAAC;cAAG;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA,CAACR,GAAG;UAACkD,MAAM,EAAE,EAAG;UAAAd,QAAA,gBACd5B,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,0BAAM;cACZjC,IAAI,EAAC,eAAe;cACpBkC,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE5D,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAwC,QAAA,eAEhD5B,OAAA,CAACb,UAAU;gBACT0C,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO,CAAE;gBACzBF,WAAW,EAAC,sCAAQ;gBACpBK,YAAY,EAAGC,OAAO,IACpBA,OAAO,IAAIA,OAAO,GAAG3D,KAAK,CAAC,CAAC,CAAC4D,KAAK,CAAC,KAAK;cACzC;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENnC,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cAACC,KAAK,EAAC,cAAI;cAACjC,IAAI,EAAC,QAAQ;cAAAe,QAAA,eACjC5B,OAAA,CAACd,MAAM;gBAAA0C,QAAA,gBACL5B,OAAA,CAACE,MAAM;kBAACwD,KAAK,EAAC,QAAQ;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClCnC,OAAA,CAACE,MAAM;kBAACwD,KAAK,EAAC,UAAU;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpCnC,OAAA,CAACE,MAAM;kBAACwD,KAAK,EAAC,cAAc;kBAAA9B,QAAA,EAAC;gBAAE;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA,CAACR,GAAG;UAACkD,MAAM,EAAE,EAAG;UAAAd,QAAA,gBACd5B,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,cAAI;cACVjC,IAAI,EAAC,OAAO;cACZkC,KAAK,EAAE,CAAC;gBAAEE,GAAG,EAAE,EAAE;gBAAE7D,OAAO,EAAE;cAAgB,CAAC,CAAE;cAAAwC,QAAA,eAE/C5B,OAAA,CAACjB,KAAK;gBAACmE,WAAW,EAAC;cAAc;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENnC,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,cAAI;cACVjC,IAAI,EAAC,UAAU;cACfkC,KAAK,EAAE,CAAC;gBAAEE,GAAG,EAAE,EAAE;gBAAE7D,OAAO,EAAE;cAAgB,CAAC,CAAE;cAAAwC,QAAA,eAE/C5B,OAAA,CAACjB,KAAK;gBAACmE,WAAW,EAAC;cAAY;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA,CAACR,GAAG;UAACkD,MAAM,EAAE,EAAG;UAAAd,QAAA,gBACd5B,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,mBAAS;cACfjC,IAAI,EAAC,QAAQ;cACbkC,KAAK,EAAE,CACL;gBAAEV,IAAI,EAAE,QAAQ;gBAAEc,GAAG,EAAE,CAAC;gBAAE/D,OAAO,EAAE;cAAU,CAAC,CAC9C;cAAAwC,QAAA,eAEF5B,OAAA,CAACT,WAAW;gBACVsC,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO,CAAE;gBACzBF,WAAW,EAAC,gCAAO;gBACnBC,GAAG,EAAE,CAAE;gBACPG,UAAU,EAAC;cAAI;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAENnC,OAAA,CAACP,GAAG;YAACkD,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAhB,QAAA,eAClB5B,OAAA,CAAClB,IAAI,CAAC+D,IAAI;cACRC,KAAK,EAAC,oBAAK;cACXjC,IAAI,EAAC,aAAa;cAClBkC,KAAK,EAAE,CAAC;gBAAEE,GAAG,EAAE,EAAE;gBAAE7D,OAAO,EAAE;cAAe,CAAC,CAAE;cAAAwC,QAAA,eAE9C5B,OAAA,CAACjB,KAAK;gBAACmE,WAAW,EAAC;cAAuB;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnC,OAAA,CAAClB,IAAI,CAAC+D,IAAI;UACRC,KAAK,EAAC,oBAAK;UACXjC,IAAI,EAAC,oBAAoB;UACzBkC,KAAK,EAAE,CAAC;YAAEE,GAAG,EAAE,GAAG;YAAE7D,OAAO,EAAE;UAAgB,CAAC,CAAE;UAAAwC,QAAA,eAEhD5B,OAAA,CAACjB,KAAK;YAACmE,WAAW,EAAC;UAAgB;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEZnC,OAAA,CAAClB,IAAI,CAAC+D,IAAI;UACRC,KAAK,EAAC,0BAAM;UACZjC,IAAI,EAAC,aAAa;UAClBkC,KAAK,EAAE,CAAC;YAAEE,GAAG,EAAE,GAAG;YAAE7D,OAAO,EAAE;UAAe,CAAC,CAAE;UAAAwC,QAAA,eAE/C5B,OAAA,CAACC,QAAQ;YACP0D,IAAI,EAAE,CAAE;YACRT,WAAW,EAAC,oEAAa;YACzBU,SAAS;YACTC,SAAS,EAAE;UAAI;YAAA7B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZnC,OAAA,CAAClB,IAAI,CAAC+D,IAAI;UAAAjB,QAAA,eACR5B,OAAA,CAACX,KAAK;YAAAuC,QAAA,gBACJ5B,OAAA,CAAChB,MAAM;cACLqD,IAAI,EAAC,SAAS;cACdyB,QAAQ,EAAC,QAAQ;cACjBtD,OAAO,EAAEA,OAAQ;cACjBuB,IAAI,eAAE/B,OAAA,CAACJ,YAAY;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAAAP,QAAA,EACxB;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnC,OAAA,CAAChB,MAAM;cAACoD,OAAO,EAAET,UAAW;cAAAC,QAAA,EAAC;YAAE;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC/B,EAAA,CAjNID,aAAuB;EAAA,QACVT,WAAW,EACbZ,IAAI,CAACyB,OAAO;AAAA;AAAAwD,EAAA,GAFvB5D,aAAuB;AAmN7B,eAAeA,aAAa;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}