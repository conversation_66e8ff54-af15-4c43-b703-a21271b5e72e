{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { getRowFormat, toArray } from \"../../utils/miscUtil\";\nexport function useFieldFormat(picker, locale, format) {\n  return React.useMemo(function () {\n    var rawFormat = getRowFormat(picker, locale, format);\n    var formatList = toArray(rawFormat);\n    var firstFormat = formatList[0];\n    var maskFormat = _typeof(firstFormat) === 'object' && firstFormat.type === 'mask' ? firstFormat.format : null;\n    return [\n    // Format list\n    formatList.map(function (config) {\n      return typeof config === 'string' || typeof config === 'function' ? config : config.format;\n    }),\n    // Mask Format\n    maskFormat];\n  }, [picker, locale, format]);\n}", "map": {"version": 3, "names": ["_typeof", "React", "getRowFormat", "toArray", "useFieldFormat", "picker", "locale", "format", "useMemo", "rawFormat", "formatList", "firstFormat", "maskFormat", "type", "map", "config"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-picker/es/PickerInput/hooks/useFieldFormat.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport * as React from 'react';\nimport { getRowFormat, toArray } from \"../../utils/miscUtil\";\nexport function useFieldFormat(picker, locale, format) {\n  return React.useMemo(function () {\n    var rawFormat = getRowFormat(picker, locale, format);\n    var formatList = toArray(rawFormat);\n    var firstFormat = formatList[0];\n    var maskFormat = _typeof(firstFormat) === 'object' && firstFormat.type === 'mask' ? firstFormat.format : null;\n    return [\n    // Format list\n    formatList.map(function (config) {\n      return typeof config === 'string' || typeof config === 'function' ? config : config.format;\n    }),\n    // Mask Format\n    maskFormat];\n  }, [picker, locale, format]);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,YAAY,EAAEC,OAAO,QAAQ,sBAAsB;AAC5D,OAAO,SAASC,cAAcA,CAACC,MAAM,EAAEC,MAAM,EAAEC,MAAM,EAAE;EACrD,OAAON,KAAK,CAACO,OAAO,CAAC,YAAY;IAC/B,IAAIC,SAAS,GAAGP,YAAY,CAACG,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC;IACpD,IAAIG,UAAU,GAAGP,OAAO,CAACM,SAAS,CAAC;IACnC,IAAIE,WAAW,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC/B,IAAIE,UAAU,GAAGZ,OAAO,CAACW,WAAW,CAAC,KAAK,QAAQ,IAAIA,WAAW,CAACE,IAAI,KAAK,MAAM,GAAGF,WAAW,CAACJ,MAAM,GAAG,IAAI;IAC7G,OAAO;IACP;IACAG,UAAU,CAACI,GAAG,CAAC,UAAUC,MAAM,EAAE;MAC/B,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,UAAU,GAAGA,MAAM,GAAGA,MAAM,CAACR,MAAM;IAC5F,CAAC,CAAC;IACF;IACAK,UAAU,CAAC;EACb,CAAC,EAAE,CAACP,MAAM,EAAEC,MAAM,EAAEC,MAAM,CAAC,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}