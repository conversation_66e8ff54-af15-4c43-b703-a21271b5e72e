{"ast": null, "code": "/* istanbul ignore file */\nimport * as React from 'react';\nfunction useRenderTimes(props, debug) {\n  // Render times\n  var timesRef = React.useRef(0);\n  timesRef.current += 1;\n\n  // Props changed\n  var propsRef = React.useRef(props);\n  var keys = [];\n  Object.keys(props || {}).map(function (key) {\n    var _propsRef$current;\n    if ((props === null || props === void 0 ? void 0 : props[key]) !== ((_propsRef$current = propsRef.current) === null || _propsRef$current === void 0 ? void 0 : _propsRef$current[key])) {\n      keys.push(key);\n    }\n  });\n  propsRef.current = props;\n\n  // Cache keys since React rerender may cause it lost\n  var keysRef = React.useRef([]);\n  if (keys.length) {\n    keysRef.current = keys;\n  }\n  React.useDebugValue(timesRef.current);\n  React.useDebugValue(keysRef.current.join(', '));\n  if (debug) {\n    console.log(\"\".concat(debug, \":\"), timesRef.current, keysRef.current);\n  }\n  return timesRef.current;\n}\nexport default process.env.NODE_ENV !== 'production' ? useRenderTimes : function () {};\nexport var RenderBlock = /*#__PURE__*/React.memo(function () {\n  var times = useRenderTimes();\n  return /*#__PURE__*/React.createElement(\"h1\", null, \"Render Times: \", times);\n});\nif (process.env.NODE_ENV !== 'production') {\n  RenderBlock.displayName = 'RenderBlock';\n}", "map": {"version": 3, "names": ["React", "useRenderTimes", "props", "debug", "timesRef", "useRef", "current", "propsRef", "keys", "Object", "map", "key", "_propsRef$current", "push", "keysRef", "length", "useDebugValue", "join", "console", "log", "concat", "process", "env", "NODE_ENV", "RenderBlock", "memo", "times", "createElement", "displayName"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-table/es/hooks/useRenderTimes.js"], "sourcesContent": ["/* istanbul ignore file */\nimport * as React from 'react';\nfunction useRenderTimes(props, debug) {\n  // Render times\n  var timesRef = React.useRef(0);\n  timesRef.current += 1;\n\n  // Props changed\n  var propsRef = React.useRef(props);\n  var keys = [];\n  Object.keys(props || {}).map(function (key) {\n    var _propsRef$current;\n    if ((props === null || props === void 0 ? void 0 : props[key]) !== ((_propsRef$current = propsRef.current) === null || _propsRef$current === void 0 ? void 0 : _propsRef$current[key])) {\n      keys.push(key);\n    }\n  });\n  propsRef.current = props;\n\n  // Cache keys since React rerender may cause it lost\n  var keysRef = React.useRef([]);\n  if (keys.length) {\n    keysRef.current = keys;\n  }\n  React.useDebugValue(timesRef.current);\n  React.useDebugValue(keysRef.current.join(', '));\n  if (debug) {\n    console.log(\"\".concat(debug, \":\"), timesRef.current, keysRef.current);\n  }\n  return timesRef.current;\n}\nexport default process.env.NODE_ENV !== 'production' ? useRenderTimes : function () {};\nexport var RenderBlock = /*#__PURE__*/React.memo(function () {\n  var times = useRenderTimes();\n  return /*#__PURE__*/React.createElement(\"h1\", null, \"Render Times: \", times);\n});\nif (process.env.NODE_ENV !== 'production') {\n  RenderBlock.displayName = 'RenderBlock';\n}"], "mappings": "AAAA;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACpC;EACA,IAAIC,QAAQ,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC;EAC9BD,QAAQ,CAACE,OAAO,IAAI,CAAC;;EAErB;EACA,IAAIC,QAAQ,GAAGP,KAAK,CAACK,MAAM,CAACH,KAAK,CAAC;EAClC,IAAIM,IAAI,GAAG,EAAE;EACbC,MAAM,CAACD,IAAI,CAACN,KAAK,IAAI,CAAC,CAAC,CAAC,CAACQ,GAAG,CAAC,UAAUC,GAAG,EAAE;IAC1C,IAAIC,iBAAiB;IACrB,IAAI,CAACV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACS,GAAG,CAAC,OAAO,CAACC,iBAAiB,GAAGL,QAAQ,CAACD,OAAO,MAAM,IAAI,IAAIM,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACD,GAAG,CAAC,CAAC,EAAE;MACtLH,IAAI,CAACK,IAAI,CAACF,GAAG,CAAC;IAChB;EACF,CAAC,CAAC;EACFJ,QAAQ,CAACD,OAAO,GAAGJ,KAAK;;EAExB;EACA,IAAIY,OAAO,GAAGd,KAAK,CAACK,MAAM,CAAC,EAAE,CAAC;EAC9B,IAAIG,IAAI,CAACO,MAAM,EAAE;IACfD,OAAO,CAACR,OAAO,GAAGE,IAAI;EACxB;EACAR,KAAK,CAACgB,aAAa,CAACZ,QAAQ,CAACE,OAAO,CAAC;EACrCN,KAAK,CAACgB,aAAa,CAACF,OAAO,CAACR,OAAO,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;EAC/C,IAAId,KAAK,EAAE;IACTe,OAAO,CAACC,GAAG,CAAC,EAAE,CAACC,MAAM,CAACjB,KAAK,EAAE,GAAG,CAAC,EAAEC,QAAQ,CAACE,OAAO,EAAEQ,OAAO,CAACR,OAAO,CAAC;EACvE;EACA,OAAOF,QAAQ,CAACE,OAAO;AACzB;AACA,eAAee,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtB,cAAc,GAAG,YAAY,CAAC,CAAC;AACtF,OAAO,IAAIuB,WAAW,GAAG,aAAaxB,KAAK,CAACyB,IAAI,CAAC,YAAY;EAC3D,IAAIC,KAAK,GAAGzB,cAAc,CAAC,CAAC;EAC5B,OAAO,aAAaD,KAAK,CAAC2B,aAAa,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAED,KAAK,CAAC;AAC9E,CAAC,CAAC;AACF,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCC,WAAW,CAACI,WAAW,GAAG,aAAa;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}