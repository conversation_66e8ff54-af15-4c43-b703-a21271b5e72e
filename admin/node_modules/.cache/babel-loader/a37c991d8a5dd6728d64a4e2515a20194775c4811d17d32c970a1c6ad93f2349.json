{"ast": null, "code": "module.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n  switch (active.tagName.toUpperCase()) {\n    // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n    default:\n      active = null;\n      break;\n  }\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' && selection.removeAllRanges();\n    if (!selection.rangeCount) {\n      ranges.forEach(function (range) {\n        selection.addRange(range);\n      });\n    }\n    active && active.focus();\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "selection", "document", "getSelection", "rangeCount", "active", "activeElement", "ranges", "i", "push", "getRangeAt", "tagName", "toUpperCase", "blur", "removeAllRanges", "type", "for<PERSON>ach", "range", "addRange", "focus"], "sources": ["/Users/<USER>/github/7/admin/node_modules/toggle-selection/index.js"], "sourcesContent": ["\nmodule.exports = function () {\n  var selection = document.getSelection();\n  if (!selection.rangeCount) {\n    return function () {};\n  }\n  var active = document.activeElement;\n\n  var ranges = [];\n  for (var i = 0; i < selection.rangeCount; i++) {\n    ranges.push(selection.getRangeAt(i));\n  }\n\n  switch (active.tagName.toUpperCase()) { // .toUpperCase handles XHTML\n    case 'INPUT':\n    case 'TEXTAREA':\n      active.blur();\n      break;\n\n    default:\n      active = null;\n      break;\n  }\n\n  selection.removeAllRanges();\n  return function () {\n    selection.type === 'Caret' &&\n    selection.removeAllRanges();\n\n    if (!selection.rangeCount) {\n      ranges.forEach(function(range) {\n        selection.addRange(range);\n      });\n    }\n\n    active &&\n    active.focus();\n  };\n};\n"], "mappings": "AACAA,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,IAAIC,SAAS,GAAGC,QAAQ,CAACC,YAAY,CAAC,CAAC;EACvC,IAAI,CAACF,SAAS,CAACG,UAAU,EAAE;IACzB,OAAO,YAAY,CAAC,CAAC;EACvB;EACA,IAAIC,MAAM,GAAGH,QAAQ,CAACI,aAAa;EAEnC,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,SAAS,CAACG,UAAU,EAAEI,CAAC,EAAE,EAAE;IAC7CD,MAAM,CAACE,IAAI,CAACR,SAAS,CAACS,UAAU,CAACF,CAAC,CAAC,CAAC;EACtC;EAEA,QAAQH,MAAM,CAACM,OAAO,CAACC,WAAW,CAAC,CAAC;IAAI;IACtC,KAAK,OAAO;IACZ,KAAK,UAAU;MACbP,MAAM,CAACQ,IAAI,CAAC,CAAC;MACb;IAEF;MACER,MAAM,GAAG,IAAI;MACb;EACJ;EAEAJ,SAAS,CAACa,eAAe,CAAC,CAAC;EAC3B,OAAO,YAAY;IACjBb,SAAS,CAACc,IAAI,KAAK,OAAO,IAC1Bd,SAAS,CAACa,eAAe,CAAC,CAAC;IAE3B,IAAI,CAACb,SAAS,CAACG,UAAU,EAAE;MACzBG,MAAM,CAACS,OAAO,CAAC,UAASC,KAAK,EAAE;QAC7BhB,SAAS,CAACiB,QAAQ,CAACD,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ;IAEAZ,MAAM,IACNA,MAAM,CAACc,KAAK,CAAC,CAAC;EAChB,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}