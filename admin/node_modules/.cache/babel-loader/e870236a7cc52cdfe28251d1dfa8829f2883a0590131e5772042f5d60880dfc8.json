{"ast": null, "code": "import KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from \"react\";\nvar ESC = KeyCode.ESC,\n  TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n    triggerRef = _ref.triggerRef,\n    onVisibleChange = _ref.onVisibleChange,\n    autoFocus = _ref.autoFocus,\n    overlayRef = _ref.overlayRef;\n  var focusMenuRef = React.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible) {\n      var _triggerRef$current, _triggerRef$current$f;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 || (_triggerRef$current$f = _triggerRef$current.focus) === null || _triggerRef$current$f === void 0 || _triggerRef$current$f.call(_triggerRef$current);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(false);\n    }\n  };\n  var focusMenu = function focusMenu() {\n    var _overlayRef$current;\n    if ((_overlayRef$current = overlayRef.current) !== null && _overlayRef$current !== void 0 && _overlayRef$current.focus) {\n      overlayRef.current.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n    return false;\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        {\n          var focusResult = false;\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n          break;\n        }\n    }\n  };\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener(\"keydown\", handleKeyDown);\n      if (autoFocus) {\n        // FIXME: hack with raf\n        raf(focusMenu, 3);\n      }\n      return function () {\n        window.removeEventListener(\"keydown\", handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}", "map": {"version": 3, "names": ["KeyCode", "raf", "React", "ESC", "TAB", "useAccessibility", "_ref", "visible", "triggerRef", "onVisibleChange", "autoFocus", "overlayRef", "focusMenuRef", "useRef", "handleCloseMenuAndReturnFocus", "_triggerRef$current", "_triggerRef$current$f", "current", "focus", "call", "focusMenu", "_overlayRef$current", "handleKeyDown", "event", "keyCode", "focusResult", "preventDefault", "useEffect", "window", "addEventListener", "removeEventListener"], "sources": ["/Users/<USER>/github/7/admin/node_modules/rc-dropdown/es/hooks/useAccessibility.js"], "sourcesContent": ["import KeyCode from \"rc-util/es/KeyCode\";\nimport raf from \"rc-util/es/raf\";\nimport * as React from \"react\";\nvar ESC = KeyCode.ESC,\n  TAB = KeyCode.TAB;\nexport default function useAccessibility(_ref) {\n  var visible = _ref.visible,\n    triggerRef = _ref.triggerRef,\n    onVisibleChange = _ref.onVisibleChange,\n    autoFocus = _ref.autoFocus,\n    overlayRef = _ref.overlayRef;\n  var focusMenuRef = React.useRef(false);\n  var handleCloseMenuAndReturnFocus = function handleCloseMenuAndReturnFocus() {\n    if (visible) {\n      var _triggerRef$current, _triggerRef$current$f;\n      (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 || (_triggerRef$current$f = _triggerRef$current.focus) === null || _triggerRef$current$f === void 0 || _triggerRef$current$f.call(_triggerRef$current);\n      onVisibleChange === null || onVisibleChange === void 0 || onVisibleChange(false);\n    }\n  };\n  var focusMenu = function focusMenu() {\n    var _overlayRef$current;\n    if ((_overlayRef$current = overlayRef.current) !== null && _overlayRef$current !== void 0 && _overlayRef$current.focus) {\n      overlayRef.current.focus();\n      focusMenuRef.current = true;\n      return true;\n    }\n    return false;\n  };\n  var handleKeyDown = function handleKeyDown(event) {\n    switch (event.keyCode) {\n      case ESC:\n        handleCloseMenuAndReturnFocus();\n        break;\n      case TAB:\n        {\n          var focusResult = false;\n          if (!focusMenuRef.current) {\n            focusResult = focusMenu();\n          }\n          if (focusResult) {\n            event.preventDefault();\n          } else {\n            handleCloseMenuAndReturnFocus();\n          }\n          break;\n        }\n    }\n  };\n  React.useEffect(function () {\n    if (visible) {\n      window.addEventListener(\"keydown\", handleKeyDown);\n      if (autoFocus) {\n        // FIXME: hack with raf\n        raf(focusMenu, 3);\n      }\n      return function () {\n        window.removeEventListener(\"keydown\", handleKeyDown);\n        focusMenuRef.current = false;\n      };\n    }\n    return function () {\n      focusMenuRef.current = false;\n    };\n  }, [visible]); // eslint-disable-line react-hooks/exhaustive-deps\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,oBAAoB;AACxC,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,IAAIC,GAAG,GAAGH,OAAO,CAACG,GAAG;EACnBC,GAAG,GAAGJ,OAAO,CAACI,GAAG;AACnB,eAAe,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC7C,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;IACxBC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,eAAe,GAAGH,IAAI,CAACG,eAAe;IACtCC,SAAS,GAAGJ,IAAI,CAACI,SAAS;IAC1BC,UAAU,GAAGL,IAAI,CAACK,UAAU;EAC9B,IAAIC,YAAY,GAAGV,KAAK,CAACW,MAAM,CAAC,KAAK,CAAC;EACtC,IAAIC,6BAA6B,GAAG,SAASA,6BAA6BA,CAAA,EAAG;IAC3E,IAAIP,OAAO,EAAE;MACX,IAAIQ,mBAAmB,EAAEC,qBAAqB;MAC9C,CAACD,mBAAmB,GAAGP,UAAU,CAACS,OAAO,MAAM,IAAI,IAAIF,mBAAmB,KAAK,KAAK,CAAC,IAAI,CAACC,qBAAqB,GAAGD,mBAAmB,CAACG,KAAK,MAAM,IAAI,IAAIF,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACG,IAAI,CAACJ,mBAAmB,CAAC;MAC5ON,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAAC,KAAK,CAAC;IAClF;EACF,CAAC;EACD,IAAIW,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACnC,IAAIC,mBAAmB;IACvB,IAAI,CAACA,mBAAmB,GAAGV,UAAU,CAACM,OAAO,MAAM,IAAI,IAAII,mBAAmB,KAAK,KAAK,CAAC,IAAIA,mBAAmB,CAACH,KAAK,EAAE;MACtHP,UAAU,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC;MAC1BN,YAAY,CAACK,OAAO,GAAG,IAAI;MAC3B,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC;EACD,IAAIK,aAAa,GAAG,SAASA,aAAaA,CAACC,KAAK,EAAE;IAChD,QAAQA,KAAK,CAACC,OAAO;MACnB,KAAKrB,GAAG;QACNW,6BAA6B,CAAC,CAAC;QAC/B;MACF,KAAKV,GAAG;QACN;UACE,IAAIqB,WAAW,GAAG,KAAK;UACvB,IAAI,CAACb,YAAY,CAACK,OAAO,EAAE;YACzBQ,WAAW,GAAGL,SAAS,CAAC,CAAC;UAC3B;UACA,IAAIK,WAAW,EAAE;YACfF,KAAK,CAACG,cAAc,CAAC,CAAC;UACxB,CAAC,MAAM;YACLZ,6BAA6B,CAAC,CAAC;UACjC;UACA;QACF;IACJ;EACF,CAAC;EACDZ,KAAK,CAACyB,SAAS,CAAC,YAAY;IAC1B,IAAIpB,OAAO,EAAE;MACXqB,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEP,aAAa,CAAC;MACjD,IAAIZ,SAAS,EAAE;QACb;QACAT,GAAG,CAACmB,SAAS,EAAE,CAAC,CAAC;MACnB;MACA,OAAO,YAAY;QACjBQ,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAER,aAAa,CAAC;QACpDV,YAAY,CAACK,OAAO,GAAG,KAAK;MAC9B,CAAC;IACH;IACA,OAAO,YAAY;MACjBL,YAAY,CAACK,OAAO,GAAG,KAAK;IAC9B,CAAC;EACH,CAAC,EAAE,CAACV,OAAO,CAAC,CAAC,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}