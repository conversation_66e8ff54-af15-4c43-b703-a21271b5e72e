{"ast": null, "code": "import * as React from 'react';\nvar TriggerContext = /*#__PURE__*/React.createContext(null);\nexport default TriggerContext;", "map": {"version": 3, "names": ["React", "TriggerContext", "createContext"], "sources": ["/Users/<USER>/github/7/admin/node_modules/@rc-component/trigger/es/context.js"], "sourcesContent": ["import * as React from 'react';\nvar TriggerContext = /*#__PURE__*/React.createContext(null);\nexport default TriggerContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC3D,eAAeD,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}