{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from \"react\";\nimport { Row, Col, Card, Statistic, Spin, message } from \"antd\";\nimport { QrcodeOutlined, CheckCircleOutlined, StopOutlined, ClockCircleOutlined, ScanOutlined } from \"@ant-design/icons\";\nimport { qrCodeApi } from \"../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [stats, setStats] = useState(null);\n  const [productStats, setProductStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    fetchStats();\n  }, []);\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      const data = await qrCodeApi.getStats();\n      setStats(data);\n    } catch (error) {\n      message.error(\"获取统计信息失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: \"center\",\n        padding: \"50px\"\n      },\n      children: /*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n      children: \"\\u4EEA\\u8868\\u76D8\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4E8C\\u7EF4\\u7801\\u6570\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.total) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#1890ff\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u6D3B\\u8DC3\\u4E8C\\u7EF4\\u7801\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.active) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 69,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#52c41a\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u505C\\u7528\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.inactive) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#faad14\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 8,\n        lg: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u8FC7\\u671F\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.expired) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#ff4d4f\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: [16, 16],\n      style: {\n        marginTop: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"stats-card\",\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u626B\\u63CF\\u6B21\\u6570\",\n            value: (stats === null || stats === void 0 ? void 0 : stats.totalScans) || 0,\n            prefix: /*#__PURE__*/_jsxDEV(ScanOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: \"#722ed1\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"fFaqyhVEGQRcIRlYNiJzxA0jw4U=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "Row", "Col", "Card", "Statistic", "Spin", "message", "QrcodeOutlined", "CheckCircleOutlined", "StopOutlined", "ClockCircleOutlined", "ScanOutlined", "qrCodeApi", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "stats", "setStats", "productStats", "setProductStats", "loading", "setLoading", "fetchStats", "data", "getStats", "error", "style", "textAlign", "padding", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "sm", "md", "lg", "className", "title", "value", "total", "prefix", "valueStyle", "color", "active", "inactive", "expired", "marginTop", "totalScans", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/pages/Dashboard.tsx"], "sourcesContent": ["import React, { useEffect, useState } from \"react\";\nimport { Row, Col, Card, Statistic, Spin, message } from \"antd\";\nimport {\n  QrcodeOutlined,\n  CheckCircleOutlined,\n  StopOutlined,\n  ClockCircleOutlined,\n  ScanOutlined,\n  ShoppingOutlined,\n  TagsOutlined,\n} from \"@ant-design/icons\";\nimport {\n  qrCodeApi,\n  productApi,\n  StatsResponse,\n  ProductStatsResponse,\n} from \"../services/api\";\n\nconst Dashboard: React.FC = () => {\n  const [stats, setStats] = useState<StatsResponse | null>(null);\n  const [productStats, setProductStats] = useState<ProductStatsResponse | null>(\n    null\n  );\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchStats();\n  }, []);\n\n  const fetchStats = async () => {\n    try {\n      setLoading(true);\n      const data = await qrCodeApi.getStats();\n      setStats(data);\n    } catch (error) {\n      message.error(\"获取统计信息失败\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div style={{ textAlign: \"center\", padding: \"50px\" }}>\n        <Spin size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <h1>仪表盘</h1>\n      <Row gutter={[16, 16]}>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"总二维码数\"\n              value={stats?.total || 0}\n              prefix={<QrcodeOutlined />}\n              valueStyle={{ color: \"#1890ff\" }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"活跃二维码\"\n              value={stats?.active || 0}\n              prefix={<CheckCircleOutlined />}\n              valueStyle={{ color: \"#52c41a\" }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"已停用\"\n              value={stats?.inactive || 0}\n              prefix={<StopOutlined />}\n              valueStyle={{ color: \"#faad14\" }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={8} lg={6}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"已过期\"\n              value={stats?.expired || 0}\n              prefix={<ClockCircleOutlined />}\n              valueStyle={{ color: \"#ff4d4f\" }}\n            />\n          </Card>\n        </Col>\n      </Row>\n      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>\n        <Col xs={24} sm={12}>\n          <Card className=\"stats-card\">\n            <Statistic\n              title=\"总扫描次数\"\n              value={stats?.totalScans || 0}\n              prefix={<ScanOutlined />}\n              valueStyle={{ color: \"#722ed1\" }}\n            />\n          </Card>\n        </Col>\n      </Row>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,GAAG,EAAEC,GAAG,EAAEC,IAAI,EAAEC,SAAS,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AAC/D,SACEC,cAAc,EACdC,mBAAmB,EACnBC,YAAY,EACZC,mBAAmB,EACnBC,YAAY,QAGP,mBAAmB;AAC1B,SACEC,SAAS,QAIJ,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAuB,IAAI,CAAC;EAC9D,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAC9C,IACF,CAAC;EACD,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAE5CD,SAAS,CAAC,MAAM;IACdwB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACFD,UAAU,CAAC,IAAI,CAAC;MAChB,MAAME,IAAI,GAAG,MAAMZ,SAAS,CAACa,QAAQ,CAAC,CAAC;MACvCP,QAAQ,CAACM,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAID,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKa,KAAK,EAAE;QAAEC,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAC,QAAA,eACnDhB,OAAA,CAACT,IAAI;QAAC0B,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC;EAEV;EAEA,oBACErB,OAAA;IAAAgB,QAAA,gBACEhB,OAAA;MAAAgB,QAAA,EAAI;IAAG;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACZrB,OAAA,CAACb,GAAG;MAACmC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAAAN,QAAA,gBACpBhB,OAAA,CAACZ,GAAG;QAACmC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChChB,OAAA,CAACX,IAAI;UAACsC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1BhB,OAAA,CAACV,SAAS;YACRsC,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAA1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE2B,KAAK,KAAI,CAAE;YACzBC,MAAM,eAAE/B,OAAA,CAACP,cAAc;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrB,OAAA,CAACZ,GAAG;QAACmC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChChB,OAAA,CAACX,IAAI;UAACsC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1BhB,OAAA,CAACV,SAAS;YACRsC,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAA1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE+B,MAAM,KAAI,CAAE;YAC1BH,MAAM,eAAE/B,OAAA,CAACN,mBAAmB;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrB,OAAA,CAACZ,GAAG;QAACmC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChChB,OAAA,CAACX,IAAI;UAACsC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1BhB,OAAA,CAACV,SAAS;YACRsC,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE,CAAA1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEgC,QAAQ,KAAI,CAAE;YAC5BJ,MAAM,eAAE/B,OAAA,CAACL,YAAY;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNrB,OAAA,CAACZ,GAAG;QAACmC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eAChChB,OAAA,CAACX,IAAI;UAACsC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1BhB,OAAA,CAACV,SAAS;YACRsC,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE,CAAA1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiC,OAAO,KAAI,CAAE;YAC3BL,MAAM,eAAE/B,OAAA,CAACJ,mBAAmB;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChCW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNrB,OAAA,CAACb,GAAG;MAACmC,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;MAACT,KAAK,EAAE;QAAEwB,SAAS,EAAE;MAAG,CAAE;MAAArB,QAAA,eAC9ChB,OAAA,CAACZ,GAAG;QAACmC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAR,QAAA,eAClBhB,OAAA,CAACX,IAAI;UAACsC,SAAS,EAAC,YAAY;UAAAX,QAAA,eAC1BhB,OAAA,CAACV,SAAS;YACRsC,KAAK,EAAC,gCAAO;YACbC,KAAK,EAAE,CAAA1B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmC,UAAU,KAAI,CAAE;YAC9BP,MAAM,eAAE/B,OAAA,CAACH,YAAY;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBW,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAU;UAAE;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CA1FID,SAAmB;AAAAsC,EAAA,GAAnBtC,SAAmB;AA4FzB,eAAeA,SAAS;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}