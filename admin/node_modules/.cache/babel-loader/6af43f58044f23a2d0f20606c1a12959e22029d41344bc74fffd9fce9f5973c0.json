{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/App.tsx\";\nimport React from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\nimport { Layout } from \"antd\";\nimport Sidebar from \"./components/Sidebar\";\nimport Header from \"./components/Header\";\nimport Dashboard from \"./pages/Dashboard\";\nimport QrCodeList from \"./pages/QrCodeList\";\nimport QrCodeDetail from \"./pages/QrCodeDetail\";\nimport CreateQrCode from \"./pages/CreateQrCode\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Content\n} = Layout;\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 19,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: \"24px 16px\",\n          padding: 24,\n          background: \"#fff\"\n        },\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/qr-codes\",\n            element: /*#__PURE__*/_jsxDEV(QrCodeList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 46\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/qr-codes/create\",\n            element: /*#__PURE__*/_jsxDEV(CreateQrCode, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 53\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/qr-codes/:id\",\n            element: /*#__PURE__*/_jsxDEV(QrCodeDetail, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 50\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 20,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Layout", "Sidebar", "Header", "Dashboard", "QrCodeList", "QrCodeDetail", "CreateQrCode", "jsxDEV", "_jsxDEV", "Content", "App", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "margin", "padding", "background", "path", "element", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/App.tsx"], "sourcesContent": ["import React from \"react\";\nimport { Routes, Route } from \"react-router-dom\";\nimport { Layout } from \"antd\";\nimport Sidebar from \"./components/Sidebar\";\nimport Header from \"./components/Header\";\nimport Dashboard from \"./pages/Dashboard\";\nimport QrCodeList from \"./pages/QrCodeList\";\nimport QrCodeDetail from \"./pages/QrCodeDetail\";\nimport CreateQrCode from \"./pages/CreateQrCode\";\nimport ProductList from \"./pages/ProductList\";\nimport ProductDetail from \"./pages/ProductDetail\";\nimport CreateProduct from \"./pages/CreateProduct\";\n\nconst { Content } = Layout;\n\nfunction App() {\n  return (\n    <Layout>\n      <Sidebar />\n      <Layout>\n        <Header />\n        <Content\n          style={{ margin: \"24px 16px\", padding: 24, background: \"#fff\" }}\n        >\n          <Routes>\n            <Route path=\"/\" element={<Dashboard />} />\n            <Route path=\"/qr-codes\" element={<QrCodeList />} />\n            <Route path=\"/qr-codes/create\" element={<CreateQrCode />} />\n            <Route path=\"/qr-codes/:id\" element={<QrCodeDetail />} />\n          </Routes>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AAChD,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,YAAY,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAKhD,MAAM;EAAEC;AAAQ,CAAC,GAAGT,MAAM;AAE1B,SAASU,GAAGA,CAAA,EAAG;EACb,oBACEF,OAAA,CAACR,MAAM;IAAAW,QAAA,gBACLH,OAAA,CAACP,OAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACXP,OAAA,CAACR,MAAM;MAAAW,QAAA,gBACLH,OAAA,CAACN,MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACVP,OAAA,CAACC,OAAO;QACNO,KAAK,EAAE;UAAEC,MAAM,EAAE,WAAW;UAAEC,OAAO,EAAE,EAAE;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAR,QAAA,eAEhEH,OAAA,CAACV,MAAM;UAAAa,QAAA,gBACLH,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEb,OAAA,CAACL,SAAS;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC1CP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,WAAW;YAACC,OAAO,eAAEb,OAAA,CAACJ,UAAU;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnDP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,kBAAkB;YAACC,OAAO,eAAEb,OAAA,CAACF,YAAY;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DP,OAAA,CAACT,KAAK;YAACqB,IAAI,EAAC,eAAe;YAACC,OAAO,eAAEb,OAAA,CAACH,YAAY;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACO,EAAA,GAnBQZ,GAAG;AAqBZ,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}