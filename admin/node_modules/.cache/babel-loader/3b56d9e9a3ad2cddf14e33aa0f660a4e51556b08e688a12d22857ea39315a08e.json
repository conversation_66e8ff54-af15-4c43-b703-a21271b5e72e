{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/github/7/admin/src/components/QRCodeModal.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from \"react\";\nimport { Modal, Button, Space, Typography, Descriptions, message, Spin, Alert, Image } from \"antd\";\nimport { DownloadOutlined, QrcodeOutlined, CopyOutlined, ReloadOutlined } from \"@ant-design/icons\";\nimport { wechatApi } from \"../services/api\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst QRCodeModal = ({\n  visible,\n  onClose,\n  product\n}) => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [qrCodeData, setQrCodeData] = useState(null);\n\n  // 生成二维码\n  const generateQRCode = async () => {\n    if (!product) return;\n    try {\n      setLoading(true);\n      const response = await wechatApi.generateQRCode({\n        productNumber: product.productNumber,\n        scene: \"admin_generate\",\n        type: \"standard\"\n      });\n      if (response.success) {\n        setQrCodeData(response.data);\n        message.success(\"二维码生成成功\");\n      } else {\n        message.error(\"二维码生成失败\");\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || \"生成失败，请重试\";\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载二维码\n  const downloadQRCode = () => {\n    if (!qrCodeData) return;\n    try {\n      const link = document.createElement(\"a\");\n      link.href = qrCodeData.qrCodeImage;\n      link.download = `${product === null || product === void 0 ? void 0 : product.name}_${qrCodeData.productNumber}_wechat_qrcode.png`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      message.success(\"二维码下载成功\");\n    } catch (error) {\n      message.error(\"下载失败\");\n    }\n  };\n\n  // 复制商品编号\n  const copyProductNumber = () => {\n    if (!product) return;\n    navigator.clipboard.writeText(product.productNumber).then(() => {\n      message.success(\"商品编号已复制到剪贴板\");\n    }).catch(() => {\n      message.error(\"复制失败\");\n    });\n  };\n\n  // 重新生成二维码\n  const regenerateQRCode = () => {\n    setQrCodeData(null);\n    generateQRCode();\n  };\n\n  // 当弹窗打开时自动生成二维码\n  React.useEffect(() => {\n    if (visible && product && !qrCodeData) {\n      generateQRCode();\n    }\n  }, [visible, product]);\n\n  // 当弹窗关闭时清理数据\n  React.useEffect(() => {\n    if (!visible) {\n      setQrCodeData(null);\n    }\n  }, [visible]);\n  return /*#__PURE__*/_jsxDEV(Modal, {\n    title: /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(QrcodeOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"\\u5FAE\\u4FE1\\u5C0F\\u7A0B\\u5E8F\\u4E8C\\u7EF4\\u7801\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 119,\n      columnNumber: 9\n    }, this),\n    open: visible,\n    onCancel: onClose,\n    width: 600,\n    footer: [/*#__PURE__*/_jsxDEV(Button, {\n      onClick: onClose,\n      children: \"\\u5173\\u95ED\"\n    }, \"close\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 17\n      }, this),\n      onClick: regenerateQRCode,\n      disabled: loading,\n      children: \"\\u91CD\\u65B0\\u751F\\u6210\"\n    }, \"regenerate\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      type: \"primary\",\n      icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 17\n      }, this),\n      onClick: downloadQRCode,\n      disabled: !qrCodeData || loading,\n      children: \"\\u4E0B\\u8F7D\\u4E8C\\u7EF4\\u7801\"\n    }, \"download\", false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 9\n    }, this)],\n    children: product && /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(Descriptions, {\n        title: \"\\u5546\\u54C1\\u4FE1\\u606F\",\n        size: \"small\",\n        column: 2,\n        style: {\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n          span: 2,\n          children: product.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u7F16\\u53F7\",\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              code: true,\n              children: product.productNumber\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"link\",\n              size: \"small\",\n              icon: /*#__PURE__*/_jsxDEV(CopyOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 25\n              }, this),\n              onClick: copyProductNumber,\n              children: \"\\u590D\\u5236\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u9152\\u7CBE\\u5EA6\",\n          children: [product.alcoholContent, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u54C1\\u724C\",\n          children: product.brand || \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5BB9\\u91CF\",\n          children: product.volume ? `${product.volume}ml` : \"-\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: \"center\",\n          marginBottom: 24\n        },\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 4,\n          children: \"\\u5C0F\\u7A0B\\u5E8F\\u4E8C\\u7EF4\\u7801\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 13\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: \"60px 0\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u6B63\\u5728\\u751F\\u6210\\u4E8C\\u7EF4\\u7801...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 15\n        }, this), !loading && qrCodeData && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"inline-block\",\n              padding: 16,\n              border: \"1px solid #d9d9d9\",\n              borderRadius: 8,\n              backgroundColor: \"#fafafa\"\n            },\n            children: /*#__PURE__*/_jsxDEV(Image, {\n              src: qrCodeData.qrCodeImage,\n              alt: \"\\u5C0F\\u7A0B\\u5E8F\\u4E8C\\u7EF4\\u7801\",\n              width: 200,\n              height: 200,\n              style: {\n                display: \"block\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 16\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: [\"\\u751F\\u6210\\u65F6\\u95F4:\", \" \", new Date(qrCodeData.generatedAt).toLocaleString()]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 200,\n          columnNumber: 15\n        }, this), !loading && !qrCodeData && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: \"60px 0\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u4E8C\\u7EF4\\u7801\\u751F\\u6210\\u5931\\u8D25\\uFF0C\\u8BF7\\u91CD\\u8BD5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u4F7F\\u7528\\u8BF4\\u660E\",\n        description: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"1. \\u7528\\u6237\\u626B\\u63CF\\u6B64\\u4E8C\\u7EF4\\u7801\\u5C06\\u8DF3\\u8F6C\\u5230\\u5FAE\\u4FE1\\u5C0F\\u7A0B\\u5E8F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"2. \\u5C0F\\u7A0B\\u5E8F\\u4F1A\\u81EA\\u52A8\\u83B7\\u53D6\\u5546\\u54C1\\u7F16\\u53F7\\u5E76\\u663E\\u793A\\u5546\\u54C1\\u8BE6\\u60C5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"3. \\u4E8C\\u7EF4\\u7801\\u5305\\u542B\\u5546\\u54C1\\u7F16\\u53F7: \", product.productNumber]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"4. \\u53EF\\u4EE5\\u4E0B\\u8F7D\\u4E8C\\u7EF4\\u7801\\u7528\\u4E8E\\u6253\\u5370\\u6216\\u5206\\u4EAB\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 15\n        }, this),\n        type: \"info\",\n        showIcon: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 117,\n    columnNumber: 5\n  }, this);\n};\n_s(QRCodeModal, \"/ZX1GJ/SXQHpxvm/vIH+BbQkcd0=\");\n_c = QRCodeModal;\nexport default QRCodeModal;\nvar _c;\n$RefreshReg$(_c, \"QRCodeModal\");", "map": {"version": 3, "names": ["React", "useState", "Modal", "<PERSON><PERSON>", "Space", "Typography", "Descriptions", "message", "Spin", "<PERSON><PERSON>", "Image", "DownloadOutlined", "QrcodeOutlined", "CopyOutlined", "ReloadOutlined", "wechatApi", "jsxDEV", "_jsxDEV", "Title", "Text", "QRCodeModal", "visible", "onClose", "product", "_s", "loading", "setLoading", "qrCodeData", "setQrCodeData", "generateQRCode", "response", "productNumber", "scene", "type", "success", "data", "error", "_error$response", "_error$response$data", "errorMessage", "downloadQRCode", "link", "document", "createElement", "href", "qrCodeImage", "download", "name", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "copyProductNumber", "navigator", "clipboard", "writeText", "then", "catch", "regenerateQRCode", "useEffect", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "open", "onCancel", "width", "footer", "onClick", "icon", "disabled", "size", "column", "style", "marginBottom", "<PERSON><PERSON>", "label", "span", "code", "alcoholContent", "brand", "volume", "textAlign", "level", "padding", "marginTop", "display", "border", "borderRadius", "backgroundColor", "src", "alt", "height", "Date", "generatedAt", "toLocaleString", "description", "showIcon", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/github/7/admin/src/components/QRCodeModal.tsx"], "sourcesContent": ["import React, { useState } from \"react\";\nimport {\n  Modal,\n  Button,\n  Space,\n  Typography,\n  Descriptions,\n  message,\n  Spin,\n  Alert,\n  Image,\n} from \"antd\";\nimport {\n  DownloadOutlined,\n  QrcodeOutlined,\n  CopyOutlined,\n  ReloadOutlined,\n} from \"@ant-design/icons\";\nimport { wechatApi, Product, QRCodeResponse } from \"../services/api\";\n\nconst { Title, Text } = Typography;\n\ninterface QRCodeModalProps {\n  visible: boolean;\n  onClose: () => void;\n  product: Product | null;\n}\n\nconst QRCodeModal: React.FC<QRCodeModalProps> = ({\n  visible,\n  onClose,\n  product,\n}) => {\n  const [loading, setLoading] = useState(false);\n  const [qrCodeData, setQrCodeData] = useState<QRCodeResponse[\"data\"] | null>(\n    null\n  );\n\n  // 生成二维码\n  const generateQRCode = async () => {\n    if (!product) return;\n\n    try {\n      setLoading(true);\n      const response = await wechatApi.generateQRCode({\n        productNumber: product.productNumber,\n        scene: \"admin_generate\",\n        type: \"standard\",\n      });\n\n      if (response.success) {\n        setQrCodeData(response.data);\n        message.success(\"二维码生成成功\");\n      } else {\n        message.error(\"二维码生成失败\");\n      }\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.message || \"生成失败，请重试\";\n      message.error(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 下载二维码\n  const downloadQRCode = () => {\n    if (!qrCodeData) return;\n\n    try {\n      const link = document.createElement(\"a\");\n      link.href = qrCodeData.qrCodeImage;\n      link.download = `${product?.name}_${qrCodeData.productNumber}_wechat_qrcode.png`;\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n      message.success(\"二维码下载成功\");\n    } catch (error) {\n      message.error(\"下载失败\");\n    }\n  };\n\n  // 复制商品编号\n  const copyProductNumber = () => {\n    if (!product) return;\n\n    navigator.clipboard\n      .writeText(product.productNumber)\n      .then(() => {\n        message.success(\"商品编号已复制到剪贴板\");\n      })\n      .catch(() => {\n        message.error(\"复制失败\");\n      });\n  };\n\n  // 重新生成二维码\n  const regenerateQRCode = () => {\n    setQrCodeData(null);\n    generateQRCode();\n  };\n\n  // 当弹窗打开时自动生成二维码\n  React.useEffect(() => {\n    if (visible && product && !qrCodeData) {\n      generateQRCode();\n    }\n  }, [visible, product]);\n\n  // 当弹窗关闭时清理数据\n  React.useEffect(() => {\n    if (!visible) {\n      setQrCodeData(null);\n    }\n  }, [visible]);\n\n  return (\n    <Modal\n      title={\n        <Space>\n          <QrcodeOutlined />\n          <span>微信小程序二维码</span>\n        </Space>\n      }\n      open={visible}\n      onCancel={onClose}\n      width={600}\n      footer={[\n        <Button key=\"close\" onClick={onClose}>\n          关闭\n        </Button>,\n        <Button\n          key=\"regenerate\"\n          icon={<ReloadOutlined />}\n          onClick={regenerateQRCode}\n          disabled={loading}\n        >\n          重新生成\n        </Button>,\n        <Button\n          key=\"download\"\n          type=\"primary\"\n          icon={<DownloadOutlined />}\n          onClick={downloadQRCode}\n          disabled={!qrCodeData || loading}\n        >\n          下载二维码\n        </Button>,\n      ]}\n    >\n      {product && (\n        <div>\n          {/* 商品信息 */}\n          <Descriptions\n            title=\"商品信息\"\n            size=\"small\"\n            column={2}\n            style={{ marginBottom: 24 }}\n          >\n            <Descriptions.Item label=\"商品名称\" span={2}>\n              {product.name}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"商品编号\">\n              <Space>\n                <Text code>{product.productNumber}</Text>\n                <Button\n                  type=\"link\"\n                  size=\"small\"\n                  icon={<CopyOutlined />}\n                  onClick={copyProductNumber}\n                >\n                  复制\n                </Button>\n              </Space>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"酒精度\">\n              {product.alcoholContent}%\n            </Descriptions.Item>\n            <Descriptions.Item label=\"品牌\">\n              {product.brand || \"-\"}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"容量\">\n              {product.volume ? `${product.volume}ml` : \"-\"}\n            </Descriptions.Item>\n          </Descriptions>\n\n          {/* 二维码展示区域 */}\n          <div style={{ textAlign: \"center\", marginBottom: 24 }}>\n            <Title level={4}>小程序二维码</Title>\n\n            {loading && (\n              <div style={{ padding: \"60px 0\" }}>\n                <Spin size=\"large\" />\n                <div style={{ marginTop: 16 }}>\n                  <Text type=\"secondary\">正在生成二维码...</Text>\n                </div>\n              </div>\n            )}\n\n            {!loading && qrCodeData && (\n              <div>\n                <div\n                  style={{\n                    display: \"inline-block\",\n                    padding: 16,\n                    border: \"1px solid #d9d9d9\",\n                    borderRadius: 8,\n                    backgroundColor: \"#fafafa\",\n                  }}\n                >\n                  <Image\n                    src={qrCodeData.qrCodeImage}\n                    alt=\"小程序二维码\"\n                    width={200}\n                    height={200}\n                    style={{ display: \"block\" }}\n                  />\n                </div>\n                <div style={{ marginTop: 16 }}>\n                  <Text type=\"secondary\">\n                    生成时间:{\" \"}\n                    {new Date(qrCodeData.generatedAt).toLocaleString()}\n                  </Text>\n                </div>\n              </div>\n            )}\n\n            {!loading && !qrCodeData && (\n              <div style={{ padding: \"60px 0\" }}>\n                <Text type=\"secondary\">二维码生成失败，请重试</Text>\n              </div>\n            )}\n          </div>\n\n          {/* 使用说明 */}\n          <Alert\n            message=\"使用说明\"\n            description={\n              <div>\n                <p>1. 用户扫描此二维码将跳转到微信小程序</p>\n                <p>2. 小程序会自动获取商品编号并显示商品详情</p>\n                <p>3. 二维码包含商品编号: {product.productNumber}</p>\n                <p>4. 可以下载二维码用于打印或分享</p>\n              </div>\n            }\n            type=\"info\"\n            showIcon\n          />\n        </div>\n      )}\n    </Modal>\n  );\n};\n\nexport default QRCodeModal;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,YAAY,EACZC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,gBAAgB,EAChBC,cAAc,EACdC,YAAY,EACZC,cAAc,QACT,mBAAmB;AAC1B,SAASC,SAAS,QAAiC,iBAAiB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErE,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGd,UAAU;AAQlC,MAAMe,WAAuC,GAAGA,CAAC;EAC/CC,OAAO;EACPC,OAAO;EACPC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAC1C,IACF,CAAC;;EAED;EACA,MAAM4B,cAAc,GAAG,MAAAA,CAAA,KAAY;IACjC,IAAI,CAACN,OAAO,EAAE;IAEd,IAAI;MACFG,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMf,SAAS,CAACc,cAAc,CAAC;QAC9CE,aAAa,EAAER,OAAO,CAACQ,aAAa;QACpCC,KAAK,EAAE,gBAAgB;QACvBC,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAIH,QAAQ,CAACI,OAAO,EAAE;QACpBN,aAAa,CAACE,QAAQ,CAACK,IAAI,CAAC;QAC5B5B,OAAO,CAAC2B,OAAO,CAAC,SAAS,CAAC;MAC5B,CAAC,MAAM;QACL3B,OAAO,CAAC6B,KAAK,CAAC,SAAS,CAAC;MAC1B;IACF,CAAC,CAAC,OAAOA,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAF,eAAA,GAAAD,KAAK,CAACN,QAAQ,cAAAO,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsB/B,OAAO,KAAI,UAAU;MAChEA,OAAO,CAAC6B,KAAK,CAACG,YAAY,CAAC;IAC7B,CAAC,SAAS;MACRb,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMc,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACb,UAAU,EAAE;IAEjB,IAAI;MACF,MAAMc,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACxCF,IAAI,CAACG,IAAI,GAAGjB,UAAU,CAACkB,WAAW;MAClCJ,IAAI,CAACK,QAAQ,GAAG,GAAGvB,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEwB,IAAI,IAAIpB,UAAU,CAACI,aAAa,oBAAoB;MAChFW,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;MAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;MACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC;MAC/BlC,OAAO,CAAC2B,OAAO,CAAC,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACd7B,OAAO,CAAC6B,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAMgB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAAC7B,OAAO,EAAE;IAEd8B,SAAS,CAACC,SAAS,CAChBC,SAAS,CAAChC,OAAO,CAACQ,aAAa,CAAC,CAChCyB,IAAI,CAAC,MAAM;MACVjD,OAAO,CAAC2B,OAAO,CAAC,aAAa,CAAC;IAChC,CAAC,CAAC,CACDuB,KAAK,CAAC,MAAM;MACXlD,OAAO,CAAC6B,KAAK,CAAC,MAAM,CAAC;IACvB,CAAC,CAAC;EACN,CAAC;;EAED;EACA,MAAMsB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B9B,aAAa,CAAC,IAAI,CAAC;IACnBC,cAAc,CAAC,CAAC;EAClB,CAAC;;EAED;EACA7B,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,IAAItC,OAAO,IAAIE,OAAO,IAAI,CAACI,UAAU,EAAE;MACrCE,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACR,OAAO,EAAEE,OAAO,CAAC,CAAC;;EAEtB;EACAvB,KAAK,CAAC2D,SAAS,CAAC,MAAM;IACpB,IAAI,CAACtC,OAAO,EAAE;MACZO,aAAa,CAAC,IAAI,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,OAAO,CAAC,CAAC;EAEb,oBACEJ,OAAA,CAACf,KAAK;IACJ0D,KAAK,eACH3C,OAAA,CAACb,KAAK;MAAAyD,QAAA,gBACJ5C,OAAA,CAACL,cAAc;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBhD,OAAA;QAAA4C,QAAA,EAAM;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CACR;IACDC,IAAI,EAAE7C,OAAQ;IACd8C,QAAQ,EAAE7C,OAAQ;IAClB8C,KAAK,EAAE,GAAI;IACXC,MAAM,EAAE,cACNpD,OAAA,CAACd,MAAM;MAAamE,OAAO,EAAEhD,OAAQ;MAAAuC,QAAA,EAAC;IAEtC,GAFY,OAAO;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEX,CAAC,eACThD,OAAA,CAACd,MAAM;MAELoE,IAAI,eAAEtD,OAAA,CAACH,cAAc;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzBK,OAAO,EAAEZ,gBAAiB;MAC1Bc,QAAQ,EAAE/C,OAAQ;MAAAoC,QAAA,EACnB;IAED,GANM,YAAY;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMV,CAAC,eACThD,OAAA,CAACd,MAAM;MAEL8B,IAAI,EAAC,SAAS;MACdsC,IAAI,eAAEtD,OAAA,CAACN,gBAAgB;QAAAmD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC3BK,OAAO,EAAE9B,cAAe;MACxBgC,QAAQ,EAAE,CAAC7C,UAAU,IAAIF,OAAQ;MAAAoC,QAAA,EAClC;IAED,GAPM,UAAU;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAOR,CAAC,CACT;IAAAJ,QAAA,EAEDtC,OAAO,iBACNN,OAAA;MAAA4C,QAAA,gBAEE5C,OAAA,CAACX,YAAY;QACXsD,KAAK,EAAC,0BAAM;QACZa,IAAI,EAAC,OAAO;QACZC,MAAM,EAAE,CAAE;QACVC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAG,CAAE;QAAAf,QAAA,gBAE5B5C,OAAA,CAACX,YAAY,CAACuE,IAAI;UAACC,KAAK,EAAC,0BAAM;UAACC,IAAI,EAAE,CAAE;UAAAlB,QAAA,EACrCtC,OAAO,CAACwB;QAAI;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACpBhD,OAAA,CAACX,YAAY,CAACuE,IAAI;UAACC,KAAK,EAAC,0BAAM;UAAAjB,QAAA,eAC7B5C,OAAA,CAACb,KAAK;YAAAyD,QAAA,gBACJ5C,OAAA,CAACE,IAAI;cAAC6D,IAAI;cAAAnB,QAAA,EAAEtC,OAAO,CAACQ;YAAa;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACzChD,OAAA,CAACd,MAAM;cACL8B,IAAI,EAAC,MAAM;cACXwC,IAAI,EAAC,OAAO;cACZF,IAAI,eAAEtD,OAAA,CAACJ,YAAY;gBAAAiD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBK,OAAO,EAAElB,iBAAkB;cAAAS,QAAA,EAC5B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC,eACpBhD,OAAA,CAACX,YAAY,CAACuE,IAAI;UAACC,KAAK,EAAC,oBAAK;UAAAjB,QAAA,GAC3BtC,OAAO,CAAC0D,cAAc,EAAC,GAC1B;QAAA;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAmB,CAAC,eACpBhD,OAAA,CAACX,YAAY,CAACuE,IAAI;UAACC,KAAK,EAAC,cAAI;UAAAjB,QAAA,EAC1BtC,OAAO,CAAC2D,KAAK,IAAI;QAAG;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC,eACpBhD,OAAA,CAACX,YAAY,CAACuE,IAAI;UAACC,KAAK,EAAC,cAAI;UAAAjB,QAAA,EAC1BtC,OAAO,CAAC4D,MAAM,GAAG,GAAG5D,OAAO,CAAC4D,MAAM,IAAI,GAAG;QAAG;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGfhD,OAAA;QAAK0D,KAAK,EAAE;UAAES,SAAS,EAAE,QAAQ;UAAER,YAAY,EAAE;QAAG,CAAE;QAAAf,QAAA,gBACpD5C,OAAA,CAACC,KAAK;UAACmE,KAAK,EAAE,CAAE;UAAAxB,QAAA,EAAC;QAAM;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,EAE9BxC,OAAO,iBACNR,OAAA;UAAK0D,KAAK,EAAE;YAAEW,OAAO,EAAE;UAAS,CAAE;UAAAzB,QAAA,gBAChC5C,OAAA,CAACT,IAAI;YAACiE,IAAI,EAAC;UAAO;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrBhD,OAAA;YAAK0D,KAAK,EAAE;cAAEY,SAAS,EAAE;YAAG,CAAE;YAAA1B,QAAA,eAC5B5C,OAAA,CAACE,IAAI;cAACc,IAAI,EAAC,WAAW;cAAA4B,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAACxC,OAAO,IAAIE,UAAU,iBACrBV,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YACE0D,KAAK,EAAE;cACLa,OAAO,EAAE,cAAc;cACvBF,OAAO,EAAE,EAAE;cACXG,MAAM,EAAE,mBAAmB;cAC3BC,YAAY,EAAE,CAAC;cACfC,eAAe,EAAE;YACnB,CAAE;YAAA9B,QAAA,eAEF5C,OAAA,CAACP,KAAK;cACJkF,GAAG,EAAEjE,UAAU,CAACkB,WAAY;cAC5BgD,GAAG,EAAC,sCAAQ;cACZzB,KAAK,EAAE,GAAI;cACX0B,MAAM,EAAE,GAAI;cACZnB,KAAK,EAAE;gBAAEa,OAAO,EAAE;cAAQ;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhD,OAAA;YAAK0D,KAAK,EAAE;cAAEY,SAAS,EAAE;YAAG,CAAE;YAAA1B,QAAA,eAC5B5C,OAAA,CAACE,IAAI;cAACc,IAAI,EAAC,WAAW;cAAA4B,QAAA,GAAC,2BAChB,EAAC,GAAG,EACR,IAAIkC,IAAI,CAACpE,UAAU,CAACqE,WAAW,CAAC,CAACC,cAAc,CAAC,CAAC;YAAA;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAEA,CAACxC,OAAO,IAAI,CAACE,UAAU,iBACtBV,OAAA;UAAK0D,KAAK,EAAE;YAAEW,OAAO,EAAE;UAAS,CAAE;UAAAzB,QAAA,eAChC5C,OAAA,CAACE,IAAI;YAACc,IAAI,EAAC,WAAW;YAAA4B,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhD,OAAA,CAACR,KAAK;QACJF,OAAO,EAAC,0BAAM;QACd2F,WAAW,eACTjF,OAAA;UAAA4C,QAAA,gBACE5C,OAAA;YAAA4C,QAAA,EAAG;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC3BhD,OAAA;YAAA4C,QAAA,EAAG;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAC7BhD,OAAA;YAAA4C,QAAA,GAAG,6DAAc,EAACtC,OAAO,CAACQ,aAAa;UAAA;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5ChD,OAAA;YAAA4C,QAAA,EAAG;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CACN;QACDhC,IAAI,EAAC,MAAM;QACXkE,QAAQ;MAAA;QAAArC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACT,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EACN;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAACzC,EAAA,CA/NIJ,WAAuC;AAAAgF,EAAA,GAAvChF,WAAuC;AAiO7C,eAAeA,WAAW;AAAC,IAAAgF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}