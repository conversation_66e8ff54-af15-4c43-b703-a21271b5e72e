'use strict';

Object.defineProperty(exports, '__esModule', {
  value: true
});
Object.defineProperty(exports, 'addResult', {
  enumerable: true,
  get: function () {
    return _helpers.addResult;
  }
});
Object.defineProperty(exports, 'buildFailureTestResult', {
  enumerable: true,
  get: function () {
    return _helpers.buildFailureTestResult;
  }
});
Object.defineProperty(exports, 'createEmptyTestResult', {
  enumerable: true,
  get: function () {
    return _helpers.createEmptyTestResult;
  }
});
Object.defineProperty(exports, 'formatTestResults', {
  enumerable: true,
  get: function () {
    return _formatTestResults.default;
  }
});
Object.defineProperty(exports, 'makeEmptyAggregatedTestResult', {
  enumerable: true,
  get: function () {
    return _helpers.makeEmptyAggregatedTestResult;
  }
});

var _formatTestResults = _interopRequireDefault(require('./formatTestResults'));

var _helpers = require('./helpers');

function _interopRequireDefault(obj) {
  return obj && obj.__esModule ? obj : {default: obj};
}
