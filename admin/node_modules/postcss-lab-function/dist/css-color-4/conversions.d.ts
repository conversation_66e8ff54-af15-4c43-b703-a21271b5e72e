/**
 * @license W3C
 * https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 *
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */
declare type color = [number, number, number];
export declare const D50: number[];
export declare const D65: number[];
export declare function lin_sRGB(RGB: color): color;
export declare function gam_sRGB(RGB: color): color;
export declare function lin_sRGB_to_XYZ(rgb: color): color;
export declare function XYZ_to_lin_sRGB(XYZ: color): color;
export declare function lin_P3(RGB: color): color;
export declare function gam_P3(RGB: color): color;
export declare function lin_P3_to_XYZ(rgb: color): color;
export declare function XYZ_to_lin_P3(XYZ: color): color;
export declare function lin_ProPhoto(RGB: color): color;
export declare function gam_ProPhoto(RGB: color): color;
export declare function lin_ProPhoto_to_XYZ(rgb: color): color;
export declare function XYZ_to_lin_ProPhoto(XYZ: color): color;
export declare function lin_a98rgb(RGB: color): color;
export declare function gam_a98rgb(RGB: color): color;
export declare function lin_a98rgb_to_XYZ(rgb: color): color;
export declare function XYZ_to_lin_a98rgb(XYZ: color): color;
export declare function lin_2020(RGB: color): color;
export declare function gam_2020(RGB: color): color;
export declare function lin_2020_to_XYZ(rgb: color): color;
export declare function XYZ_to_lin_2020(XYZ: color): color;
export declare function D65_to_D50(XYZ: color): color;
export declare function D50_to_D65(XYZ: color): color;
export declare function XYZ_to_Lab(XYZ: color): color;
export declare function Lab_to_XYZ(Lab: color): color;
export declare function Lab_to_LCH(Lab: color): color;
export declare function LCH_to_Lab(LCH: color): color;
export declare function XYZ_to_OKLab(XYZ: color): color;
export declare function OKLab_to_XYZ(OKLab: color): color;
export declare function OKLab_to_OKLCH(OKLab: color): color;
export declare function OKLCH_to_OKLab(OKLCH: color): color;
export declare function rectangular_premultiply(color: color, alpha: number): color;
export declare function rectangular_un_premultiply(color: color, alpha: number): color;
export declare function polar_premultiply(color: color, alpha: number, hueIndex: number): color;
export declare function polar_un_premultiply(color: color, alpha: number, hueIndex: number): color;
export declare function hsl_premultiply(color: color, alpha: number): color;
export {};
