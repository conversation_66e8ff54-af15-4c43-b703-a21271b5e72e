{"name": "leven", "version": "3.1.0", "description": "Measure the difference between two strings using the fastest JS implementation of the Levenshtein distance algorithm", "license": "MIT", "repository": "sindresorhus/leven", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd", "bench": "matcha bench.js"}, "files": ["index.js", "index.d.ts"], "keywords": ["leven", "<PERSON><PERSON><PERSON><PERSON>", "distance", "algorithm", "algo", "string", "difference", "diff", "fast", "fuzzy", "similar", "similarity", "compare", "comparison", "edit", "text", "match", "matching"], "devDependencies": {"ava": "^1.4.1", "fast-levenshtein": "^2.0.6", "ld": "^0.1.0", "levdist": "^2.2.9", "levenshtein": "^1.0.5", "levenshtein-component": "^0.0.1", "levenshtein-edit-distance": "^2.0.3", "matcha": "^0.7.0", "natural": "^0.6.3", "talisman": "^0.21.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}