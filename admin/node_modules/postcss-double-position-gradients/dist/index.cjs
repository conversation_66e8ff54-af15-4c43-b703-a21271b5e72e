"use strict";var e=require("@csstools/postcss-progressive-custom-properties"),t=require("postcss-value-parser");function r(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var n=r(e),s=r(t);function i(e){return e.includes("conic-gradient(")||e.includes("linear-gradient(")||e.includes("radial-gradient(")||e.includes("repeating-conic-gradient(")||e.includes("repeating-linear-gradient(")||e.includes("repeating-radial-gradient(")}const a=["at","bottom","center","circle","closest-corner","closest-side","ellipse","farthest-corner","farthest-side","from","in","left","right","to","top"],o=e=>"div"===e.type&&","===e.value;function l(e){try{return!1!==s.default.unit(null==e?void 0:e.value)}catch(e){return!1}}const c=e=>({postcssPlugin:"postcss-double-position-gradients",Declaration(t,{result:r}){if(!i(t.value.toLowerCase()))return;if(function(e){let t=e.parent;for(;t;)if("atrule"===t.type){if("supports"===t.name.toLowerCase()&&i(t.params.toLowerCase()))return!0;t=t.parent}else t=t.parent;return!1}(t))return;let n;try{n=s.default(t.value)}catch(e){t.warn(r,`Failed to parse value '${t.value}' as a CSS gradient. Leaving the original value intact.`)}if(void 0===n)return;n.walk((e=>{if("function"!==e.type||"conic-gradient"!==(t=e.value.toLowerCase())&&"linear-gradient"!==t&&"radial-gradient"!==t&&"repeating-conic-gradient"!==t&&"repeating-linear-gradient"!==t&&"repeating-radial-gradient"!==t)return;var t;const r=e.nodes.filter((e=>"comment"!==e.type&&"space"!==e.type));let n=!1;r.forEach(((t,r,s)=>{if("word"===t.type&&a.includes(t.value.toLowerCase())&&(n=!0),"div"===t.type&&","===t.value&&(n=!1),n)return;const i=Object(s[r-1]),c=Object(s[r-2]),u=Object(s[r+1]);if(c.type&&l(i)&&l(t)){const r=c,n={type:"div",value:",",before:o(u)?u.before:"",after:o(u)?"":" "};e.nodes.splice(e.nodes.indexOf(t)-1,0,n,r)}}))}));const c=n.toString();c!==t.value&&(t.cloneBefore({value:c}),e.preserve||t.remove())}});c.postcss=!0;const u=e=>{const t=Object.assign({enableProgressiveCustomProperties:!0,preserve:!0},e);return t.enableProgressiveCustomProperties&&t.preserve?{postcssPlugin:"postcss-double-position-gradients",plugins:[n.default(),c(t)]}:c(t)};u.postcss=!0,module.exports=u;
